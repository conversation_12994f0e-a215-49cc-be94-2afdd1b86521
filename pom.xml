<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.1.RELEASE</version>
    </parent>

    <groupId>com.integralN</groupId>
    <artifactId>concise</artifactId>
    <version>1.6.0</version>

    <name>concise</name>
    <description>concise</description>

    <packaging>pom</packaging>

    <modules>
        <module>concise-base</module>
        <module>concise-manage</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <mysql-connector-java.version>8.0.17</mysql-connector-java.version>
        <oracle.version>********</oracle.version>
        <mssql.version>9.3.0.jre8-preview</mssql.version>
        <postgresql.version>42.2.19</postgresql.version>
        <druid.version>1.1.21</druid.version>
        <mp.version>3.4.2</mp.version>
        <fastjson.version>1.2.83</fastjson.version>
        <jwt.version>0.9.1</jwt.version>
        <hutool.version>5.5.8</hutool.version>
        <lombok.versin>1.18.12</lombok.versin>
        <easypoi.version>4.2.0</easypoi.version>
        <jodconverter.version>4.2.0</jodconverter.version>
        <libreoffice.version>6.4.3</libreoffice.version>
        <justauth.version>1.15.6</justauth.version>
        <aliyun.oss.version>3.8.0</aliyun.oss.version>
        <qcloud.oss.version>5.6.23</qcloud.oss.version>
        <aliyun.sms.sdk.version>4.4.6</aliyun.sms.sdk.version>
        <aliyun.sms.esc.version>4.17.6</aliyun.sms.esc.version>
        <qcloud.sms.sdk.version>3.1.57</qcloud.sms.sdk.version>
        <knife4j.version>2.0.8</knife4j.version>
    </properties>

    <dependencyManagement>

        <dependencies>

            <!--mybatis-plus-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mp.version}</version>
            </dependency>

            <!-- 数据库驱动,可根据自己需要自行删减，默认使用mysql -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>

            <!-- oracle -->
            <!--<dependency>
                <groupId>com.oracle</groupId>
                <artifactId>ojdbc6</artifactId>
                <version>${oracle.version}</version>
            </dependency>-->

            <!-- mssql -->
           <!-- <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${mssql.version}</version>
            </dependency>-->

            <!-- postgresql -->
           <!-- <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>-->

            <!--数据库连接池-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!--jwt token-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!--fastjson-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!--hutool-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!--lombok-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.versin}</version>
            </dependency>

            <!-- swagger接口文档 -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!--easypoi导入导出-->
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-base</artifactId>
                <version>${easypoi.version}</version>
            </dependency>

            <!--libreoffice文档在线预览-->
            <dependency>
                <groupId>org.jodconverter</groupId>
                <artifactId>jodconverter-core</artifactId>
                <version>${jodconverter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jodconverter</groupId>
                <artifactId>jodconverter-local</artifactId>
                <version>${jodconverter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jodconverter</groupId>
                <artifactId>jodconverter-spring-boot-starter</artifactId>
                <version>${jodconverter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.libreoffice</groupId>
                <artifactId>ridl</artifactId>
                <version>${libreoffice.version}</version>
            </dependency>

            <!--justauth第三方登录-->
            <dependency>
                <groupId>me.zhyd.oauth</groupId>
                <artifactId>JustAuth</artifactId>
                <version>${justauth.version}</version>
            </dependency>

            <!--阿里云上传文件客户端，用的时候手动引入-->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun.oss.version}</version>
            </dependency>

            <!--腾讯云上传文件客户端，用的时候手动引入-->
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${qcloud.oss.version}</version>
            </dependency>

            <!--阿里云短信发送的sdk-->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun.sms.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-ecs</artifactId>
                <version>${aliyun.sms.esc.version}</version>
            </dependency>

            <!--腾讯云短信sdk-->
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>${qcloud.sms.sdk.version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <delimiters>
                        <delimiter>@</delimiter>
                    </delimiters>
                    <useDefaultDelimiters>false</useDefaultDelimiters>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.0</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                <argLine>-Dfile.encoding=UTF-8 ${jacocoArgLine}</argLine>
                <testFailureIgnore>true</testFailureIgnore>
                </configuration>
                <version>2.22.1</version>
            </plugin>
            <!--jacoco 统计自动化代码覆盖率-->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.6</version>
                <configuration>
                <skip>false</skip>
                <includes></includes>
                </configuration>
                <executions>
                <execution>
                    <id>default-prepare-agent</id>
                    <goals>
                    <goal>prepare-agent</goal>
                    </goals>
                    <configuration>
                    <propertyName>jacocoArgLine</propertyName>
                    </configuration>
                </execution>
                <execution>
                    <id>default-report</id>
                    <phase>prepare-package</phase>
                    <goals>
                    <goal>report</goal>
                    </goals>
                </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/webapp</directory>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <spring.active>local</spring.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <spring.active>dev</spring.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <spring.active>prod</spring.active>
            </properties>
        </profile>
    </profiles>

</project>
