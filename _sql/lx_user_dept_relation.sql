-- ----------------------------
-- 浙政钉用户机构关系表
-- ----------------------------
DROP TABLE IF EXISTS `lx_user_dept_relation`;
CREATE TABLE `lx_user_dept_relation` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `user_id` varchar(50) NOT NULL COMMENT '用户ID（浙政钉employeeCode）',
  `user_dept_id` varchar(100) DEFAULT NULL COMMENT '用户关联的部门ID',
  `user_dept_name` varchar(200) DEFAULT NULL COMMENT '用户关联的部门名称',
  `position` varchar(100) DEFAULT NULL COMMENT '职务',
  `rank` varchar(100) DEFAULT NULL COMMENT '职级',
  `work_address` varchar(500) DEFAULT NULL COMMENT '办公地址',
  `work_phone` varchar(50) DEFAULT NULL COMMENT '办公电话',
  `jzjg` varchar(100) DEFAULT NULL COMMENT '矫正机构id',
  `jzjg_name` varchar(200) DEFAULT NULL COMMENT '矫正机构名称',
  `is_main` tinyint(1) DEFAULT 0 COMMENT '是否主要部门（0:否 1:是）',
  `sort_order` int(3) DEFAULT 0 COMMENT '排序顺序',
  `status` int(1) DEFAULT 1 COMMENT '状态（0:无效 1:有效）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_user_dept_id` (`user_dept_id`),
  KEY `idx_jzjg` (`jzjg`),
  KEY `idx_is_main` (`is_main`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='浙政钉用户机构关系表';
