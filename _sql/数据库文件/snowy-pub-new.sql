/*
 Navicat Premium Data Transfer

 Source Server         : 腾讯*************
 Source Server Type    : MySQL
 Source Server Version : 50737
 Source Host           : *************:3306
 Source Schema         : snowy

 Target Server Type    : MySQL
 Target Server Version : 50737
 File Encoding         : 65001

 Date: 11/02/2022 14:11:18
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_app
-- ----------------------------
DROP TABLE IF EXISTS `sys_app`;
CREATE TABLE `sys_app`  (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
  `active` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否默认激活（Y-是，N-否）',
  `status` tinyint(4) NOT NULL COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统应用表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_app
-- ----------------------------
INSERT INTO `sys_app` VALUES (1265476890672672821, '系统应用', 'system', 'Y', 0, '2020-03-25 19:07:00', 1265476890672672808, '2020-08-15 15:23:05', 1280709549107552257);
INSERT INTO `sys_app` VALUES (1265476890672672822, '业务应用', 'business', 'N', 2, '2020-03-26 08:40:33', 1265476890672672808, '2020-09-23 22:00:01', 1265476890672672808);
INSERT INTO `sys_app` VALUES (1342445032647098369, '系统工具', 'system_tool', 'N', 0, '2020-12-25 20:20:12', 1265476890672672808, NULL, NULL);

-- ----------------------------
-- Table structure for sys_area
-- ----------------------------
DROP TABLE IF EXISTS `sys_area`;
CREATE TABLE `sys_area`  (
  `id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `level_code` tinyint(3) UNSIGNED NULL DEFAULT NULL COMMENT '层级',
  `parent_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '父级行政代码',
  `area_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '行政代码',
  `zip_code` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '邮政编码',
  `city_code` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '区号',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '名称',
  `short_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '简称',
  `merger_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '组合名',
  `pinyin` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '拼音',
  `lng` decimal(10, 6) NULL DEFAULT NULL COMMENT '经度',
  `lat` decimal(10, 6) NULL DEFAULT NULL COMMENT '纬度',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`area_code`) USING BTREE,
  INDEX `idx_parent_code`(`parent_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '中国行政地区表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_area
-- ----------------------------

-- ----------------------------
-- Table structure for sys_code_generate
-- ----------------------------
DROP TABLE IF EXISTS `sys_code_generate`;
CREATE TABLE `sys_code_generate`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `author_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '作者姓名',
  `class_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类名',
  `table_prefix` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否移除表前缀',
  `generate_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '生成位置类型',
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据库表名',
  `package_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包名称',
  `bus_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务名',
  `table_comment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '功能名',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成基础配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_code_generate
-- ----------------------------

-- ----------------------------
-- Table structure for sys_code_generate_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_code_generate_config`;
CREATE TABLE `sys_code_generate_config`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `code_gen_id` bigint(20) NULL DEFAULT NULL COMMENT '代码生成主表ID',
  `column_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据库字段名',
  `java_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'java类字段名',
  `data_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物理类型',
  `column_comment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段描述',
  `java_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'java类型',
  `effect_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作用类型（字典）',
  `dict_type_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典code',
  `whether_table` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列表展示',
  `whether_add_update` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '增改',
  `whether_retract` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列表是否缩进（字典）',
  `whether_required` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否必填（字典）',
  `query_whether` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否是查询条件',
  `query_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '查询方式',
  `column_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主键',
  `column_key_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主外键名称',
  `whether_common` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否是通用字段',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成详细配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_code_generate_config
-- ----------------------------

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '值',
  `sys_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否是系统参数（Y-是，N-否）',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `status` tinyint(4) NOT NULL COMMENT '状态（字典 0正常 1停用 2删除）',
  `group_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '常量所属分类的编码，来自于“常量的分类”字典',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统参数配置表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1265117443880853506, 'jwt密钥', 'SNOWY_JWT_SECRET', 'snowy', 'Y', '（重要）jwt密钥，默认为空，自行设置', 0, 'DEFAULT', '2020-05-26 06:35:19', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1265117443880853507, '默认密码', 'SNOWY_DEFAULT_PASSWORD', '123456', 'Y', '默认密码', 0, 'DEFAULT', '2020-05-26 06:37:56', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1265117443880853508, 'token过期时间', 'SNOWY_TOKEN_EXPIRE', '86400', 'Y', 'token过期时间（单位：秒）', 0, 'DEFAULT', '2020-05-27 11:54:49', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1265117443880853509, 'session会话过期时间', 'SNOWY_SESSION_EXPIRE', '7200', 'Y', 'session会话过期时间（单位：秒）', 0, 'DEFAULT', '2020-05-27 11:54:49', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1265117443880853519, '阿里云短信keyId', 'SNOWY_ALIYUN_SMS_ACCESSKEY_ID', '你的keyId', 'Y', '阿里云短信keyId', 0, 'ALIYUN_SMS', '2020-06-07 16:27:11', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1269547042242371585, '阿里云短信secret', 'SNOWY_ALIYUN_SMS_ACCESSKEY_SECRET', '你的secret', 'Y', '阿里云短信secret', 0, 'ALIYUN_SMS', '2020-06-07 16:29:37', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1269547130041737217, '阿里云短信签名', 'SNOWY_ALIYUN_SMS_SIGN_NAME', 'Concise快速开发平台', 'Y', '阿里云短信签名', 0, 'ALIYUN_SMS', '2020-06-07 16:29:58', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1269547279530926081, '阿里云短信-登录模板号', 'SNOWY_ALIYUN_SMS_LOGIN_TEMPLATE_CODE', 'SMS_1877123456', 'Y', '阿里云短信-登录模板号', 0, 'ALIYUN_SMS', '2020-06-07 16:30:33', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1269547410879750145, '阿里云短信默认失效时间', 'SNOWY_ALIYUN_SMS_INVALIDATE_MINUTES', '5', 'Y', '阿里云短信默认失效时间（单位：分钟）', 0, 'ALIYUN_SMS', '2020-06-07 16:31:04', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1269575927357071361, '腾讯云短信secretId', 'SNOWY_TENCENT_SMS_SECRET_ID', '你的secretId', 'Y', '腾讯云短信secretId', 0, 'TENCENT_SMS', '2020-06-07 18:24:23', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1269575991693500418, '腾讯云短信secretKey', 'SNOWY_TENCENT_SMS_SECRET_KEY', '你的secretkey', 'Y', '腾讯云短信secretKey', 0, 'TENCENT_SMS', '2020-06-07 18:24:39', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1269576044084551682, '腾讯云短信sdkAppId', 'SNOWY_TENCENT_SMS_SDK_APP_ID', '1400375123', 'Y', '腾讯云短信sdkAppId', 0, 'TENCENT_SMS', '2020-06-07 18:24:51', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1269576089294954497, '腾讯云短信签名', 'SNOWY_TENCENT_SMS_SIGN', 'Concise快速开发平台', 'Y', '腾讯云短信签名', 0, 'TENCENT_SMS', '2020-06-07 18:25:02', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1270378172860403713, '邮箱host', 'SNOWY_EMAIL_HOST', 'smtp.126.com', 'Y', '邮箱host', 0, 'EMAIL', '2020-06-09 23:32:14', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1270378295543795714, '邮箱用户名', 'SNOWY_EMAIL_USERNAME', '<EMAIL>', 'Y', '邮箱用户名', 0, 'EMAIL', '2020-06-09 23:32:43', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1270378340510928897, '邮箱密码', 'SNOWY_EMAIL_PASSWORD', '你的邮箱密码', 'Y', '邮箱密码', 0, 'EMAIL', '2020-06-09 23:32:54', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1270378527358783489, '邮箱端口', 'SNOWY_EMAIL_PORT', '465', 'Y', '邮箱端口', 0, 'EMAIL', '2020-06-09 23:33:38', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1270378790035460097, '邮箱是否开启ssl', 'SNOWY_EMAIL_SSL', 'true', 'Y', '邮箱是否开启ssl', 0, 'EMAIL', '2020-06-09 23:34:41', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1270380786649972737, '邮箱发件人', 'SNOWY_EMAIL_FROM', '<EMAIL>', 'Y', '邮箱发件人', 0, 'EMAIL', '2020-06-09 23:42:37', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1270380786649972738, 'win本地上传文件路径', 'SNOWY_FILE_UPLOAD_PATH_FOR_WINDOWS', 'd:/tmp', 'Y', 'win本地上传文件路径', 0, 'FILE_PATH', '2020-06-09 23:42:37', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1270380786649972739, 'linux/mac本地上传文件路径', 'SNOWY_FILE_UPLOAD_PATH_FOR_LINUX', '/tmp', 'Y', 'linux/mac本地上传文件路径', 0, 'FILE_PATH', '2020-06-09 23:42:37', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1270380786649982740, 'Concise演示环境', 'SNOWY_DEMO_ENV_FLAG', 'false', 'Y', 'Concise演示环境的开关，true-打开，false-关闭，如果演示环境开启，则只能读数据不能写数据', 0, 'DEFAULT', '2020-06-09 23:42:37', 1265476890672672808, '2020-09-03 14:38:17', 1265476890672672808);
INSERT INTO `sys_config` VALUES (1270380786649982741, 'Concise放开XSS过滤的接口', 'SNOWY_UN_XSS_FILTER_URL', '/demo/xssfilter,/demo/unxss', 'Y', '多个url可以用英文逗号隔开', 0, 'DEFAULT', '2020-06-09 23:42:37', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1270380786649982742, '单用户登陆的开关', 'SNOWY_ENABLE_SINGLE_LOGIN', 'false', 'Y', '单用户登陆的开关，true-打开，false-关闭，如果一个人登录两次，就会将上一次登陆挤下去', 0, 'DEFAULT', '2020-06-09 23:42:37', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1270380786649982743, '登录验证码的开关', 'SNOWY_CAPTCHA_OPEN', 'true', 'Y', '登录验证码的开关，true-打开，false-关闭', 0, 'DEFAULT', '2020-06-09 23:42:37', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1280694281648070659, '阿里云定位api接口地址', 'SNOWY_IP_GEO_API', 'http://api01.aliyun.venuscn.com/ip?ip=%s', 'Y', '阿里云定位api接口地址', 0, 'DEFAULT', '2020-07-20 10:44:46', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1280694281648070660, '阿里云定位appCode', 'SNOWY_IP_GEO_APP_CODE', '461535aabeae4f34861884d392f5d452', 'Y', '阿里云定位appCode', 0, 'DEFAULT', '2020-07-20 10:44:46', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1288309751255412737, 'Oauth用户登录的开关', 'SNOWY_ENABLE_OAUTH_LOGIN', 'true', 'Y', 'Oauth用户登录的开关', 0, 'OAUTH', '2020-07-29 11:05:55', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1288310043346743297, 'Oauth码云登录ClientId', 'SNOWY_OAUTH_GITEE_CLIENT_ID', '你的clientId', 'Y', 'Oauth码云登录ClientId', 0, 'OAUTH', '2020-07-29 11:07:05', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1288310157876408321, 'Oauth码云登录ClientSecret', 'SNOWY_OAUTH_GITEE_CLIENT_SECRET', '你的clientSecret', 'Y', 'Oauth码云登录ClientSecret', 0, 'OAUTH', '2020-07-29 11:07:32', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_config` VALUES (1288310280056483841, 'Oauth码云登录回调地址', 'SNOWY_OAUTH_GITEE_REDIRECT_URI', 'http://localhost:83/oauth/callback/gitee', 'Y', 'Oauth码云登录回调地址', 0, 'OAUTH', '2020-07-29 11:08:01', 1265476890672672808, NULL, NULL);

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `type_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典类型id',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '值',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
  `sort` int(11) NOT NULL COMMENT '排序',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `status` tinyint(4) NOT NULL COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统字典值表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES ('1265216536659087357', '1265216211667636234', '男', '1', 100, '男性', 0, '2020-04-01 10:23:29', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265216536659087358', '1265216211667636234', '女', '2', 100, '女性', 0, '2020-04-01 10:23:49', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265216536659087359', '1265216211667636234', '未知', '3', 100, '未知性别', 0, '2020-04-01 10:24:01', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265216536659087361', '1265216211667636235', '默认常量', 'DEFAULT', 100, '默认常量，都以SNOWY_开头的', 0, '2020-04-14 23:25:45', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265216536659087363', '1265216211667636235', '阿里云短信', 'ALIYUN_SMS', 100, '阿里云短信配置', 0, '2020-04-14 23:25:45', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265216536659087364', '1265216211667636235', '腾讯云短信', 'TENCENT_SMS', 100, '腾讯云短信', 0, '2020-04-14 23:25:45', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265216536659087365', '1265216211667636235', '邮件配置', 'EMAIL', 100, '邮箱配置', 0, '2020-04-14 23:25:45', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265216536659087366', '1265216211667636235', '文件上传路径', 'FILE_PATH', 100, '文件上传路径', 0, '2020-04-14 23:25:45', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265216536659087367', '1265216211667636235', 'Oauth配置', 'OAUTH', 100, 'Oauth配置', 0, '2020-04-14 23:25:45', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265216617500102656', '1265216211667636226', '正常', '0', 100, '正常', 0, '2020-05-26 17:41:44', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265216617500102657', '1265216211667636226', '停用', '1', 100, '停用', 0, '2020-05-26 17:42:03', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265216938389524482', '1265216211667636226', '删除', '2', 100, '删除', 0, '2020-05-26 17:43:19', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265217669028892673', '1265217074079453185', '否', 'N', 100, '否', 0, '2020-05-26 17:46:14', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265217706584690689', '1265217074079453185', '是', 'Y', 100, '是', 0, '2020-05-26 17:46:23', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265220776437731330', '1265217846770913282', '登录', '1', 100, '登录', 0, '2020-05-26 17:58:34', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265220806070489090', '1265217846770913282', '登出', '2', 100, '登出', 0, '2020-05-26 17:58:41', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265221129564573697', '1265221049302372354', '目录', '0', 100, '目录', 0, '2020-05-26 17:59:59', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265221163119005697', '1265221049302372354', '菜单', '1', 100, '菜单', 0, '2020-05-26 18:00:07', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265221188091891713', '1265221049302372354', '按钮', '2', 100, '按钮', 0, '2020-05-26 18:00:13', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265466389204967426', '1265466149622128641', '未发送', '0', 100, '未发送', 0, '2020-05-27 10:14:33', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265466432670539778', '1265466149622128641', '发送成功', '1', 100, '发送成功', 0, '2020-05-27 10:14:43', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265466486097584130', '1265466149622128641', '发送失败', '2', 100, '发送失败', 0, '2020-05-27 10:14:56', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265466530477514754', '1265466149622128641', '失效', '3', 100, '失效', 0, '2020-05-27 10:15:07', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265466835009150978', '1265466752209395713', '无', '0', 100, '无', 0, '2020-05-27 10:16:19', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265466874758569986', '1265466752209395713', '组件', '1', 100, '组件', 0, '2020-05-27 10:16:29', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265466925476093953', '1265466752209395713', '内链', '2', 100, '内链', 0, '2020-05-27 10:16:41', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265466962209808385', '1265466752209395713', '外链', '3', 100, '外链', 0, '2020-05-27 10:16:50', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265467428423475202', '1265467337566461954', '系统权重', '1', 100, '系统权重', 0, '2020-05-27 10:18:41', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265467503090475009', '1265467337566461954', '业务权重', '2', 100, '业务权重', 0, '2020-05-27 10:18:59', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265468138431062018', '1265468028632571905', '全部数据', '1', 100, '全部数据', 0, '2020-05-27 10:21:30', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265468194928336897', '1265468028632571905', '本部门及以下数据', '2', 100, '本部门及以下数据', 0, '2020-05-27 10:21:44', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265468241992622082', '1265468028632571905', '本部门数据', '3', 100, '本部门数据', 0, '2020-05-27 10:21:55', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265468273634451457', '1265468028632571905', '仅本人数据', '4', 100, '仅本人数据', 0, '2020-05-27 10:22:02', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265468302046666753', '1265468028632571905', '自定义数据', '5', 100, '自定义数据', 0, '2020-05-27 10:22:09', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265468508100239362', '1265468437904367618', 'app', '1', 100, 'app', 0, '2020-05-27 10:22:58', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265468543433056258', '1265468437904367618', 'pc', '2', 100, 'pc', 0, '2020-05-27 10:23:07', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1265468576874242050', '1265468437904367618', '其他', '3', 100, '其他', 0, '2020-05-27 10:23:15', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1275617233011335170', '1275617093517172738', '其它', '0', 100, '其它', 0, '2020-06-24 10:30:23', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1275617295355469826', '1275617093517172738', '增加', '1', 100, '增加', 0, '2020-06-24 10:30:38', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1275617348610547714', '1275617093517172738', '删除', '2', 100, '删除', 0, '2020-06-24 10:30:50', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1275617395515449346', '1275617093517172738', '编辑', '3', 100, '编辑', 0, '2020-06-24 10:31:02', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1275617433612312577', '1275617093517172738', '更新', '4', 100, '更新', 0, '2020-06-24 10:31:11', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1275617472707420161', '1275617093517172738', '查询', '5', 100, '查询', 0, '2020-06-24 10:31:20', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1275617502973517826', '1275617093517172738', '详情', '6', 100, '详情', 0, '2020-06-24 10:31:27', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1275617536959963137', '1275617093517172738', '树', '7', 100, '树', 0, '2020-06-24 10:31:35', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1275617619524837377', '1275617093517172738', '导入', '8', 100, '导入', 0, '2020-06-24 10:31:55', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1275617651816783873', '1275617093517172738', '导出', '9', 100, '导出', 0, '2020-06-24 10:32:03', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1275617683475390465', '1275617093517172738', '授权', '10', 100, '授权', 0, '2020-06-24 10:32:10', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1275617709928865793', '1275617093517172738', '强退', '11', 100, '强退', 0, '2020-06-24 10:32:17', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1275617739091861505', '1275617093517172738', '清空', '12', 100, '清空', 0, '2020-06-24 10:32:23', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1275617788601425921', '1275617093517172738', '修改状态', '13', 100, '修改状态', 0, '2020-06-24 10:32:35', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1277774590944317441', '1277774529430654977', '阿里云', '1', 100, '阿里云', 0, '2020-06-30 09:22:57', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1277774666055913474', '1277774529430654977', '腾讯云', '2', 100, '腾讯云', 0, '2020-06-30 09:23:15', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1277774695168577538', '1277774529430654977', 'minio', '3', 100, 'minio', 0, '2020-06-30 09:23:22', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1277774726835572737', '1277774529430654977', '本地', '4', 100, '本地', 0, '2020-06-30 09:23:29', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1278607123583868929', '1278606951432855553', '运行', '1', 100, '运行', 0, '2020-07-02 16:31:08', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1278607162943217666', '1278606951432855553', '停止', '2', 100, '停止', 0, '2020-07-02 16:31:18', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1278939265862004738', '1278911800547147777', '通知', '1', 100, '通知', 0, '2020-07-03 14:30:57', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1278939319922388994', '1278911800547147777', '公告', '2', 100, '公告', 0, '2020-07-03 14:31:10', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1278939399001796609', '1278911952657776642', '草稿', '0', 100, '草稿', 0, '2020-07-03 14:31:29', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1278939432686252034', '1278911952657776642', '发布', '1', 100, '发布', 0, '2020-07-03 14:31:37', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1278939458804183041', '1278911952657776642', '撤回', '2', 100, '撤回', 0, '2020-07-03 14:31:43', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1278939485878415362', '1278911952657776642', '删除', '3', 100, '删除', 0, '2020-07-03 14:31:50', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1291390260160299009', '1291390159941599233', '是', 'true', 100, '是', 2, '2020-08-06 23:06:46', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1291390315437031426', '1291390159941599233', '否', 'false', 100, '否', 2, '2020-08-06 23:06:59', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1342446007168466945', '1342445962104864770', '下载压缩包', '1', 100, '下载压缩包', 0, '2020-12-25 20:24:04', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1342446035433881601', '1342445962104864770', '生成到本项目', '2', 100, '生成到本项目', 0, '2020-12-25 20:24:11', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1358094655567454210', '1358094419419750401', '输入框', 'input', 100, '输入框', 0, '2021-02-07 00:46:13', 1265476890672672808, '2021-02-08 01:01:28', 1265476890672672808);
INSERT INTO `sys_dict_data` VALUES ('1358094740510498817', '1358094419419750401', '时间选择', 'datepicker', 100, '时间选择', 0, '2021-02-07 00:46:33', 1265476890672672808, '2021-02-08 01:04:07', 1265476890672672808);
INSERT INTO `sys_dict_data` VALUES ('1358094793149014017', '1358094419419750401', '下拉框', 'select', 100, '下拉框', 0, '2021-02-07 00:46:46', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1358095496009506817', '1358094419419750401', '单选框', 'radio', 100, '单选框', 0, '2021-02-07 00:49:33', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1358095673084633090', '1358094419419750401', '开关', 'switch', 100, '开关', 2, '2021-02-07 00:50:15', 1265476890672672808, '2021-02-11 19:07:18', 1265476890672672808);
INSERT INTO `sys_dict_data` VALUES ('1358458689433190402', '1358457818733428737', '等于', 'eq', 1, '等于', 0, '2021-02-08 00:52:45', 1265476890672672808, '2021-02-13 23:35:36', 1265476890672672808);
INSERT INTO `sys_dict_data` VALUES ('1358458785168179202', '1358457818733428737', '模糊', 'like', 2, '模糊', 0, '2021-02-08 00:53:08', 1265476890672672808, '2021-02-13 23:35:46', 1265476890672672808);
INSERT INTO `sys_dict_data` VALUES ('1358460475682406401', '1358094419419750401', '多选框', 'checkbox', 100, '多选框', 0, '2021-02-08 00:59:51', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1358460819019743233', '1358094419419750401', '数字输入框', 'inputnumber', 100, '数字输入框', 0, '2021-02-08 01:01:13', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1358470210267725826', '1358470065111252994', 'Long', 'Long', 100, 'Long', 0, '2021-02-08 01:38:32', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1358470239351029762', '1358470065111252994', 'String', 'String', 100, 'String', 0, '2021-02-08 01:38:39', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1358470265640927233', '1358470065111252994', 'Date', 'Date', 100, 'Date', 0, '2021-02-08 01:38:45', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1358470300168437761', '1358470065111252994', 'Integer', 'Integer', 100, 'Integer', 0, '2021-02-08 01:38:53', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1358470697377415169', '1358470065111252994', 'boolean', 'boolean', 100, 'boolean', 0, '2021-02-08 01:40:28', 1265476890672672808, '2021-02-08 01:40:47', 1265476890672672808);
INSERT INTO `sys_dict_data` VALUES ('1358471133434036226', '1358470065111252994', 'int', 'int', 100, 'int', 0, '2021-02-08 01:42:12', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1358471188291338241', '1358470065111252994', 'double', 'double', 100, 'double', 0, '2021-02-08 01:42:25', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1358756511688761346', '1358457818733428737', '大于', 'gt', 3, '大于', 0, '2021-02-08 20:36:12', 1265476890672672808, '2021-02-13 23:45:24', 1265476890672672808);
INSERT INTO `sys_dict_data` VALUES ('1358756547159990274', '1358457818733428737', '小于', 'lt', 4, '大于', 0, '2021-02-08 20:36:20', 1265476890672672808, '2021-02-13 23:45:29', 1265476890672672808);
INSERT INTO `sys_dict_data` VALUES ('1358756609990664193', '1358457818733428737', '不等于', 'ne', 7, '不等于', 0, '2021-02-08 20:36:35', 1265476890672672808, '2021-02-13 23:45:46', 1265476890672672808);
INSERT INTO `sys_dict_data` VALUES ('1358756685030957057', '1358457818733428737', '大于等于', 'ge', 5, '大于等于', 0, '2021-02-08 20:36:53', 1265476890672672808, '2021-02-13 23:45:35', 1265476890672672808);
INSERT INTO `sys_dict_data` VALUES ('1358756800525312001', '1358457818733428737', '小于等于', 'le', 6, '小于等于', 0, '2021-02-08 20:37:20', 1265476890672672808, '2021-02-13 23:45:40', 1265476890672672808);
INSERT INTO `sys_dict_data` VALUES ('1360529773814083586', '1358094419419750401', '文本域', 'textarea', 100, '文本域', 0, '2021-02-13 18:02:30', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_data` VALUES ('1360606105914732545', '1358457818733428737', '不为空', 'isNotNull', 8, '不为空', 0, '2021-02-13 23:05:49', 1265476890672672808, '2021-02-13 23:45:50', 1265476890672672808);

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
  `sort` int(11) NOT NULL COMMENT '排序',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `status` tinyint(4) NOT NULL COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统字典类型表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES ('1265216211667636226', '通用状态', 'common_status', 100, '通用状态', 0, '2020-05-26 17:40:26', 1265476890672672808, '2020-06-08 11:31:47', 1265476890672672808);
INSERT INTO `sys_dict_type` VALUES ('1265216211667636234', '性别', 'sex', 100, '性别字典', 0, '2020-04-01 10:12:30', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1265216211667636235', '常量的分类', 'consts_type', 100, '常量的分类，用于区别一组配置', 0, '2020-04-14 23:24:13', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1265217074079453185', '是否', 'yes_or_no', 100, '是否', 0, '2020-05-26 17:43:52', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1265217846770913282', '访问类型', 'vis_type', 100, '访问类型', 0, '2020-05-26 17:46:56', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1265221049302372354', '菜单类型', 'menu_type', 100, '菜单类型', 0, '2020-05-26 17:59:39', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1265466149622128641', '发送类型', 'send_type', 100, '发送类型', 0, '2020-05-27 10:13:36', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1265466752209395713', '打开方式', 'open_type', 100, '打开方式', 0, '2020-05-27 10:16:00', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1265467337566461954', '菜单权重', 'menu_weight', 100, '菜单权重', 0, '2020-05-27 10:18:19', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1265468028632571905', '数据范围类型', 'data_scope_type', 100, '数据范围类型', 0, '2020-05-27 10:21:04', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1265468437904367618', '短信发送来源', 'sms_send_source', 100, '短信发送来源', 0, '2020-05-27 10:22:42', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1275617093517172738', '操作类型', 'op_type', 100, '操作类型', 0, '2020-06-24 10:29:50', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1277774529430654977', '文件存储位置', 'file_storage_location', 100, '文件存储位置', 0, '2020-06-30 09:22:42', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1278606951432855553', '运行状态', 'run_status', 100, '定时任务运行状态', 0, '2020-07-02 16:30:27', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1278911800547147777', '通知公告类型', 'notice_type', 100, '通知公告类型', 0, '2020-07-03 12:41:49', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1278911952657776642', '通知公告状态', 'notice_status', 100, '通知公告状态', 0, '2020-07-03 12:42:25', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1291390159941599233', '是否boolean', 'yes_true_false', 100, '是否boolean', 2, '2020-08-06 23:06:22', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1342445962104864770', '代码生成方式', 'code_gen_create_type', 100, '代码生成方式', 0, '2020-12-25 20:23:53', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1358094419419750401', '代码生成作用类型', 'code_gen_effect_type', 100, '代码生成作用类型', 0, '2021-02-07 00:45:16', 1265476890672672808, '2021-02-08 00:47:48', 1265476890672672808);
INSERT INTO `sys_dict_type` VALUES ('1358457818733428737', '代码生成查询类型', 'code_gen_query_type', 100, '代码生成查询类型', 0, '2021-02-08 00:49:18', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_dict_type` VALUES ('1358470065111252994', '代码生成java类型', 'code_gen_java_type', 100, '代码生成java类型', 0, '2021-02-08 01:37:57', 1265476890672672808, NULL, NULL);

-- ----------------------------
-- Table structure for sys_emp
-- ----------------------------
DROP TABLE IF EXISTS `sys_emp`;
CREATE TABLE `sys_emp`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `job_num` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工号',
  `org_id` bigint(20) NOT NULL COMMENT '所属机构id',
  `org_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属机构名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '员工表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_emp
-- ----------------------------
INSERT INTO `sys_emp` VALUES (1275735541155614721, '102', 1265476890672672769, '华夏集团北京分公司');
INSERT INTO `sys_emp` VALUES (1280700700074041345, '110', 1265476890672672771, '研发部');
INSERT INTO `sys_emp` VALUES (1280709549107552257, '100', 1265476890672672770, '华夏集团成都分公司');
INSERT INTO `sys_emp` VALUES (1491982204861599746, NULL, 1265476890672672769, '华夏集团北京分公司');

-- ----------------------------
-- Table structure for sys_emp_ext_org_pos
-- ----------------------------
DROP TABLE IF EXISTS `sys_emp_ext_org_pos`;
CREATE TABLE `sys_emp_ext_org_pos`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `emp_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工id',
  `org_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '机构id',
  `pos_id` bigint(20) NOT NULL COMMENT '岗位id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '员工附属机构岗位表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_emp_ext_org_pos
-- ----------------------------

-- ----------------------------
-- Table structure for sys_emp_pos
-- ----------------------------
DROP TABLE IF EXISTS `sys_emp_pos`;
CREATE TABLE `sys_emp_pos`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `emp_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工id',
  `pos_id` bigint(20) NOT NULL COMMENT '职位id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '员工职位关联表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_emp_pos
-- ----------------------------
INSERT INTO `sys_emp_pos` VALUES (1280710811995709441, '1275735541155614721', 1265476890672672787);
INSERT INTO `sys_emp_pos` VALUES (1280710828479324161, '1280700700074041345', 1265476890672672790);
INSERT INTO `sys_emp_pos` VALUES (1281042262003867649, '1280709549107552257', 1265476890672672787);
INSERT INTO `sys_emp_pos` VALUES (1491982205624963073, '1491982204861599746', 1265476890672672787);

-- ----------------------------
-- Table structure for sys_file_info
-- ----------------------------
DROP TABLE IF EXISTS `sys_file_info`;
CREATE TABLE `sys_file_info`  (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `file_location` tinyint(4) NOT NULL COMMENT '文件存储位置（1:阿里云，2:腾讯云，3:minio，4:本地）',
  `file_bucket` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件仓库',
  `file_origin_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件名称（上传时候的文件名）',
  `file_suffix` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件后缀',
  `file_size_kb` bigint(20) NULL DEFAULT NULL COMMENT '文件大小kb',
  `file_size_info` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件大小信息，计算后的',
  `file_object_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '存储到bucket的名称（文件唯一标识id）',
  `file_path` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '存储路径',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建用户',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改用户',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文件信息表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_file_info
-- ----------------------------

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `pid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '父id',
  `pids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '父ids',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '菜单类型（字典 0目录 1菜单 2按钮）',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `router` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件地址',
  `permission` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `application` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用分类（应用编码）',
  `open_type` tinyint(4) NOT NULL COMMENT '打开方式（字典 0无 1组件 2内链 3外链）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否可见（Y-是，N-否）',
  `link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '链接地址',
  `redirect` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '重定向地址',
  `weight` tinyint(4) NULL DEFAULT NULL COMMENT '权重（字典 1系统权重 2业务权重）',
  `sort` int(11) NOT NULL COMMENT '排序',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统菜单表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES ('1264622039642255311', '0', '[0],', '主控面板', 'system_index', 0, 'home', '/', 'RouteView', NULL, 'system', 0, 'Y', NULL, '/analysis', 1, 1, NULL, 0, '2020-05-25 02:19:24', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255321', '1264622039642255311', '[0],[1264622039642255311],', '分析页', 'system_index_dashboard', 1, NULL, 'analysis', 'system/dashboard/Analysis', NULL, 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-05-25 02:21:55', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255331', '1264622039642255311', '[0],[1264622039642255311],', '工作台', 'system_index_workplace', 1, NULL, 'workplace', 'system/dashboard/Workplace', NULL, 'system', 0, 'Y', NULL, NULL, 1, 2, NULL, 0, '2020-05-25 02:23:48', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255341', '0', '[0],', '组织架构', 'sys_mgr', 0, 'team', '/sys', 'PageView', NULL, 'system', 0, 'Y', NULL, NULL, 1, 2, NULL, 0, '2020-03-27 15:58:16', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255351', '1264622039642255341', '[0],[1264622039642255341],', '用户管理', 'sys_user_mgr', 1, NULL, '/mgr_user', 'system/user/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 3, NULL, 0, '2020-03-27 16:10:21', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255361', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户查询', 'sys_user_mgr_page', 2, NULL, NULL, NULL, 'sysUser:page', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 16:36:49', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255371', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户编辑', 'sys_user_mgr_edit', 2, NULL, NULL, NULL, 'sysUser:edit', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 12:20:23', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255381', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户增加', 'sys_user_mgr_add', 2, NULL, NULL, NULL, 'sysUser:add', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 16:37:35', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255391', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户删除', 'sys_user_mgr_delete', 2, NULL, NULL, NULL, 'sysUser:delete', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 16:37:58', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255401', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户详情', 'sys_user_mgr_detail', 2, NULL, NULL, NULL, 'sysUser:detail', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 16:38:25', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255411', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户导出', 'sys_user_mgr_export', 2, NULL, NULL, NULL, 'sysUser:export', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 12:21:59', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255421', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户选择器', 'sys_user_mgr_selector', 2, NULL, NULL, NULL, 'sysUser:selector', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-03 13:30:14', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255431', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户授权角色', 'sys_user_mgr_grant_role', 2, NULL, NULL, NULL, 'sysUser:grantRole', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 09:22:01', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255441', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户拥有角色', 'sys_user_mgr_own_role', 2, NULL, NULL, NULL, 'sysUser:ownRole', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-05-29 14:27:22', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255451', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户授权数据', 'sys_user_mgr_grant_data', 2, NULL, NULL, NULL, 'sysUser:grantData', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 09:22:13', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255461', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户拥有数据', 'sys_user_mgr_own_data', 2, NULL, NULL, NULL, 'sysUser:ownData', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-05-29 14:27:41', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255471', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户更新信息', 'sys_user_mgr_update_info', 2, NULL, NULL, NULL, 'sysUser:updateInfo', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 16:19:32', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255481', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户修改密码', 'sys_user_mgr_update_pwd', 2, NULL, NULL, NULL, 'sysUser:updatePwd', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 16:20:25', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255491', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户修改状态', 'sys_user_mgr_change_status', 2, NULL, NULL, NULL, 'sysUser:changeStatus', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-23 11:13:14', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255501', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户修改头像', 'sys_user_mgr_update_avatar', 2, NULL, NULL, NULL, 'sysUser:updateAvatar', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 12:21:42', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255511', '1264622039642255351', '[0],[1264622039642255341],[1264622039642255351],', '用户重置密码', 'sys_user_mgr_reset_pwd', 2, NULL, NULL, NULL, 'sysUser:resetPwd', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-05-29 15:01:51', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255521', '1264622039642255341', '[0],[1264622039642255341],', '机构管理', 'sys_org_mgr', 1, NULL, '/org', 'system/org/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 4, NULL, 0, '2020-03-27 17:15:39', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255531', '1264622039642255521', '[0],[1264622039642255341],[1264622039642255521]', '机构查询', 'sys_org_mgr_page', 2, NULL, NULL, NULL, 'sysOrg:page', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 17:17:37', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255541', '1264622039642255521', '[0],[1264622039642255341],[1264622039642255521]', '机构列表', 'sys_org_mgr_list', 2, NULL, NULL, NULL, 'sysOrg:list', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 11:54:26', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255551', '1264622039642255521', '[0],[1264622039642255341],[1264622039642255521]', '机构增加', 'sys_org_mgr_add', 2, NULL, NULL, NULL, 'sysOrg:add', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 17:19:53', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255561', '1264622039642255521', '[0],[1264622039642255341],[1264622039642255521]', '机构编辑', 'sys_org_mgr_edit', 2, NULL, NULL, NULL, 'sysOrg:edit', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 11:54:37', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255571', '1264622039642255521', '[0],[1264622039642255341],[1264622039642255521]', '机构删除', 'sys_org_mgr_delete', 2, NULL, NULL, NULL, 'sysOrg:delete', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 17:20:48', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255581', '1264622039642255521', '[0],[1264622039642255341],[1264622039642255521]', '机构详情', 'sys_org_mgr_detail', 2, NULL, NULL, NULL, 'sysOrg:detail', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 17:21:15', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255591', '1264622039642255521', '[0],[1264622039642255341],[1264622039642255521]', '机构树', 'sys_org_mgr_tree', 2, NULL, NULL, NULL, 'sysOrg:tree', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 17:21:58', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255601', '1264622039642255341', '[0],[1264622039642255341],', '职位管理', 'sys_pos_mgr', 1, NULL, '/pos', 'system/pos/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 5, NULL, 0, '2020-03-27 18:38:31', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255611', '1264622039642255601', '[0],[1264622039642255341],[1264622039642255601],', '职位查询', 'sys_pos_mgr_page', 2, NULL, NULL, NULL, 'sysPos:page', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 18:41:48', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255621', '1264622039642255601', '[0],[1264622039642255341],[1264622039642255601],', '职位列表', 'sys_pos_mgr_list', 2, NULL, NULL, NULL, 'sysPos:list', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 11:55:57', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255631', '1264622039642255601', '[0],[1264622039642255341],[1264622039642255601],', '职位增加', 'sys_pos_mgr_add', 2, NULL, NULL, NULL, 'sysPos:add', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 18:42:20', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255641', '1264622039642255601', '[0],[1264622039642255341],[1264622039642255601],', '职位编辑', 'sys_pos_mgr_edit', 2, NULL, NULL, NULL, 'sysPos:edit', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 11:56:08', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255651', '1264622039642255601', '[0],[1264622039642255341],[1264622039642255601],', '职位删除', 'sys_pos_mgr_delete', 2, NULL, NULL, NULL, 'sysPos:delete', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 18:42:39', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255661', '1264622039642255601', '[0],[1264622039642255341],[1264622039642255601],', '职位详情', 'sys_pos_mgr_detail', 2, NULL, NULL, NULL, 'sysPos:detail', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 18:43:00', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255671', '0', '[0],', '权限管理', 'auth_manager', 0, 'safety-certificate', '/auth', 'PageView', NULL, 'system', 0, 'Y', NULL, NULL, 1, 3, NULL, 0, '2020-07-15 15:51:57', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255681', '1264622039642255671', '[0],[1264622039642255671],', '应用管理', 'sys_app_mgr', 1, NULL, '/app', 'system/app/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 6, NULL, 0, '2020-03-27 16:40:21', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255691', '1264622039642255681', '[0],[1264622039642255671],[1264622039642255681],', '应用查询', 'sys_app_mgr_page', 2, NULL, NULL, NULL, 'sysApp:page', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 16:41:58', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255701', '1264622039642255681', '[0],[1264622039642255671],[1264622039642255681],', '应用列表', 'sys_app_mgr_list', 2, NULL, NULL, NULL, 'sysApp:list', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 10:04:59', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255711', '1264622039642255681', '[0],[1264622039642255671],[1264622039642255681],', '应用增加', 'sys_app_mgr_add', 2, NULL, NULL, NULL, 'sysApp:add', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 16:44:10', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255721', '1264622039642255681', '[0],[1264622039642255671],[1264622039642255681],', '应用编辑', 'sys_app_mgr_edit', 2, NULL, NULL, NULL, 'sysApp:edit', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 10:04:34', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255731', '1264622039642255681', '[0],[1264622039642255671],[1264622039642255681],', '应用删除', 'sys_app_mgr_delete', 2, NULL, NULL, NULL, 'sysApp:delete', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 17:14:29', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255741', '1264622039642255681', '[0],[1264622039642255671],[1264622039642255681],', '应用详情', 'sys_app_mgr_detail', 2, NULL, NULL, NULL, 'sysApp:detail', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 17:14:56', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255751', '1264622039642255681', '[0],[1264622039642255671],[1264622039642255681],', '设为默认应用', 'sys_app_mgr_set_as_default', 2, NULL, NULL, NULL, 'sysApp:setAsDefault', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 17:14:56', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255761', '1264622039642255671', '[0],[1264622039642255671],', '菜单管理', 'sys_menu_mgr', 1, NULL, '/menu', 'system/menu/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 7, NULL, 0, '2020-03-27 18:44:35', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255771', '1264622039642255761', '[0],[1264622039642255671],[1264622039642255761],', '菜单列表', 'sys_menu_mgr_list', 2, NULL, NULL, NULL, 'sysMenu:list', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 18:45:20', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255781', '1264622039642255761', '[0],[1264622039642255671],[1264622039642255761],', '菜单增加', 'sys_menu_mgr_add', 2, NULL, NULL, NULL, 'sysMenu:add', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 18:45:37', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255791', '1264622039642255761', '[0],[1264622039642255671],[1264622039642255761],', '菜单编辑', 'sys_menu_mgr_edit', 2, NULL, NULL, NULL, 'sysMenu:edit', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 11:52:00', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255801', '1264622039642255761', '[0],[1264622039642255671],[1264622039642255761],', '菜单删除', 'sys_menu_mgr_delete', 2, NULL, NULL, NULL, 'sysMenu:delete', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 18:46:01', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255811', '1264622039642255761', '[0],[1264622039642255671],[1264622039642255761],', '菜单详情', 'sys_menu_mgr_detail', 2, NULL, NULL, NULL, 'sysMenu:detail', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 18:46:22', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255821', '1264622039642255761', '[0],[1264622039642255671],[1264622039642255761],', '菜单授权树', 'sys_menu_mgr_grant_tree', 2, NULL, NULL, NULL, 'sysMenu:treeForGrant', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-03 09:50:31', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255831', '1264622039642255761', '[0],[1264622039642255671],[1264622039642255761],', '菜单树', 'sys_menu_mgr_tree', 2, NULL, NULL, NULL, 'sysMenu:tree', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-27 18:47:50', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255841', '1264622039642255761', '[0],[1264622039642255671],[1264622039642255761],', '菜单切换', 'sys_menu_mgr_change', 2, NULL, NULL, NULL, 'sysMenu:change', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-03 09:51:43', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255851', '1264622039642255671', '[0],[1264622039642255671],', '角色管理', 'sys_role_mgr', 1, NULL, '/role', 'system/role/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 8, NULL, 0, '2020-03-28 16:01:09', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255861', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色查询', 'sys_role_mgr_page', 2, NULL, NULL, NULL, 'sysRole:page', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-28 16:02:09', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255871', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色增加', 'sys_role_mgr_add', 2, NULL, NULL, NULL, 'sysRole:add', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-28 16:02:27', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255881', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色编辑', 'sys_role_mgr_edit', 2, NULL, NULL, NULL, 'sysRole:edit', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 11:57:27', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255891', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色删除', 'sys_role_mgr_delete', 2, NULL, NULL, NULL, 'sysRole:delete', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-28 16:02:46', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255901', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色详情', 'sys_role_mgr_detail', 2, NULL, NULL, NULL, 'sysRole:detail', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-03-28 16:03:01', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255911', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色下拉', 'sys_role_mgr_drop_down', 2, NULL, NULL, NULL, 'sysRole:dropDown', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-05-29 15:45:39', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255921', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色授权菜单', 'sys_role_mgr_grant_menu', 2, NULL, NULL, NULL, 'sysRole:grantMenu', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 09:16:27', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255931', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色拥有菜单', 'sys_role_mgr_own_menu', 2, NULL, NULL, NULL, 'sysRole:ownMenu', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-05-29 14:21:54', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255941', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色授权数据', 'sys_role_mgr_grant_data', 2, NULL, NULL, NULL, 'sysRole:grantData', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 09:16:56', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255951', '1264622039642255851', '[0],[1264622039642255671],[1264622039642255851],', '角色拥有数据', 'sys_role_mgr_own_data', 2, NULL, NULL, NULL, 'sysRole:ownData', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-05-29 14:23:08', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255961', '0', '[0],', '开发管理', 'system_tools', 0, 'euro', '/tools', 'PageView', NULL, 'system', 1, 'Y', NULL, NULL, 1, 4, NULL, 0, '2020-05-25 02:10:55', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255971', '1264622039642255961', '[0],[1264622039642255961],', '系统配置', 'system_tools_config', 1, NULL, '/config', 'system/config/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 9, NULL, 0, '2020-05-25 02:12:56', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255981', '1264622039642255971', '[0],[1264622039642255961],[1264622039642255971],', '配置查询', 'system_tools_config_page', 2, NULL, NULL, NULL, 'sysConfig:page', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-05-27 17:02:22', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642255991', '1264622039642255971', '[0],[1264622039642255961],[1264622039642255971],', '配置列表', 'system_tools_config_list', 2, NULL, NULL, NULL, 'sysConfig:list', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-05-27 17:02:42', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256001', '1264622039642255971', '[0],[1264622039642255961],[1264622039642255971],', '配置增加', 'system_tools_config_add', 2, NULL, NULL, NULL, 'sysConfig:add', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-05-27 17:03:31', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256011', '1264622039642255971', '[0],[1264622039642255961],[1264622039642255971],', '配置编辑', 'system_tools_config_edit', 2, NULL, NULL, NULL, 'sysConfig:edit', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-05-27 17:03:55', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256021', '1264622039642255971', '[0],[1264622039642255961],[1264622039642255971],', '配置删除', 'system_tools_config_delete', 2, NULL, NULL, NULL, 'sysConfig:delete', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-05-27 17:03:44', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256031', '1264622039642255971', '[0],[1264622039642255961],[1264622039642255971],', '配置详情', 'system_tools_config_detail', 2, NULL, NULL, NULL, 'sysConfig:detail', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-05-27 17:02:59', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256041', '1264622039642255961', '[0],[1264622039642255961],', '邮件发送', 'sys_email_mgr', 1, NULL, '/email', 'system/email/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 10, NULL, 0, '2020-07-02 11:44:21', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256051', '1264622039642256041', '[0],[1264622039642255961],[1264622039642256041],', '发送文本邮件', 'sys_email_mgr_send_email', 2, NULL, NULL, NULL, 'email:sendEmail', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 11:45:39', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256061', '1264622039642256041', '[0],[1264622039642255961],[1264622039642256041],', '发送html邮件', 'sys_email_mgr_send_email_html', 2, NULL, NULL, NULL, 'email:sendEmailHtml', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 11:45:57', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256071', '1264622039642255961', '[0],[1264622039642255961],', '短信管理', 'sys_sms_mgr', 1, NULL, '/sms', 'system/sms/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 11, NULL, 0, '2020-07-02 12:00:12', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256081', '1264622039642256071', '[0],[1264622039642255961],[1264622039642256071],', '短信发送查询', 'sys_sms_mgr_page', 2, NULL, NULL, NULL, 'sms:page', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 12:16:56', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256091', '1264622039642256071', '[0],[1264622039642255961],[1264622039642256071],', '发送验证码短信', 'sys_sms_mgr_send_login_message', 2, NULL, NULL, NULL, 'sms:sendLoginMessage', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 12:02:31', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256101', '1264622039642256071', '[0],[1264622039642255961],[1264622039642256071],', '验证短信验证码', 'sys_sms_mgr_validate_message', 2, NULL, NULL, NULL, 'sms:validateMessage', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 12:02:50', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256111', '1264622039642255961', '[0],[1264622039642255961],', '字典管理', 'sys_dict_mgr', 1, NULL, '/dict', 'system/dict/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 12, NULL, 0, '2020-04-01 11:17:26', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256121', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典类型查询', 'sys_dict_mgr_dict_type_page', 2, NULL, NULL, NULL, 'sysDictType:page', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 11:20:22', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256131', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典类型列表', 'sys_dict_mgr_dict_type_list', 2, NULL, NULL, NULL, 'sysDictType:list', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-05-29 15:12:35', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256141', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典类型增加', 'sys_dict_mgr_dict_type_add', 2, NULL, NULL, NULL, 'sysDictType:add', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 11:19:58', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256151', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典类型删除', 'sys_dict_mgr_dict_type_delete', 2, NULL, NULL, NULL, 'sysDictType:delete', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 11:21:30', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256161', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典类型编辑', 'sys_dict_mgr_dict_type_edit', 2, NULL, NULL, NULL, 'sysDictType:edit', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 11:21:42', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256171', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典类型详情', 'sys_dict_mgr_dict_type_detail', 2, NULL, NULL, NULL, 'sysDictType:detail', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 11:22:06', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256181', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典类型下拉', 'sys_dict_mgr_dict_type_drop_down', 2, NULL, NULL, NULL, 'sysDictType:dropDown', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 11:22:23', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256191', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典类型修改状态', 'sys_dict_mgr_dict_type_change_status', 2, NULL, NULL, NULL, 'sysDictType:changeStatus', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-23 11:15:50', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256201', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典值查询', 'sys_dict_mgr_dict_page', 2, NULL, NULL, NULL, 'sysDictData:page', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 11:23:11', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256211', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典值列表', 'sys_dict_mgr_dict_list', 2, NULL, NULL, NULL, 'sysDictData:list', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 11:24:58', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256221', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典值增加', 'sys_dict_mgr_dict_add', 2, NULL, NULL, NULL, 'sysDictData:add', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 11:22:51', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256231', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典值删除', 'sys_dict_mgr_dict_delete', 2, NULL, NULL, NULL, 'sysDictData:delete', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 11:23:26', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256241', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典值编辑', 'sys_dict_mgr_dict_edit', 2, NULL, NULL, NULL, 'sysDictData:edit', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 11:24:21', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256251', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典值详情', 'sys_dict_mgr_dict_detail', 2, NULL, NULL, NULL, 'sysDictData:detail', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-04-01 11:24:42', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256261', '1264622039642256111', '[0],[1264622039642255961],[1264622039642256111],', '字典值修改状态', 'sys_dict_mgr_dict_change_status', 2, NULL, NULL, NULL, 'sysDictData:changeStatus', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-23 11:17:53', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256271', '1264622039642255961', '[0],[1264622039642255961],', '接口文档', 'sys_swagger_mgr', 1, NULL, '/swagger', 'Iframe', NULL, 'system', 2, 'Y', 'http://localhost:82/doc.html', NULL, 1, 13, NULL, 0, '2020-07-02 12:16:56', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256281', '0', '[0],', '日志管理', 'sys_log_mgr', 0, 'read', '/log', 'PageView', NULL, 'system', 1, 'Y', NULL, NULL, 1, 5, NULL, 0, '2020-04-01 09:25:01', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256291', '1264622039642256281', '[0],[1264622039642256281],', '访问日志', 'sys_log_mgr_vis_log', 1, NULL, '/vislog', 'system/log/vislog/index', NULL, 'system', 0, 'Y', NULL, NULL, 1, 14, NULL, 0, '2020-04-01 09:26:40', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256301', '1264622039642256291', '[0],[1264622039642256281],[1264622039642256291],', '访问日志查询', 'sys_log_mgr_vis_log_page', 2, NULL, NULL, NULL, 'sysVisLog:page', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 09:55:51', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256311', '1264622039642256291', '[0],[1264622039642256281],[1264622039642256291],', '访问日志清空', 'sys_log_mgr_vis_log_delete', 2, NULL, NULL, NULL, 'sysVisLog:delete', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 09:56:57', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256321', '1264622039642256281', '[0],[1264622039642256281],', '操作日志', 'sys_log_mgr_op_log', 1, NULL, '/oplog', 'system/log/oplog/index', NULL, 'system', 0, 'Y', NULL, NULL, 1, 15, NULL, 0, '2020-04-01 09:26:59', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256331', '1264622039642256321', '[0],[1264622039642256281],[1264622039642256321],', '操作日志查询', 'sys_log_mgr_op_log_page', 2, NULL, NULL, NULL, 'sysOpLog:page', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 09:57:39', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256341', '1264622039642256321', '[0],[1264622039642256281],[1264622039642256321],', '操作日志清空', 'sys_log_mgr_op_log_delete', 2, NULL, NULL, NULL, 'sysOpLog:delete', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-02 09:58:13', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256351', '0', '[0],', '系统监控', 'sys_monitor_mgr', 0, 'deployment-unit', '/monitor', 'PageView', NULL, 'system', 1, 'Y', NULL, NULL, 1, 6, NULL, 0, '2020-06-05 16:00:50', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256361', '1264622039642256351', '[0],[1264622039642256351],', '服务监控', 'sys_monitor_mgr_machine_monitor', 1, NULL, '/machine', 'system/machine/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 16, NULL, 0, '2020-06-05 16:02:38', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256371', '1264622039642256361', '[0],[1264622039642256351],[1264622039642256361],', '服务监控查询', 'sys_monitor_mgr_machine_monitor_query', 2, NULL, NULL, NULL, 'sysMachine:query', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-05 16:05:33', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256381', '1264622039642256351', '[0],[1264622039642256351],', '在线用户', 'sys_monitor_mgr_online_user', 1, NULL, '/onlineUser', 'system/onlineUser/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 17, NULL, 0, '2020-06-05 16:01:55', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256391', '1264622039642256381', '[0],[1264622039642256351],[1264622039642256381],', '在线用户列表', 'sys_monitor_mgr_online_user_list', 2, NULL, NULL, NULL, 'sysOnlineUser:list', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-05 16:03:46', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256401', '1264622039642256381', '[0],[1264622039642256351],[1264622039642256381],', '在线用户强退', 'sys_monitor_mgr_online_user_force_exist', 2, NULL, NULL, NULL, 'sysOnlineUser:forceExist', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-05 16:04:16', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256411', '1264622039642256351', '[0],[1264622039642256351],', '数据监控', 'sys_monitor_mgr_druid', 1, NULL, '/druid', 'Iframe', NULL, 'system', 2, 'Y', 'http://localhost:82/druid', NULL, 1, 18, NULL, 0, '2020-06-28 16:15:07', 1265476890672672808, '2020-09-13 09:39:10', 1265476890672672808);
INSERT INTO `sys_menu` VALUES ('1264622039642256421', '0', '[0],', '通知公告', 'sys_notice', 0, 'sound', '/notice', 'PageView', NULL, 'system', 1, 'Y', NULL, NULL, 1, 7, NULL, 0, '2020-06-29 15:41:53', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256431', '1264622039642256421', '[0],[1264622039642256421],', '公告管理', 'sys_notice_mgr', 1, NULL, '/notice', 'system/notice/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 19, NULL, 0, '2020-06-29 15:44:24', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256441', '1264622039642256431', '[0],[1264622039642256421],[1264622039642256431],', '公告查询', 'sys_notice_mgr_page', 2, NULL, NULL, NULL, 'sysNotice:page', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-29 15:45:30', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256451', '1264622039642256431', '[0],[1264622039642256421],[1264622039642256431],', '公告增加', 'sys_notice_mgr_add', 2, NULL, NULL, NULL, 'sysNotice:add', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-29 15:45:57', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256461', '1264622039642256431', '[0],[1264622039642256421],[1264622039642256431],', '公告编辑', 'sys_notice_mgr_edit', 2, NULL, NULL, NULL, 'sysNotice:edit', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-29 15:46:22', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256471', '1264622039642256431', '[0],[1264622039642256421],[1264622039642256431],', '公告删除', 'sys_notice_mgr_delete', 2, NULL, NULL, NULL, 'sysNotice:delete', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-29 15:46:11', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256481', '1264622039642256431', '[0],[1264622039642256421],[1264622039642256431],', '公告查看', 'sys_notice_mgr_detail', 2, NULL, NULL, NULL, 'sysNotice:detail', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-29 15:46:33', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256491', '1264622039642256431', '[0],[1264622039642256421],[1264622039642256431],', '公告修改状态', 'sys_notice_mgr_changeStatus', 2, NULL, NULL, NULL, 'sysNotice:changeStatus', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-29 15:46:50', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256501', '1264622039642256421', '[0],[1264622039642256421],', '已收公告', 'sys_notice_mgr_received', 1, NULL, '/noticeReceived', 'system/noticeReceived/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 20, NULL, 0, '2020-06-29 16:32:53', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256511', '1264622039642256501', '[0],[1264622039642256421],[1264622039642256501],', '已收公告查询', 'sys_notice_mgr_received_page', 2, NULL, NULL, NULL, 'sysNotice:received', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-29 16:33:43', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256521', '0', '[0],', '文件管理', 'sys_file_mgr', 0, 'file', '/file', 'PageView', NULL, 'system', 1, 'Y', NULL, NULL, 1, 8, NULL, 0, '2020-06-24 17:31:10', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256531', '1264622039642256521', '[0],[1264622039642256521],', '系统文件', 'sys_file_mgr_sys_file', 1, NULL, '/file', 'system/file/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 21, NULL, 0, '2020-06-24 17:32:57', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256541', '1264622039642256531', '[0],[1264622039642256521],[1264622039642256531],', '文件查询', 'sys_file_mgr_sys_file_page', 2, NULL, NULL, NULL, 'sysFileInfo:page', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-24 17:35:38', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256551', '1264622039642256531', '[0],[1264622039642256521],[1264622039642256531],', '文件列表', 'sys_file_mgr_sys_file_list', 2, NULL, NULL, NULL, 'sysFileInfo:list', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-24 17:35:49', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256561', '1264622039642256531', '[0],[1264622039642256521],[1264622039642256531],', '文件删除', 'sys_file_mgr_sys_file_delete', 2, NULL, NULL, NULL, 'sysFileInfo:delete', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-24 17:36:11', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256571', '1264622039642256531', '[0],[1264622039642256521],[1264622039642256531],', '文件详情', 'sys_file_mgr_sys_file_detail', 2, NULL, NULL, NULL, 'sysFileInfo:detail', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-24 17:36:01', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256581', '1264622039642256531', '[0],[1264622039642256521],[1264622039642256531],', '文件上传', 'sys_file_mgr_sys_file_upload', 2, NULL, NULL, NULL, 'sysFileInfo:upload', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-24 17:34:29', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256591', '1264622039642256531', '[0],[1264622039642256521],[1264622039642256531],', '文件下载', 'sys_file_mgr_sys_file_download', 2, NULL, NULL, NULL, 'sysFileInfo:download', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-24 17:34:55', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256601', '1264622039642256531', '[0],[1264622039642256521],[1264622039642256531],', '图片预览', 'sys_file_mgr_sys_file_preview', 2, NULL, NULL, NULL, 'sysFileInfo:preview', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-06-24 17:35:19', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256611', '0', '[0],', '定时任务', 'sys_timers', 0, 'dashboard', '/timers', 'PageView', NULL, 'system', 1, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-01 17:17:20', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256621', '1264622039642256611', '[0],[1264622039642256611],', '任务管理', 'sys_timers_mgr', 1, NULL, '/timers', 'system/timers/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 22, NULL, 0, '2020-07-01 17:18:53', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256631', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务查询', 'sys_timers_mgr_page', 2, NULL, NULL, NULL, 'sysTimers:page', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-01 17:19:43', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256641', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务列表', 'sys_timers_mgr_list', 2, NULL, NULL, NULL, 'sysTimers:list', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-01 17:19:56', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256651', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务详情', 'sys_timers_mgr_detail', 2, NULL, NULL, NULL, 'sysTimers:detail', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-01 17:20:10', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256661', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务增加', 'sys_timers_mgr_add', 2, NULL, NULL, NULL, 'sysTimers:add', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-01 17:20:23', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256671', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务删除', 'sys_timers_mgr_delete', 2, NULL, NULL, NULL, 'sysTimers:delete', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-01 17:20:33', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256681', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务编辑', 'sys_timers_mgr_edit', 2, NULL, NULL, NULL, 'sysTimers:edit', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-01 17:20:43', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256691', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务可执行列表', 'sys_timers_mgr_get_action_classes', 2, NULL, NULL, NULL, 'sysTimers:getActionClasses', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-01 17:22:16', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256701', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务启动', 'sys_timers_mgr_start', 2, NULL, NULL, NULL, 'sysTimers:start', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-01 17:22:32', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256711', '1264622039642256621', '[0],[1264622039642256611],[1264622039642256621],', '定时任务关闭', 'sys_timers_mgr_stop', 2, NULL, NULL, NULL, 'sysTimers:stop', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-07-01 17:22:43', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256721', '0', '[0],', '区域管理', 'sys_area', 0, 'environment', '/area', 'PageView', NULL, 'system', 1, 'Y', NULL, NULL, 1, 100, NULL, 0, '2021-05-19 13:55:40', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256731', '1264622039642256721', '[0],[1264622039642256721],', '系统区域', 'sys_area_mgr', 1, NULL, '/area', 'system/area/index', NULL, 'system', 1, 'Y', NULL, NULL, 1, 100, NULL, 0, '2021-05-19 13:57:42', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1264622039642256741', '1264622039642256731', '[0],[1264622039642256721],[1264622039642256731],', '系统区域列表', 'sys_area_mgr_list', 2, NULL, NULL, NULL, 'sysArea:list', 'system', 0, 'Y', NULL, NULL, 1, 100, NULL, 0, '2021-05-19 14:01:39', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_menu` VALUES ('1342445437296771074', '0', '[0],', '代码生成', 'code_gen', 1, 'thunderbolt', '/codeGenerate/index', 'gen/codeGenerate/index', NULL, 'system_tool', 1, 'Y', NULL, NULL, 1, 100, NULL, 0, '2020-12-25 20:21:48', 1265476890672672808, NULL, NULL);

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `title` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `type` tinyint(4) NOT NULL COMMENT '类型（字典 1通知 2公告）',
  `public_user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发布人id',
  `public_user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发布人姓名',
  `public_org_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发布机构id',
  `public_org_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发布机构名称',
  `public_time` datetime NULL DEFAULT NULL COMMENT '发布时间',
  `cancel_time` datetime NULL DEFAULT NULL COMMENT '撤回时间',
  `status` tinyint(4) NOT NULL COMMENT '状态（字典 0草稿 1发布 2撤回 3删除）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_notice
-- ----------------------------

-- ----------------------------
-- Table structure for sys_notice_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice_user`;
CREATE TABLE `sys_notice_user`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `notice_id` bigint(20) NOT NULL COMMENT '通知公告id',
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户id',
  `status` tinyint(4) NOT NULL COMMENT '状态（字典 0未读 1已读）',
  `read_time` datetime NULL DEFAULT NULL COMMENT '阅读时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统用户数据范围表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_notice_user
-- ----------------------------

-- ----------------------------
-- Table structure for sys_oauth_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_oauth_user`;
CREATE TABLE `sys_oauth_user`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '第三方平台的用户唯一id',
  `access_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户授权的token',
  `nick_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像',
  `blog` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户网址',
  `company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所在公司',
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '位置',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `gender` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别',
  `source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户来源',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户备注（各平台中的用户个人介绍）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建用户',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新用户',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '第三方认证用户信息表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_oauth_user
-- ----------------------------

-- ----------------------------
-- Table structure for sys_op_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_op_log`;
CREATE TABLE `sys_op_log`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `op_type` tinyint(4) NULL DEFAULT NULL COMMENT '操作类型',
  `success` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否执行成功（Y-是，N-否）',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '具体消息',
  `ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'ip',
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `browser` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '浏览器',
  `os` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作系统',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求地址',
  `class_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名称',
  `method_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '方法名称',
  `req_method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求方式（GET POST PUT DELETE)',
  `param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求参数',
  `result` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '返回结果',
  `op_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作账号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统操作日志表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_op_log
-- ----------------------------
INSERT INTO `sys_op_log` VALUES (1491965336042000385, '系统字典类型_树', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/tree', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'tree', 'GET', '', '{\"code\":200,\"data\":[],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:40:30', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965368946315266, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"yes_or_no\"}', '{\"code\":200,\"data\":[{\"code\":\"N\",\"value\":\"否\"},{\"code\":\"Y\",\"value\":\"是\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:40:38', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965369814536193, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"common_status\"}', '{\"code\":200,\"data\":[{\"code\":\"0\",\"value\":\"正常\"},{\"code\":\"1\",\"value\":\"停用\"},{\"code\":\"2\",\"value\":\"删除\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:40:38', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965369890033666, '系统应用_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysApp/page', 'com.concise.com.concise.sys.modular.app.controller.SysAppController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"active\":\"Y\",\"code\":\"system\",\"createTime\":1585134420000,\"createUser\":1265476890672672808,\"id\":1265476890672672821,\"name\":\"系统应用\",\"status\":0,\"updateTime\":1597476185000,\"updateUser\":1280709549107552257},{\"active\":\"N\",\"code\":\"system_tool\",\"createTime\":1608898812000,\"createUser\":1265476890672672808,\"id\":1342445032647098369,\"name\":\"系统工具\",\"status\":0}],\"totalPage\":1,\"totalRows\":2},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:40:38', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965379633401857, '系统菜单_列表', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysMenu/list', 'com.concise.com.concise.sys.modular.menu.controller.SysMenuController', 'list', 'GET', '{}', '{\"code\":200,\"data\":[],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:40:41', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965432578101250, '系统角色_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysRole/page', 'com.concise.com.concise.sys.modular.role.controller.SysRoleController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"ent_manager_role\",\"createTime\":1585826846000,\"createUser\":1265476890672672808,\"dataScopeType\":1,\"id\":\"1265476890672672817\",\"name\":\"组织架构管理员\",\"remark\":\"组织架构管理员\",\"sort\":100,\"status\":0,\"updateTime\":1599897247000,\"updateUser\":1265476890672672808},{\"code\":\"auth_role\",\"createTime\":1585826920000,\"createUser\":1265476890672672808,\"dataScopeType\":5,\"id\":\"1265476890672672818\",\"name\":\"权限管理员\",\"remark\":\"权限管理员\",\"sort\":101,\"status\":0,\"updateTime\":1594867941000,\"updateUser\":1265476890672672808}],\"totalPage\":1,\"totalRows\":2},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:40:53', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965457882337281, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:40:59', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965457882337282, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"account\":\"yubaoshan\",\"birthday\":************,\"email\":\"<EMAIL>\",\"id\":1275735541155614721,\"name\":\"俞宝山\",\"nickName\":\"俞宝山\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"102\",\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"},\"tel\":\"\"},{\"account\":\"xuyuxiang\",\"birthday\":*************,\"id\":1280709549107552257,\"name\":\"徐玉祥\",\"nickName\":\"就是那个锅\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"100\",\"orgId\":1265476890672672770,\"orgName\":\"华夏集团成都分公司\"}}],\"totalPage\":1,\"totalRows\":2},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:40:59', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965457974611970, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"sex\"}', '{\"code\":200,\"data\":[{\"code\":\"1\",\"value\":\"男\"},{\"code\":\"2\",\"value\":\"女\"},{\"code\":\"3\",\"value\":\"未知\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:40:59', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965458054303746, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"common_status\"}', '{\"code\":200,\"data\":[{\"code\":\"0\",\"value\":\"正常\"},{\"code\":\"1\",\"value\":\"停用\"},{\"code\":\"2\",\"value\":\"删除\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:40:59', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965790612287489, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:42:19', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965790796836865, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"account\":\"yubaoshan\",\"birthday\":************,\"email\":\"<EMAIL>\",\"id\":1275735541155614721,\"name\":\"俞宝山\",\"nickName\":\"俞宝山\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"102\",\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"},\"tel\":\"\"},{\"account\":\"xuyuxiang\",\"birthday\":*************,\"id\":1280709549107552257,\"name\":\"徐玉祥\",\"nickName\":\"就是那个锅\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"100\",\"orgId\":1265476890672672770,\"orgName\":\"华夏集团成都分公司\"}}],\"totalPage\":1,\"totalRows\":2},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:42:19', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965791518257154, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"sex\"}', '{\"code\":200,\"data\":[{\"code\":\"1\",\"value\":\"男\"},{\"code\":\"2\",\"value\":\"女\"},{\"code\":\"3\",\"value\":\"未知\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:42:19', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965791715389442, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"common_status\"}', '{\"code\":200,\"data\":[{\"code\":\"0\",\"value\":\"正常\"},{\"code\":\"1\",\"value\":\"停用\"},{\"code\":\"2\",\"value\":\"删除\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:42:19', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965803618824193, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:42:22', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965804981972994, '系统机构_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/page', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"hxjt_bj\",\"createTime\":1585212942000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672769\",\"name\":\"华夏集团北京分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团北京分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd\",\"createTime\":1585212962000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672770\",\"name\":\"华夏集团成都分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团成都分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt\",\"createTime\":1585212653000,\"createUser\":1265476890672672808,\"id\":\"1265476890651701250\",\"name\":\"华夏集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"remark\":\"华夏集团总公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_yfb\",\"createTime\":1585212996000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672771\",\"name\":\"研发部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司研发部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_qhb\",\"createTime\":1585213026000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672772\",\"name\":\"企划部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司企划部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb\",\"createTime\":1585213055000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672773\",\"name\":\"市场部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司市场部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_cwb\",\"createTime\":1585213081000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672774\",\"name\":\"财务部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司财务部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb_2b\",\"createTime\":1586158610000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672775\",\"name\":\"市场部二部\",\"pid\":\"1265476890672672773\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],[1265476890672672773],\",\"remark\":\"华夏集团成都分公司市场部二部\",\"sort\":100,\"status\":0}],\"totalPage\":1,\"totalRows\":8},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:42:22', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965805242019841, '系统机构_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/page', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"hxjt_bj\",\"createTime\":1585212942000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672769\",\"name\":\"华夏集团北京分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团北京分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd\",\"createTime\":1585212962000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672770\",\"name\":\"华夏集团成都分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团成都分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt\",\"createTime\":1585212653000,\"createUser\":1265476890672672808,\"id\":\"1265476890651701250\",\"name\":\"华夏集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"remark\":\"华夏集团总公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_yfb\",\"createTime\":1585212996000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672771\",\"name\":\"研发部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司研发部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_qhb\",\"createTime\":1585213026000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672772\",\"name\":\"企划部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司企划部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb\",\"createTime\":1585213055000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672773\",\"name\":\"市场部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司市场部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_cwb\",\"createTime\":1585213081000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672774\",\"name\":\"财务部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司财务部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb_2b\",\"createTime\":1586158610000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672775\",\"name\":\"市场部二部\",\"pid\":\"1265476890672672773\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],[1265476890672672773],\",\"remark\":\"华夏集团成都分公司市场部二部\",\"sort\":100,\"status\":0}],\"totalPage\":1,\"totalRows\":8},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:42:22', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965815664865282, '系统职位_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysPos/page', 'com.concise.com.concise.sys.modular.pos.controller.SysPosController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"zjl\",\"createTime\":1585222134000,\"createUser\":1265476890672672808,\"id\":1265476890672672787,\"name\":\"总经理\",\"remark\":\"总经理职位\",\"sort\":100,\"status\":0,\"updateTime\":1591102864000,\"updateUser\":1265476890672672808},{\"code\":\"fzjl\",\"createTime\":1585222197000,\"createUser\":1265476890672672808,\"id\":1265476890672672788,\"name\":\"副总经理\",\"remark\":\"副总经理职位\",\"sort\":100,\"status\":0},{\"code\":\"bmjl\",\"createTime\":1585222309000,\"createUser\":1265476890672672808,\"id\":1265476890672672789,\"name\":\"部门经理\",\"remark\":\"部门经理职位\",\"sort\":100,\"status\":0},{\"code\":\"gzry\",\"createTime\":1590550320000,\"createUser\":1265476890672672808,\"id\":1265476890672672790,\"name\":\"工作人员\",\"remark\":\"工作人员职位\",\"sort\":100,\"status\":0,\"updateTime\":*************,\"updateUser\":1265476890672672808}],\"totalPage\":1,\"totalRows\":4},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:42:24', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965828495241218, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"yes_or_no\"}', '{\"code\":200,\"data\":[{\"code\":\"N\",\"value\":\"否\"},{\"code\":\"Y\",\"value\":\"是\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:42:28', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965829606731777, '系统应用_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysApp/page', 'com.concise.com.concise.sys.modular.app.controller.SysAppController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"active\":\"Y\",\"code\":\"system\",\"createTime\":1585134420000,\"createUser\":1265476890672672808,\"id\":1265476890672672821,\"name\":\"系统应用\",\"status\":0,\"updateTime\":1597476185000,\"updateUser\":1280709549107552257},{\"active\":\"N\",\"code\":\"system_tool\",\"createTime\":1608898812000,\"createUser\":1265476890672672808,\"id\":1342445032647098369,\"name\":\"系统工具\",\"status\":0}],\"totalPage\":1,\"totalRows\":2},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:42:28', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965829736755201, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"common_status\"}', '{\"code\":200,\"data\":[{\"code\":\"0\",\"value\":\"正常\"},{\"code\":\"1\",\"value\":\"停用\"},{\"code\":\"2\",\"value\":\"删除\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:42:28', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965836611219457, '系统菜单_列表', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysMenu/list', 'com.concise.com.concise.sys.modular.menu.controller.SysMenuController', 'list', 'GET', '{}', '{\"code\":200,\"data\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"system_index_workplace\",\"component\":\"system/dashboard/Workplace\",\"createTime\":1590344628000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255331\",\"name\":\"工作台\",\"openType\":0,\"pid\":\"1264622039642255311\",\"pids\":\"[0],[1264622039642255311],\",\"router\":\"workplace\",\"sort\":2,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"system_index_dashboard\",\"component\":\"system/dashboard/Analysis\",\"createTime\":1590344515000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255321\",\"name\":\"分析页\",\"openType\":0,\"pid\":\"1264622039642255311\",\"pids\":\"[0],[1264622039642255311],\",\"router\":\"analysis\",\"sort\":100,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"system_index\",\"component\":\"RouteView\",\"createTime\":1590344364000,\"createUser\":1265476890672672808,\"icon\":\"home\",\"id\":\"1264622039642255311\",\"name\":\"主控面板\",\"openType\":0,\"pid\":\"0\",\"pids\":\"[0],\",\"redirect\":\"/analysis\",\"router\":\"/\",\"sort\":1,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_page\",\"createTime\":1585298209000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255361\",\"name\":\"用户查询\",\"openType\":0,\"permission\":\"sysUser:page\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_edit\",\"createTime\":1593663623000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255371\",\"name\":\"用户编辑\",\"openType\":0,\"permission\":\"sysUser:edit\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_add\",\"createTime\":1585298255000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255381\",\"name\":\"用户增加\",\"openType\":0,\"permission\":\"sysUser:add\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_delete\",\"createTime\":1585298278000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255391\",\"name\":\"用户删除\",\"openType\":0,\"permission\":\"sysUser:delete\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_detail\",\"createTime\":1585298305000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255401\",\"name\":\"用户详情\",\"openType\":0,\"permission\":\"sysUser:detail\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_export\",\"createTime\":1593663719000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255411\",\"name\":\"用户导出\",\"openType\":0,\"permission\":\"sysUser:export\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_selector\",\"createTime\":1593754214000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255421\",\"name\":\"用户选择器\",\"openType\":0,\"permission\":\"sysUser:selector\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_grant_role\",\"createTime\":1585704121000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255431\",\"name\":\"用户授权角色\",\"openType\":0,\"permission\":\"sysUser:grantRole\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_own_role\",\"createTime\":1590733642000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255441\",\"name\":\"用户拥有角色\",\"openType\":0,\"permission\":\"sysUser:ownRole\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_grant_data\",\"createTime\":1585704133000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255451\",\"name\":\"用户授权数据\",\"openType\":0,\"permission\":\"sysUser:grantData\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_own_data\",\"createTime\":1590733661000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255461\",\"name\":\"用户拥有数据\",\"openType\":0,\"permission\":\"sysUser:ownData\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_update_info\",\"createTime\":1585729172000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255471\",\"name\":\"用户更新信息\",\"openType\":0,\"permission\":\"sysUser:updateInfo\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_update_pwd\",\"createTime\":1585729225000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255481\",\"name\":\"用户修改密码\",\"openType\":0,\"permission\":\"sysUser:updatePwd\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_change_status\",\"createTime\":1592881994000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255491\",\"name\":\"用户修改状态\",\"openType\":0,\"permission\":\"sysUser:changeStatus\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_update_avatar\",\"createTime\":1593663702000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255501\",\"name\":\"用户修改头像\",\"openType\":0,\"permission\":\"sysUser:updateAvatar\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_reset_pwd\",\"createTime\":1590735711000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255511\",\"name\":\"用户重置密码\",\"openType\":0,\"permission\":\"sysUser:resetPwd\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_user_mgr\",\"component\":\"system/user/index\",\"createTime\":1585296621000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255351\",\"name\":\"用户管理\",\"openType\":1,\"pid\":\"1264622039642255341\",\"pids\":\"[0],[1264622039642255341],\",\"router\":\"/mgr_user\",\"sort\":3,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_org_mgr_page\",\"createTime\":1585300657000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255531\",\"name\":\"机构查询\",\"openType\":0,\"permission\":\"sysOrg:page\",\"pid\":\"1264622039642255521\",\"pids\":\"[0],[1264622039642255341],[1264622039642255521]\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_org_mgr_list\",\"createTime\":1593662066000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255541\",\"name\":\"机构列表\",\"openType\":0,\"permission\":\"sysOrg:list\",\"pid\":\"1264622039642255521\",\"pids\":\"[0],[1264622039642255341],[1264622039642255521]\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_org_mgr_add\",\"createTime\":1585300793000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255551\",\"name\":\"机构增加\",\"openType\":0,\"permission\":\"sysOrg:add\",\"pid\":\"1264622039642255521\",\"pids\":\"[0],[1264622039642255341],[1264622039642255521]\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_org_mgr_edit\",\"createTime\":1593662077000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255561\",\"name\":\"机构编辑\",\"openType\":0,\"permission\":\"sysOrg:edit\",\"pid\":\"1264622039642255521\",\"pids\":\"[0],[1264622039642255341],[1264622039642255521]\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_org_mgr_delete\",\"createTime\":1585300848000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255571\",\"name\":\"机构删除\",\"openType\":0,\"permission\":\"sysOrg:delete\",\"pid\":\"1264622039642255521\",\"pids\":\"[0],[1264622039642255341],[1264622039642255521]\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_org_mgr_detail\",\"createTime\":1585300875000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255581\",\"name\":\"机构详情\",\"openType\":0,\"permission\":\"sysOrg:detail\",\"pid\":\"1264622039642255521\",\"pids\":\"[0],[1264622039642255341],[1264622039642255521]\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_org_mgr_tree\",\"createTime\":1585300918000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255591\",\"name\":\"机构树\",\"openType\":0,\"permission\":\"sysOrg:tree\",\"pid\":\"1264622039642255521\",\"pids\":\"[0],[1264622039642255341],[1264622039642255521]\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_org_mgr\",\"component\":\"system/org/index\",\"createTime\":1585300539000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255521\",\"name\":\"机构管理\",\"openType\":1,\"pid\":\"1264622039642255341\",\"pids\":\"[0],[1264622039642255341],\",\"router\":\"/org\",\"sort\":4,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_pos_mgr_list\",\"createTime\":1593662157000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255621\",\"name\":\"职位列表\",\"openType\":0,\"permission\":\"sysPos:list\",\"pid\":\"1264622039642255601\",\"pids\":\"[0],[1264622039642255341],[1264622039642255601],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_pos_mgr_add\",\"createTime\":1585305740000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255631\",\"name\":\"职位增加\",\"openType\":0,\"permission\":\"sysPos:add\",\"pid\":\"1264622039642255601\",\"pids\":\"[0],[1264622039642255341],[1264622039642255601],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_pos_mgr_edit\",\"createTime\":1593662168000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255641\",\"name\":\"职位编辑\",\"openType\":0,\"permission\":\"sysPos:edit\",\"pid\":\"1264622039642255601\",\"pids\":\"[0],[1264622039642255341],[1264622039642255601],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_pos_mgr_delete\",\"createTime\":1585305759000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255651\",\"name\":\"职位删除\",\"openType\":0,\"permission\":\"sysPos:delete\",\"pid\":\"1264622039642255601\",\"pids\":\"[0],[1264622039642255341],[1264622039642255601],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_pos_mgr_detail\",\"createTime\":1585305780000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255661\",\"name\":\"职位详情\",\"openType\":0,\"permission\":\"sysPos:detail\",\"pid\":\"1264622039642255601\",\"pids\":\"[0],[1264622039642255341],[1264622039642255601],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_pos_mgr_page\",\"createTime\":1585305708000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255611\",\"name\":\"职位查询\",\"openType\":0,\"permission\":\"sysPos:page\",\"pid\":\"1264622039642255601\",\"pids\":\"[0],[1264622039642255341],[1264622039642255601],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_pos_mgr\",\"component\":\"system/pos/index\",\"createTime\":1585305511000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255601\",\"name\":\"职位管理\",\"openType\":1,\"pid\":\"1264622039642255341\",\"pids\":\"[0],[1264622039642255341],\",\"router\":\"/pos\",\"sort\":5,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_mgr\",\"component\":\"PageView\",\"createTime\":1585295896000,\"createUser\":1265476890672672808,\"icon\":\"team\",\"id\":\"1264622039642255341\",\"name\":\"组织架构\",\"openType\":0,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/sys\",\"sort\":2,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_app_mgr_page\",\"createTime\":1585298518000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255691\",\"name\":\"应用查询\",\"openType\":0,\"permission\":\"sysApp:page\",\"pid\":\"1264622039642255681\",\"pids\":\"[0],[1264622039642255671],[1264622039642255681],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_app_mgr_list\",\"createTime\":1593655499000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255701\",\"name\":\"应用列表\",\"openType\":0,\"permission\":\"sysApp:list\",\"pid\":\"1264622039642255681\",\"pids\":\"[0],[1264622039642255671],[1264622039642255681],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_app_mgr_add\",\"createTime\":1585298650000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255711\",\"name\":\"应用增加\",\"openType\":0,\"permission\":\"sysApp:add\",\"pid\":\"1264622039642255681\",\"pids\":\"[0],[1264622039642255671],[1264622039642255681],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_app_mgr_edit\",\"createTime\":1593655474000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255721\",\"name\":\"应用编辑\",\"openType\":0,\"permission\":\"sysApp:edit\",\"pid\":\"1264622039642255681\",\"pids\":\"[0],[1264622039642255671],[1264622039642255681],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_app_mgr_delete\",\"createTime\":1585300469000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255731\",\"name\":\"应用删除\",\"openType\":0,\"permission\":\"sysApp:delete\",\"pid\":\"1264622039642255681\",\"pids\":\"[0],[1264622039642255671],[1264622039642255681],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_app_mgr_detail\",\"createTime\":1585300496000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255741\",\"name\":\"应用详情\",\"openType\":0,\"permission\":\"sysApp:detail\",\"pid\":\"1264622039642255681\",\"pids\":\"[0],[1264622039642255671],[1264622039642255681],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_app_mgr_set_as_default\",\"createTime\":1585300496000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255751\",\"name\":\"设为默认应用\",\"openType\":0,\"permission\":\"sysApp:setAsDefault\",\"pid\":\"1264622039642255681\",\"pids\":\"[0],[1264622039642255671],[1264622039642255681],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_app_mgr\",\"component\":\"system/app/index\",\"createTime\":1585298421000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255681\",\"name\":\"应用管理\",\"openType\":1,\"pid\":\"1264622039642255671\",\"pids\":\"[0],[1264622039642255671],\",\"router\":\"/app\",\"sort\":6,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_menu_mgr_list\",\"createTime\":1585305920000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255771\",\"name\":\"菜单列表\",\"openType\":0,\"permission\":\"sysMenu:list\",\"pid\":\"1264622039642255761\",\"pids\":\"[0],[1264622039642255671],[1264622039642255761],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_menu_mgr_add\",\"createTime\":1585305937000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255781\",\"name\":\"菜单增加\",\"openType\":0,\"permission\":\"sysMenu:add\",\"pid\":\"1264622039642255761\",\"pids\":\"[0],[1264622039642255671],[1264622039642255761],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_menu_mgr_edit\",\"createTime\":1593661920000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255791\",\"name\":\"菜单编辑\",\"openType\":0,\"permission\":\"sysMenu:edit\",\"pid\":\"1264622039642255761\",\"pids\":\"[0],[1264622039642255671],[1264622039642255761],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_menu_mgr_delete\",\"createTime\":1585305961000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255801\",\"name\":\"菜单删除\",\"openType\":0,\"permission\":\"sysMenu:delete\",\"pid\":\"1264622039642255761\",\"pids\":\"[0],[1264622039642255671],[1264622039642255761],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_menu_mgr_detail\",\"createTime\":1585305982000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255811\",\"name\":\"菜单详情\",\"openType\":0,\"permission\":\"sysMenu:detail\",\"pid\":\"1264622039642255761\",\"pids\":\"[0],[1264622039642255671],[1264622039642255761],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_menu_mgr_grant_tree\",\"createTime\":1591149031000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255821\",\"name\":\"菜单授权树\",\"openType\":0,\"permission\":\"sysMenu:treeForGrant\",\"pid\":\"1264622039642255761\",\"pids\":\"[0],[1264622039642255671],[1264622039642255761],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_menu_mgr_tree\",\"createTime\":1585306070000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255831\",\"name\":\"菜单树\",\"openType\":0,\"permission\":\"sysMenu:tree\",\"pid\":\"1264622039642255761\",\"pids\":\"[0],[1264622039642255671],[1264622039642255761],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_menu_mgr_change\",\"createTime\":1591149103000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255841\",\"name\":\"菜单切换\",\"openType\":0,\"permission\":\"sysMenu:change\",\"pid\":\"1264622039642255761\",\"pids\":\"[0],[1264622039642255671],[1264622039642255761],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_menu_mgr\",\"component\":\"system/menu/index\",\"createTime\":1585305875000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255761\",\"name\":\"菜单管理\",\"openType\":1,\"pid\":\"1264622039642255671\",\"pids\":\"[0],[1264622039642255671],\",\"router\":\"/menu\",\"sort\":7,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_edit\",\"createTime\":1593662247000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255881\",\"name\":\"角色编辑\",\"openType\":0,\"permission\":\"sysRole:edit\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_delete\",\"createTime\":1585382566000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255891\",\"name\":\"角色删除\",\"openType\":0,\"permission\":\"sysRole:delete\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_detail\",\"createTime\":1585382581000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255901\",\"name\":\"角色详情\",\"openType\":0,\"permission\":\"sysRole:detail\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_drop_down\",\"createTime\":1590738339000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255911\",\"name\":\"角色下拉\",\"openType\":0,\"permission\":\"sysRole:dropDown\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_grant_menu\",\"createTime\":1585703787000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255921\",\"name\":\"角色授权菜单\",\"openType\":0,\"permission\":\"sysRole:grantMenu\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_own_menu\",\"createTime\":1590733314000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255931\",\"name\":\"角色拥有菜单\",\"openType\":0,\"permission\":\"sysRole:ownMenu\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_grant_data\",\"createTime\":1585703816000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255941\",\"name\":\"角色授权数据\",\"openType\":0,\"permission\":\"sysRole:grantData\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_own_data\",\"createTime\":1590733388000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255951\",\"name\":\"角色拥有数据\",\"openType\":0,\"permission\":\"sysRole:ownData\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_page\",\"createTime\":1585382529000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255861\",\"name\":\"角色查询\",\"openType\":0,\"permission\":\"sysRole:page\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_add\",\"createTime\":1585382547000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255871\",\"name\":\"角色增加\",\"openType\":0,\"permission\":\"sysRole:add\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_role_mgr\",\"component\":\"system/role/index\",\"createTime\":1585382469000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255851\",\"name\":\"角色管理\",\"openType\":1,\"pid\":\"1264622039642255671\",\"pids\":\"[0],[1264622039642255671],\",\"router\":\"/role\",\"sort\":8,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"auth_manager\",\"component\":\"PageView\",\"createTime\":1594799517000,\"createUser\":1265476890672672808,\"icon\":\"safety-certificate\",\"id\":\"1264622039642255671\",\"name\":\"权限管理\",\"openType\":0,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/auth\",\"sort\":3,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"system_tools_config_page\",\"createTime\":1590570142000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255981\",\"name\":\"配置查询\",\"openType\":0,\"permission\":\"sysConfig:page\",\"pid\":\"1264622039642255971\",\"pids\":\"[0],[1264622039642255961],[1264622039642255971],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"system_tools_config_list\",\"createTime\":1590570162000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255991\",\"name\":\"配置列表\",\"openType\":0,\"permission\":\"sysConfig:list\",\"pid\":\"1264622039642255971\",\"pids\":\"[0],[1264622039642255961],[1264622039642255971],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"system_tools_config_add\",\"createTime\":1590570211000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256001\",\"name\":\"配置增加\",\"openType\":0,\"permission\":\"sysConfig:add\",\"pid\":\"1264622039642255971\",\"pids\":\"[0],[1264622039642255961],[1264622039642255971],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"system_tools_config_edit\",\"createTime\":1590570235000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256011\",\"name\":\"配置编辑\",\"openType\":0,\"permission\":\"sysConfig:edit\",\"pid\":\"1264622039642255971\",\"pids\":\"[0],[1264622039642255961],[1264622039642255971],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"system_tools_config_delete\",\"createTime\":1590570224000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256021\",\"name\":\"配置删除\",\"openType\":0,\"permission\":\"sysConfig:delete\",\"pid\":\"1264622039642255971\",\"pids\":\"[0],[1264622039642255961],[1264622039642255971],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"system_tools_config_detail\",\"createTime\":1590570179000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256031\",\"name\":\"配置详情\",\"openType\":0,\"permission\":\"sysConfig:detail\",\"pid\":\"1264622039642255971\",\"pids\":\"[0],[1264622039642255961],[1264622039642255971],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"system_tools_config\",\"component\":\"system/config/index\",\"createTime\":1590343976000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255971\",\"name\":\"系统配置\",\"openType\":1,\"pid\":\"1264622039642255961\",\"pids\":\"[0],[1264622039642255961],\",\"router\":\"/config\",\"sort\":9,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_email_mgr_send_email\",\"createTime\":1593661539000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256051\",\"name\":\"发送文本邮件\",\"openType\":0,\"permission\":\"email:sendEmail\",\"pid\":\"1264622039642256041\",\"pids\":\"[0],[1264622039642255961],[1264622039642256041],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_email_mgr_send_email_html\",\"createTime\":1593661557000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256061\",\"name\":\"发送html邮件\",\"openType\":0,\"permission\":\"email:sendEmailHtml\",\"pid\":\"1264622039642256041\",\"pids\":\"[0],[1264622039642255961],[1264622039642256041],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_email_mgr\",\"component\":\"system/email/index\",\"createTime\":1593661461000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256041\",\"name\":\"邮件发送\",\"openType\":1,\"pid\":\"1264622039642255961\",\"pids\":\"[0],[1264622039642255961],\",\"router\":\"/email\",\"sort\":10,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_sms_mgr_page\",\"createTime\":1593663416000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256081\",\"name\":\"短信发送查询\",\"openType\":0,\"permission\":\"sms:page\",\"pid\":\"1264622039642256071\",\"pids\":\"[0],[1264622039642255961],[1264622039642256071],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_sms_mgr_send_login_message\",\"createTime\":1593662551000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256091\",\"name\":\"发送验证码短信\",\"openType\":0,\"permission\":\"sms:sendLoginMessage\",\"pid\":\"1264622039642256071\",\"pids\":\"[0],[1264622039642255961],[1264622039642256071],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_sms_mgr_validate_message\",\"createTime\":1593662570000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256101\",\"name\":\"验证短信验证码\",\"openType\":0,\"permission\":\"sms:validateMessage\",\"pid\":\"1264622039642256071\",\"pids\":\"[0],[1264622039642255961],[1264622039642256071],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_sms_mgr\",\"component\":\"system/sms/index\",\"createTime\":1593662412000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256071\",\"name\":\"短信管理\",\"openType\":1,\"pid\":\"1264622039642255961\",\"pids\":\"[0],[1264622039642255961],\",\"router\":\"/sms\",\"sort\":11,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_type_list\",\"createTime\":1590736355000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256131\",\"name\":\"字典类型列表\",\"openType\":0,\"permission\":\"sysDictType:list\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_type_add\",\"createTime\":1585711198000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256141\",\"name\":\"字典类型增加\",\"openType\":0,\"permission\":\"sysDictType:add\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_type_delete\",\"createTime\":1585711290000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256151\",\"name\":\"字典类型删除\",\"openType\":0,\"permission\":\"sysDictType:delete\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_type_edit\",\"createTime\":1585711302000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256161\",\"name\":\"字典类型编辑\",\"openType\":0,\"permission\":\"sysDictType:edit\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_type_detail\",\"createTime\":1585711326000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256171\",\"name\":\"字典类型详情\",\"openType\":0,\"permission\":\"sysDictType:detail\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_type_drop_down\",\"createTime\":1585711343000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256181\",\"name\":\"字典类型下拉\",\"openType\":0,\"permission\":\"sysDictType:dropDown\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_type_change_status\",\"createTime\":1592882150000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256191\",\"name\":\"字典类型修改状态\",\"openType\":0,\"permission\":\"sysDictType:changeStatus\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_page\",\"createTime\":1585711391000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256201\",\"name\":\"字典值查询\",\"openType\":0,\"permission\":\"sysDictData:page\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_list\",\"createTime\":1585711498000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256211\",\"name\":\"字典值列表\",\"openType\":0,\"permission\":\"sysDictData:list\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_add\",\"createTime\":1585711371000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256221\",\"name\":\"字典值增加\",\"openType\":0,\"permission\":\"sysDictData:add\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_delete\",\"createTime\":1585711406000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256231\",\"name\":\"字典值删除\",\"openType\":0,\"permission\":\"sysDictData:delete\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_edit\",\"createTime\":1585711461000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256241\",\"name\":\"字典值编辑\",\"openType\":0,\"permission\":\"sysDictData:edit\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_detail\",\"createTime\":1585711482000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256251\",\"name\":\"字典值详情\",\"openType\":0,\"permission\":\"sysDictData:detail\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_change_status\",\"createTime\":1592882273000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256261\",\"name\":\"字典值修改状态\",\"openType\":0,\"permission\":\"sysDictData:changeStatus\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_type_page\",\"createTime\":1585711222000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256121\",\"name\":\"字典类型查询\",\"openType\":0,\"permission\":\"sysDictType:page\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_dict_mgr\",\"component\":\"system/dict/index\",\"createTime\":1585711046000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256111\",\"name\":\"字典管理\",\"openType\":1,\"pid\":\"1264622039642255961\",\"pids\":\"[0],[1264622039642255961],\",\"router\":\"/dict\",\"sort\":12,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_swagger_mgr\",\"component\":\"Iframe\",\"createTime\":1593663416000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256271\",\"link\":\"http://localhost:82/doc.html\",\"name\":\"接口文档\",\"openType\":2,\"pid\":\"1264622039642255961\",\"pids\":\"[0],[1264622039642255961],\",\"router\":\"/swagger\",\"sort\":13,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"system_tools\",\"component\":\"PageView\",\"createTime\":1590343855000,\"createUser\":1265476890672672808,\"icon\":\"euro\",\"id\":\"1264622039642255961\",\"name\":\"开发管理\",\"openType\":1,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/tools\",\"sort\":4,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_log_mgr_vis_log_page\",\"createTime\":1593654951000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256301\",\"name\":\"访问日志查询\",\"openType\":0,\"permission\":\"sysVisLog:page\",\"pid\":\"1264622039642256291\",\"pids\":\"[0],[1264622039642256281],[1264622039642256291],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_log_mgr_vis_log_delete\",\"createTime\":1593655017000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256311\",\"name\":\"访问日志清空\",\"openType\":0,\"permission\":\"sysVisLog:delete\",\"pid\":\"1264622039642256291\",\"pids\":\"[0],[1264622039642256281],[1264622039642256291],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_log_mgr_vis_log\",\"component\":\"system/log/vislog/index\",\"createTime\":1585704400000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256291\",\"name\":\"访问日志\",\"openType\":0,\"pid\":\"1264622039642256281\",\"pids\":\"[0],[1264622039642256281],\",\"router\":\"/vislog\",\"sort\":14,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_log_mgr_op_log_page\",\"createTime\":1593655059000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256331\",\"name\":\"操作日志查询\",\"openType\":0,\"permission\":\"sysOpLog:page\",\"pid\":\"1264622039642256321\",\"pids\":\"[0],[1264622039642256281],[1264622039642256321],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_log_mgr_op_log_delete\",\"createTime\":1593655093000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256341\",\"name\":\"操作日志清空\",\"openType\":0,\"permission\":\"sysOpLog:delete\",\"pid\":\"1264622039642256321\",\"pids\":\"[0],[1264622039642256281],[1264622039642256321],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_log_mgr_op_log\",\"component\":\"system/log/oplog/index\",\"createTime\":1585704419000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256321\",\"name\":\"操作日志\",\"openType\":0,\"pid\":\"1264622039642256281\",\"pids\":\"[0],[1264622039642256281],\",\"router\":\"/oplog\",\"sort\":15,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_log_mgr\",\"component\":\"PageView\",\"createTime\":1585704301000,\"createUser\":1265476890672672808,\"icon\":\"read\",\"id\":\"1264622039642256281\",\"name\":\"日志管理\",\"openType\":1,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/log\",\"sort\":5,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_monitor_mgr_machine_monitor_query\",\"createTime\":1591344333000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256371\",\"name\":\"服务监控查询\",\"openType\":0,\"permission\":\"sysMachine:query\",\"pid\":\"1264622039642256361\",\"pids\":\"[0],[1264622039642256351],[1264622039642256361],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_monitor_mgr_machine_monitor\",\"component\":\"system/machine/index\",\"createTime\":1591344158000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256361\",\"name\":\"服务监控\",\"openType\":1,\"pid\":\"1264622039642256351\",\"pids\":\"[0],[1264622039642256351],\",\"router\":\"/machine\",\"sort\":16,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_monitor_mgr_online_user_list\",\"createTime\":1591344226000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256391\",\"name\":\"在线用户列表\",\"openType\":0,\"permission\":\"sysOnlineUser:list\",\"pid\":\"1264622039642256381\",\"pids\":\"[0],[1264622039642256351],[1264622039642256381],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_monitor_mgr_online_user_force_exist\",\"createTime\":1591344256000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256401\",\"name\":\"在线用户强退\",\"openType\":0,\"permission\":\"sysOnlineUser:forceExist\",\"pid\":\"1264622039642256381\",\"pids\":\"[0],[1264622039642256351],[1264622039642256381],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_monitor_mgr_online_user\",\"component\":\"system/onlineUser/index\",\"createTime\":1591344115000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256381\",\"name\":\"在线用户\",\"openType\":1,\"pid\":\"1264622039642256351\",\"pids\":\"[0],[1264622039642256351],\",\"router\":\"/onlineUser\",\"sort\":17,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_monitor_mgr_druid\",\"component\":\"Iframe\",\"createTime\":1593332107000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256411\",\"link\":\"http://localhost:82/druid\",\"name\":\"数据监控\",\"openType\":2,\"pid\":\"1264622039642256351\",\"pids\":\"[0],[1264622039642256351],\",\"router\":\"/druid\",\"sort\":18,\"status\":0,\"type\":1,\"updateTime\":1599961150000,\"updateUser\":1265476890672672808,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_monitor_mgr\",\"component\":\"PageView\",\"createTime\":1591344050000,\"createUser\":1265476890672672808,\"icon\":\"deployment-unit\",\"id\":\"1264622039642256351\",\"name\":\"系统监控\",\"openType\":1,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/monitor\",\"sort\":6,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_notice_mgr_page\",\"createTime\":1593416730000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256441\",\"name\":\"公告查询\",\"openType\":0,\"permission\":\"sysNotice:page\",\"pid\":\"1264622039642256431\",\"pids\":\"[0],[1264622039642256421],[1264622039642256431],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_notice_mgr_add\",\"createTime\":1593416757000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256451\",\"name\":\"公告增加\",\"openType\":0,\"permission\":\"sysNotice:add\",\"pid\":\"1264622039642256431\",\"pids\":\"[0],[1264622039642256421],[1264622039642256431],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_notice_mgr_edit\",\"createTime\":1593416782000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256461\",\"name\":\"公告编辑\",\"openType\":0,\"permission\":\"sysNotice:edit\",\"pid\":\"1264622039642256431\",\"pids\":\"[0],[1264622039642256421],[1264622039642256431],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_notice_mgr_delete\",\"createTime\":1593416771000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256471\",\"name\":\"公告删除\",\"openType\":0,\"permission\":\"sysNotice:delete\",\"pid\":\"1264622039642256431\",\"pids\":\"[0],[1264622039642256421],[1264622039642256431],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_notice_mgr_detail\",\"createTime\":1593416793000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256481\",\"name\":\"公告查看\",\"openType\":0,\"permission\":\"sysNotice:detail\",\"pid\":\"1264622039642256431\",\"pids\":\"[0],[1264622039642256421],[1264622039642256431],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_notice_mgr_changeStatus\",\"createTime\":1593416810000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256491\",\"name\":\"公告修改状态\",\"openType\":0,\"permission\":\"sysNotice:changeStatus\",\"pid\":\"1264622039642256431\",\"pids\":\"[0],[1264622039642256421],[1264622039642256431],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_notice_mgr\",\"component\":\"system/notice/index\",\"createTime\":1593416664000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256431\",\"name\":\"公告管理\",\"openType\":1,\"pid\":\"1264622039642256421\",\"pids\":\"[0],[1264622039642256421],\",\"router\":\"/notice\",\"sort\":19,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_notice_mgr_received_page\",\"createTime\":1593419623000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256511\",\"name\":\"已收公告查询\",\"openType\":0,\"permission\":\"sysNotice:received\",\"pid\":\"1264622039642256501\",\"pids\":\"[0],[1264622039642256421],[1264622039642256501],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_notice_mgr_received\",\"component\":\"system/noticeReceived/index\",\"createTime\":1593419573000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256501\",\"name\":\"已收公告\",\"openType\":1,\"pid\":\"1264622039642256421\",\"pids\":\"[0],[1264622039642256421],\",\"router\":\"/noticeReceived\",\"sort\":20,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_notice\",\"component\":\"PageView\",\"createTime\":1593416513000,\"createUser\":1265476890672672808,\"icon\":\"sound\",\"id\":\"1264622039642256421\",\"name\":\"通知公告\",\"openType\":1,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/notice\",\"sort\":7,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_file_mgr_sys_file_page\",\"createTime\":1592991338000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256541\",\"name\":\"文件查询\",\"openType\":0,\"permission\":\"sysFileInfo:page\",\"pid\":\"1264622039642256531\",\"pids\":\"[0],[1264622039642256521],[1264622039642256531],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_file_mgr_sys_file_list\",\"createTime\":1592991349000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256551\",\"name\":\"文件列表\",\"openType\":0,\"permission\":\"sysFileInfo:list\",\"pid\":\"1264622039642256531\",\"pids\":\"[0],[1264622039642256521],[1264622039642256531],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_file_mgr_sys_file_delete\",\"createTime\":1592991371000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256561\",\"name\":\"文件删除\",\"openType\":0,\"permission\":\"sysFileInfo:delete\",\"pid\":\"1264622039642256531\",\"pids\":\"[0],[1264622039642256521],[1264622039642256531],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_file_mgr_sys_file_detail\",\"createTime\":1592991361000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256571\",\"name\":\"文件详情\",\"openType\":0,\"permission\":\"sysFileInfo:detail\",\"pid\":\"1264622039642256531\",\"pids\":\"[0],[1264622039642256521],[1264622039642256531],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_file_mgr_sys_file_upload\",\"createTime\":1592991269000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256581\",\"name\":\"文件上传\",\"openType\":0,\"permission\":\"sysFileInfo:upload\",\"pid\":\"1264622039642256531\",\"pids\":\"[0],[1264622039642256521],[1264622039642256531],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_file_mgr_sys_file_download\",\"createTime\":1592991295000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256591\",\"name\":\"文件下载\",\"openType\":0,\"permission\":\"sysFileInfo:download\",\"pid\":\"1264622039642256531\",\"pids\":\"[0],[1264622039642256521],[1264622039642256531],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_file_mgr_sys_file_preview\",\"createTime\":1592991319000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256601\",\"name\":\"图片预览\",\"openType\":0,\"permission\":\"sysFileInfo:preview\",\"pid\":\"1264622039642256531\",\"pids\":\"[0],[1264622039642256521],[1264622039642256531],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_file_mgr_sys_file\",\"component\":\"system/file/index\",\"createTime\":1592991177000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256531\",\"name\":\"系统文件\",\"openType\":1,\"pid\":\"1264622039642256521\",\"pids\":\"[0],[1264622039642256521],\",\"router\":\"/file\",\"sort\":21,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_file_mgr\",\"component\":\"PageView\",\"createTime\":1592991070000,\"createUser\":1265476890672672808,\"icon\":\"file\",\"id\":\"1264622039642256521\",\"name\":\"文件管理\",\"openType\":1,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/file\",\"sort\":8,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system_tool\",\"children\":[],\"code\":\"code_gen\",\"component\":\"gen/codeGenerate/index\",\"createTime\":1608898908000,\"createUser\":1265476890672672808,\"icon\":\"thunderbolt\",\"id\":\"1342445437296771074\",\"name\":\"代码生成\",\"openType\":1,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/codeGenerate/index\",\"sort\":100,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_area_mgr_list\",\"createTime\":1621404099000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256741\",\"name\":\"系统区域列表\",\"openType\":0,\"permission\":\"sysArea:list\",\"pid\":\"1264622039642256731\",\"pids\":\"[0],[1264622039642256721],[1264622039642256731],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_area_mgr\",\"component\":\"system/area/index\",\"createTime\":1621403862000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256731\",\"name\":\"系统区域\",\"openType\":1,\"pid\":\"1264622039642256721\",\"pids\":\"[0],[1264622039642256721],\",\"router\":\"/area\",\"sort\":100,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_area\",\"component\":\"PageView\",\"createTime\":1621403740000,\"createUser\":1265476890672672808,\"icon\":\"environment\",\"id\":\"1264622039642256721\",\"name\":\"区域管理\",\"openType\":1,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/area\",\"sort\":100,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_list\",\"createTime\":1593595196000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256641\",\"name\":\"定时任务列表\",\"openType\":0,\"permission\":\"sysTimers:list\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_detail\",\"createTime\":1593595210000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256651\",\"name\":\"定时任务详情\",\"openType\":0,\"permission\":\"sysTimers:detail\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_add\",\"createTime\":1593595223000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256661\",\"name\":\"定时任务增加\",\"openType\":0,\"permission\":\"sysTimers:add\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_delete\",\"createTime\":1593595233000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256671\",\"name\":\"定时任务删除\",\"openType\":0,\"permission\":\"sysTimers:delete\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_edit\",\"createTime\":1593595243000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256681\",\"name\":\"定时任务编辑\",\"openType\":0,\"permission\":\"sysTimers:edit\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_get_action_classes\",\"createTime\":1593595336000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256691\",\"name\":\"定时任务可执行列表\",\"openType\":0,\"permission\":\"sysTimers:getActionClasses\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_start\",\"createTime\":1593595352000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256701\",\"name\":\"定时任务启动\",\"openType\":0,\"permission\":\"sysTimers:start\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_stop\",\"createTime\":1593595363000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256711\",\"name\":\"定时任务关闭\",\"openType\":0,\"permission\":\"sysTimers:stop\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_page\",\"createTime\":1593595183000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256631\",\"name\":\"定时任务查询\",\"openType\":0,\"permission\":\"sysTimers:page\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_timers_mgr\",\"component\":\"system/timers/index\",\"createTime\":1593595133000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256621\",\"name\":\"任务管理\",\"openType\":1,\"pid\":\"1264622039642256611\",\"pids\":\"[0],[1264622039642256611],\",\"router\":\"/timers\",\"sort\":22,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_timers\",\"component\":\"PageView\",\"createTime\":1593595040000,\"createUser\":1265476890672672808,\"icon\":\"dashboard\",\"id\":\"1264622039642256611\",\"name\":\"定时任务\",\"openType\":1,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/timers\",\"sort\":100,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:42:29', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965885009293314, '系统菜单_切换', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysMenu/change', 'com.concise.com.concise.sys.modular.menu.controller.SysMenuController', 'change', 'POST', '{\"application\":\"system_tool\"}', '{\"code\":200,\"data\":[{\"component\":\"gen/codeGenerate/index\",\"hidden\":false,\"id\":\"1342445437296771074\",\"meta\":{\"icon\":\"thunderbolt\",\"show\":true,\"title\":\"代码生成\"},\"name\":\"code_gen\",\"path\":\"/codeGenerate/index\",\"pid\":\"0\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:42:41', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491965891275583489, '代码生成配置_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/codeGenerate/page', 'com.concise.generate.modular.controller.CodeGenerateController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[],\"rows\":[],\"totalPage\":0,\"totalRows\":0},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:42:43', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966055193178114, '系统菜单_切换', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysMenu/change', 'com.concise.com.concise.sys.modular.menu.controller.SysMenuController', 'change', 'POST', '{\"application\":\"system\"}', '{\"code\":200,\"data\":[{\"component\":\"RouteView\",\"hidden\":false,\"id\":\"1264622039642255311\",\"meta\":{\"icon\":\"home\",\"show\":true,\"title\":\"主控面板\"},\"name\":\"system_index\",\"path\":\"/\",\"pid\":\"0\",\"redirect\":\"/analysis\"},{\"component\":\"system/dashboard/Workplace\",\"hidden\":false,\"id\":\"1264622039642255331\",\"meta\":{\"show\":true,\"title\":\"工作台\"},\"name\":\"system_index_workplace\",\"path\":\"workplace\",\"pid\":\"1264622039642255311\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642255341\",\"meta\":{\"icon\":\"team\",\"show\":true,\"title\":\"组织架构\"},\"name\":\"sys_mgr\",\"path\":\"/sys\",\"pid\":\"0\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642255671\",\"meta\":{\"icon\":\"safety-certificate\",\"show\":true,\"title\":\"权限管理\"},\"name\":\"auth_manager\",\"path\":\"/auth\",\"pid\":\"0\"},{\"component\":\"system/user/index\",\"hidden\":false,\"id\":\"1264622039642255351\",\"meta\":{\"show\":true,\"title\":\"用户管理\"},\"name\":\"sys_user_mgr\",\"path\":\"/mgr_user\",\"pid\":\"1264622039642255341\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642255961\",\"meta\":{\"icon\":\"euro\",\"show\":true,\"title\":\"开发管理\"},\"name\":\"system_tools\",\"path\":\"/tools\",\"pid\":\"0\"},{\"component\":\"system/org/index\",\"hidden\":false,\"id\":\"1264622039642255521\",\"meta\":{\"show\":true,\"title\":\"机构管理\"},\"name\":\"sys_org_mgr\",\"path\":\"/org\",\"pid\":\"1264622039642255341\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256281\",\"meta\":{\"icon\":\"read\",\"show\":true,\"title\":\"日志管理\"},\"name\":\"sys_log_mgr\",\"path\":\"/log\",\"pid\":\"0\"},{\"component\":\"system/pos/index\",\"hidden\":false,\"id\":\"1264622039642255601\",\"meta\":{\"show\":true,\"title\":\"职位管理\"},\"name\":\"sys_pos_mgr\",\"path\":\"/pos\",\"pid\":\"1264622039642255341\"},{\"component\":\"system/app/index\",\"hidden\":false,\"id\":\"1264622039642255681\",\"meta\":{\"show\":true,\"title\":\"应用管理\"},\"name\":\"sys_app_mgr\",\"path\":\"/app\",\"pid\":\"1264622039642255671\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256351\",\"meta\":{\"icon\":\"deployment-unit\",\"show\":true,\"title\":\"系统监控\"},\"name\":\"sys_monitor_mgr\",\"path\":\"/monitor\",\"pid\":\"0\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256421\",\"meta\":{\"icon\":\"sound\",\"show\":true,\"title\":\"通知公告\"},\"name\":\"sys_notice\",\"path\":\"/notice\",\"pid\":\"0\"},{\"component\":\"system/menu/index\",\"hidden\":false,\"id\":\"1264622039642255761\",\"meta\":{\"show\":true,\"title\":\"菜单管理\"},\"name\":\"sys_menu_mgr\",\"path\":\"/menu\",\"pid\":\"1264622039642255671\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256521\",\"meta\":{\"icon\":\"file\",\"show\":true,\"title\":\"文件管理\"},\"name\":\"sys_file_mgr\",\"path\":\"/file\",\"pid\":\"0\"},{\"component\":\"system/role/index\",\"hidden\":false,\"id\":\"1264622039642255851\",\"meta\":{\"show\":true,\"title\":\"角色管理\"},\"name\":\"sys_role_mgr\",\"path\":\"/role\",\"pid\":\"1264622039642255671\"},{\"component\":\"system/config/index\",\"hidden\":false,\"id\":\"1264622039642255971\",\"meta\":{\"show\":true,\"title\":\"系统配置\"},\"name\":\"system_tools_config\",\"path\":\"/config\",\"pid\":\"1264622039642255961\"},{\"component\":\"system/email/index\",\"hidden\":false,\"id\":\"1264622039642256041\",\"meta\":{\"show\":true,\"title\":\"邮件发送\"},\"name\":\"sys_email_mgr\",\"path\":\"/email\",\"pid\":\"1264622039642255961\"},{\"component\":\"system/sms/index\",\"hidden\":false,\"id\":\"1264622039642256071\",\"meta\":{\"show\":true,\"title\":\"短信管理\"},\"name\":\"sys_sms_mgr\",\"path\":\"/sms\",\"pid\":\"1264622039642255961\"},{\"component\":\"system/dict/index\",\"hidden\":false,\"id\":\"1264622039642256111\",\"meta\":{\"show\":true,\"title\":\"字典管理\"},\"name\":\"sys_dict_mgr\",\"path\":\"/dict\",\"pid\":\"1264622039642255961\"},{\"component\":\"Iframe\",\"hidden\":false,\"id\":\"1264622039642256271\",\"meta\":{\"link\":\"http://localhost:82/doc.html\",\"show\":true,\"title\":\"接口文档\"},\"name\":\"sys_swagger_mgr\",\"path\":\"/swagger\",\"pid\":\"1264622039642255961\"},{\"component\":\"system/log/vislog/index\",\"hidden\":false,\"id\":\"1264622039642256291\",\"meta\":{\"show\":true,\"title\":\"访问日志\"},\"name\":\"sys_log_mgr_vis_log\",\"path\":\"/vislog\",\"pid\":\"1264622039642256281\"},{\"component\":\"system/log/oplog/index\",\"hidden\":false,\"id\":\"1264622039642256321\",\"meta\":{\"show\":true,\"title\":\"操作日志\"},\"name\":\"sys_log_mgr_op_log\",\"path\":\"/oplog\",\"pid\":\"1264622039642256281\"},{\"component\":\"system/machine/index\",\"hidden\":false,\"id\":\"1264622039642256361\",\"meta\":{\"show\":true,\"title\":\"服务监控\"},\"name\":\"sys_monitor_mgr_machine_monitor\",\"path\":\"/machine\",\"pid\":\"1264622039642256351\"},{\"component\":\"system/onlineUser/index\",\"hidden\":false,\"id\":\"1264622039642256381\",\"meta\":{\"show\":true,\"title\":\"在线用户\"},\"name\":\"sys_monitor_mgr_online_user\",\"path\":\"/onlineUser\",\"pid\":\"1264622039642256351\"},{\"component\":\"Iframe\",\"hidden\":false,\"id\":\"1264622039642256411\",\"meta\":{\"link\":\"http://localhost:82/druid\",\"show\":true,\"title\":\"数据监控\"},\"name\":\"sys_monitor_mgr_druid\",\"path\":\"/druid\",\"pid\":\"1264622039642256351\"},{\"component\":\"system/notice/index\",\"hidden\":false,\"id\":\"1264622039642256431\",\"meta\":{\"show\":true,\"title\":\"公告管理\"},\"name\":\"sys_notice_mgr\",\"path\":\"/notice\",\"pid\":\"1264622039642256421\"},{\"component\":\"system/noticeReceived/index\",\"hidden\":false,\"id\":\"1264622039642256501\",\"meta\":{\"show\":true,\"title\":\"已收公告\"},\"name\":\"sys_notice_mgr_received\",\"path\":\"/noticeReceived\",\"pid\":\"1264622039642256421\"},{\"component\":\"system/file/index\",\"hidden\":false,\"id\":\"1264622039642256531\",\"meta\":{\"show\":true,\"title\":\"系统文件\"},\"name\":\"sys_file_mgr_sys_file\",\"path\":\"/file\",\"pid\":\"1264622039642256521\"},{\"component\":\"system/timers/index\",\"hidden\":false,\"id\":\"1264622039642256621\",\"meta\":{\"show\":true,\"title\":\"任务管理\"},\"name\":\"sys_timers_mgr\",\"path\":\"/timers\",\"pid\":\"1264622039642256611\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256721\",\"meta\":{\"icon\":\"environment\",\"show\":true,\"title\":\"区域管理\"},\"name\":\"sys_area\",\"path\":\"/area\",\"pid\":\"0\"},{\"component\":\"system/area/index\",\"hidden\":false,\"id\":\"1264622039642256731\",\"meta\":{\"show\":true,\"title\":\"系统区域\"},\"name\":\"sys_area_mgr\",\"path\":\"/area\",\"pid\":\"1264622039642256721\"},{\"component\":\"system/dashboard/Analysis\",\"hidden\":false,\"id\":\"1264622039642255321\",\"meta\":{\"show\":true,\"title\":\"分析页\"},\"name\":\"system_index_dashboard\",\"path\":\"analysis\",\"pid\":\"1264622039642255311\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256611\",\"meta\":{\"icon\":\"dashboard\",\"show\":true,\"title\":\"定时任务\"},\"name\":\"sys_timers\",\"path\":\"/timers\",\"pid\":\"0\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:43:22', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966062575153153, '系统菜单_切换', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysMenu/change', 'com.concise.com.concise.sys.modular.menu.controller.SysMenuController', 'change', 'POST', '{\"application\":\"system_tool\"}', '{\"code\":200,\"data\":[{\"component\":\"gen/codeGenerate/index\",\"hidden\":false,\"id\":\"1342445437296771074\",\"meta\":{\"icon\":\"thunderbolt\",\"show\":true,\"title\":\"代码生成\"},\"name\":\"code_gen\",\"path\":\"/codeGenerate/index\",\"pid\":\"0\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:43:23', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966068879192066, '数据库表列表_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/codeGenerate/InformationList', 'com.concise.generate.modular.controller.CodeGenerateController', 'InformationList', 'GET', '', '{\"code\":200,\"data\":[{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"系统应用表\",\"tableName\":\"sys_app\",\"updateTime\":\"2022-02-11 10:27:42\"},{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"中国行政地区表\",\"tableName\":\"sys_area\"},{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"代码生成基础配置\",\"tableName\":\"sys_code_generate\"},{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"代码生成详细配置\",\"tableName\":\"sys_code_generate_config\"},{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"系统参数配置表\",\"tableName\":\"sys_config\",\"updateTime\":\"2022-02-11 10:27:42\"},{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"系统字典值表\",\"tableName\":\"sys_dict_data\",\"updateTime\":\"2022-02-11 10:27:42\"},{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"系统字典类型表\",\"tableName\":\"sys_dict_type\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"员工表\",\"tableName\":\"sys_emp\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"员工附属机构岗位表\",\"tableName\":\"sys_emp_ext_org_pos\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"员工职位关联表\",\"tableName\":\"sys_emp_pos\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"文件信息表\",\"tableName\":\"sys_file_info\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统菜单表\",\"tableName\":\"sys_menu\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"通知表\",\"tableName\":\"sys_notice\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统用户数据范围表\",\"tableName\":\"sys_notice_user\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"第三方认证用户信息表\",\"tableName\":\"sys_oauth_user\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统操作日志表\",\"tableName\":\"sys_op_log\",\"updateTime\":\"2022-02-11 10:43:24\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统组织机构表\",\"tableName\":\"sys_org\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统职位表\",\"tableName\":\"sys_pos\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统角色表\",\"tableName\":\"sys_role\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统角色数据范围表\",\"tableName\":\"sys_role_data_scope\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统角色菜单表\",\"tableName\":\"sys_role_menu\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"短信信息发送表\",\"tableName\":\"sys_sms\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"定时任务\",\"tableName\":\"sys_timers\",\"updateTime\":\"2022-02-11 10:37:12\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统用户表\",\"tableName\":\"sys_user\",\"updateTime\":\"2022-02-11 10:40:30\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统用户数据范围表\",\"tableName\":\"sys_user_data_scope\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统用户角色表\",\"tableName\":\"sys_user_role\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统访问日志表\",\"tableName\":\"sys_vis_log\",\"updateTime\":\"2022-02-11 10:40:30\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:43:25', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966367085817858, '代码生成配置_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/codeGenerate/page', 'com.concise.generate.modular.controller.CodeGenerateController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[],\"rows\":[],\"totalPage\":0,\"totalRows\":0},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:44:36', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966445200535553, '代码生成配置_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/codeGenerate/page', 'com.concise.generate.modular.controller.CodeGenerateController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[],\"rows\":[],\"totalPage\":0,\"totalRows\":0},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:44:55', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966453673029634, '数据库表列表_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/codeGenerate/InformationList', 'com.concise.generate.modular.controller.CodeGenerateController', 'InformationList', 'GET', '', '{\"code\":200,\"data\":[{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"系统应用表\",\"tableName\":\"sys_app\",\"updateTime\":\"2022-02-11 10:27:42\"},{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"中国行政地区表\",\"tableName\":\"sys_area\"},{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"代码生成基础配置\",\"tableName\":\"sys_code_generate\"},{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"代码生成详细配置\",\"tableName\":\"sys_code_generate_config\"},{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"系统参数配置表\",\"tableName\":\"sys_config\",\"updateTime\":\"2022-02-11 10:27:42\"},{\"createTime\":\"2022-02-11 10:44:18\",\"tableComment\":\"系统字典值表\",\"tableName\":\"sys_dict_data\",\"updateTime\":\"2022-02-11 10:44:18\"},{\"createTime\":\"2022-02-11 10:44:46\",\"tableComment\":\"系统字典类型表\",\"tableName\":\"sys_dict_type\",\"updateTime\":\"2022-02-11 10:44:46\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"员工表\",\"tableName\":\"sys_emp\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"员工附属机构岗位表\",\"tableName\":\"sys_emp_ext_org_pos\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"员工职位关联表\",\"tableName\":\"sys_emp_pos\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"文件信息表\",\"tableName\":\"sys_file_info\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统菜单表\",\"tableName\":\"sys_menu\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"通知表\",\"tableName\":\"sys_notice\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统用户数据范围表\",\"tableName\":\"sys_notice_user\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"第三方认证用户信息表\",\"tableName\":\"sys_oauth_user\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统操作日志表\",\"tableName\":\"sys_op_log\",\"updateTime\":\"2022-02-11 10:44:55\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统组织机构表\",\"tableName\":\"sys_org\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统职位表\",\"tableName\":\"sys_pos\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统角色表\",\"tableName\":\"sys_role\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统角色数据范围表\",\"tableName\":\"sys_role_data_scope\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统角色菜单表\",\"tableName\":\"sys_role_menu\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"短信信息发送表\",\"tableName\":\"sys_sms\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"定时任务\",\"tableName\":\"sys_timers\",\"updateTime\":\"2022-02-11 10:37:12\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统用户表\",\"tableName\":\"sys_user\",\"updateTime\":\"2022-02-11 10:40:30\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统用户数据范围表\",\"tableName\":\"sys_user_data_scope\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统用户角色表\",\"tableName\":\"sys_user_role\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统访问日志表\",\"tableName\":\"sys_vis_log\",\"updateTime\":\"2022-02-11 10:40:30\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:44:57', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966637639397377, '系统菜单_切换', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysMenu/change', 'com.concise.com.concise.sys.modular.menu.controller.SysMenuController', 'change', 'POST', '{\"application\":\"system\"}', '{\"code\":200,\"data\":[{\"component\":\"RouteView\",\"hidden\":false,\"id\":\"1264622039642255311\",\"meta\":{\"icon\":\"home\",\"show\":true,\"title\":\"主控面板\"},\"name\":\"system_index\",\"path\":\"/\",\"pid\":\"0\",\"redirect\":\"/analysis\"},{\"component\":\"system/dashboard/Workplace\",\"hidden\":false,\"id\":\"1264622039642255331\",\"meta\":{\"show\":true,\"title\":\"工作台\"},\"name\":\"system_index_workplace\",\"path\":\"workplace\",\"pid\":\"1264622039642255311\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642255341\",\"meta\":{\"icon\":\"team\",\"show\":true,\"title\":\"组织架构\"},\"name\":\"sys_mgr\",\"path\":\"/sys\",\"pid\":\"0\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642255671\",\"meta\":{\"icon\":\"safety-certificate\",\"show\":true,\"title\":\"权限管理\"},\"name\":\"auth_manager\",\"path\":\"/auth\",\"pid\":\"0\"},{\"component\":\"system/user/index\",\"hidden\":false,\"id\":\"1264622039642255351\",\"meta\":{\"show\":true,\"title\":\"用户管理\"},\"name\":\"sys_user_mgr\",\"path\":\"/mgr_user\",\"pid\":\"1264622039642255341\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642255961\",\"meta\":{\"icon\":\"euro\",\"show\":true,\"title\":\"开发管理\"},\"name\":\"system_tools\",\"path\":\"/tools\",\"pid\":\"0\"},{\"component\":\"system/org/index\",\"hidden\":false,\"id\":\"1264622039642255521\",\"meta\":{\"show\":true,\"title\":\"机构管理\"},\"name\":\"sys_org_mgr\",\"path\":\"/org\",\"pid\":\"1264622039642255341\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256281\",\"meta\":{\"icon\":\"read\",\"show\":true,\"title\":\"日志管理\"},\"name\":\"sys_log_mgr\",\"path\":\"/log\",\"pid\":\"0\"},{\"component\":\"system/pos/index\",\"hidden\":false,\"id\":\"1264622039642255601\",\"meta\":{\"show\":true,\"title\":\"职位管理\"},\"name\":\"sys_pos_mgr\",\"path\":\"/pos\",\"pid\":\"1264622039642255341\"},{\"component\":\"system/app/index\",\"hidden\":false,\"id\":\"1264622039642255681\",\"meta\":{\"show\":true,\"title\":\"应用管理\"},\"name\":\"sys_app_mgr\",\"path\":\"/app\",\"pid\":\"1264622039642255671\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256351\",\"meta\":{\"icon\":\"deployment-unit\",\"show\":true,\"title\":\"系统监控\"},\"name\":\"sys_monitor_mgr\",\"path\":\"/monitor\",\"pid\":\"0\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256421\",\"meta\":{\"icon\":\"sound\",\"show\":true,\"title\":\"通知公告\"},\"name\":\"sys_notice\",\"path\":\"/notice\",\"pid\":\"0\"},{\"component\":\"system/menu/index\",\"hidden\":false,\"id\":\"1264622039642255761\",\"meta\":{\"show\":true,\"title\":\"菜单管理\"},\"name\":\"sys_menu_mgr\",\"path\":\"/menu\",\"pid\":\"1264622039642255671\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256521\",\"meta\":{\"icon\":\"file\",\"show\":true,\"title\":\"文件管理\"},\"name\":\"sys_file_mgr\",\"path\":\"/file\",\"pid\":\"0\"},{\"component\":\"system/role/index\",\"hidden\":false,\"id\":\"1264622039642255851\",\"meta\":{\"show\":true,\"title\":\"角色管理\"},\"name\":\"sys_role_mgr\",\"path\":\"/role\",\"pid\":\"1264622039642255671\"},{\"component\":\"system/config/index\",\"hidden\":false,\"id\":\"1264622039642255971\",\"meta\":{\"show\":true,\"title\":\"系统配置\"},\"name\":\"system_tools_config\",\"path\":\"/config\",\"pid\":\"1264622039642255961\"},{\"component\":\"system/email/index\",\"hidden\":false,\"id\":\"1264622039642256041\",\"meta\":{\"show\":true,\"title\":\"邮件发送\"},\"name\":\"sys_email_mgr\",\"path\":\"/email\",\"pid\":\"1264622039642255961\"},{\"component\":\"system/sms/index\",\"hidden\":false,\"id\":\"1264622039642256071\",\"meta\":{\"show\":true,\"title\":\"短信管理\"},\"name\":\"sys_sms_mgr\",\"path\":\"/sms\",\"pid\":\"1264622039642255961\"},{\"component\":\"system/dict/index\",\"hidden\":false,\"id\":\"1264622039642256111\",\"meta\":{\"show\":true,\"title\":\"字典管理\"},\"name\":\"sys_dict_mgr\",\"path\":\"/dict\",\"pid\":\"1264622039642255961\"},{\"component\":\"Iframe\",\"hidden\":false,\"id\":\"1264622039642256271\",\"meta\":{\"link\":\"http://localhost:82/doc.html\",\"show\":true,\"title\":\"接口文档\"},\"name\":\"sys_swagger_mgr\",\"path\":\"/swagger\",\"pid\":\"1264622039642255961\"},{\"component\":\"system/log/vislog/index\",\"hidden\":false,\"id\":\"1264622039642256291\",\"meta\":{\"show\":true,\"title\":\"访问日志\"},\"name\":\"sys_log_mgr_vis_log\",\"path\":\"/vislog\",\"pid\":\"1264622039642256281\"},{\"component\":\"system/log/oplog/index\",\"hidden\":false,\"id\":\"1264622039642256321\",\"meta\":{\"show\":true,\"title\":\"操作日志\"},\"name\":\"sys_log_mgr_op_log\",\"path\":\"/oplog\",\"pid\":\"1264622039642256281\"},{\"component\":\"system/machine/index\",\"hidden\":false,\"id\":\"1264622039642256361\",\"meta\":{\"show\":true,\"title\":\"服务监控\"},\"name\":\"sys_monitor_mgr_machine_monitor\",\"path\":\"/machine\",\"pid\":\"1264622039642256351\"},{\"component\":\"system/onlineUser/index\",\"hidden\":false,\"id\":\"1264622039642256381\",\"meta\":{\"show\":true,\"title\":\"在线用户\"},\"name\":\"sys_monitor_mgr_online_user\",\"path\":\"/onlineUser\",\"pid\":\"1264622039642256351\"},{\"component\":\"Iframe\",\"hidden\":false,\"id\":\"1264622039642256411\",\"meta\":{\"link\":\"http://localhost:82/druid\",\"show\":true,\"title\":\"数据监控\"},\"name\":\"sys_monitor_mgr_druid\",\"path\":\"/druid\",\"pid\":\"1264622039642256351\"},{\"component\":\"system/notice/index\",\"hidden\":false,\"id\":\"1264622039642256431\",\"meta\":{\"show\":true,\"title\":\"公告管理\"},\"name\":\"sys_notice_mgr\",\"path\":\"/notice\",\"pid\":\"1264622039642256421\"},{\"component\":\"system/noticeReceived/index\",\"hidden\":false,\"id\":\"1264622039642256501\",\"meta\":{\"show\":true,\"title\":\"已收公告\"},\"name\":\"sys_notice_mgr_received\",\"path\":\"/noticeReceived\",\"pid\":\"1264622039642256421\"},{\"component\":\"system/file/index\",\"hidden\":false,\"id\":\"1264622039642256531\",\"meta\":{\"show\":true,\"title\":\"系统文件\"},\"name\":\"sys_file_mgr_sys_file\",\"path\":\"/file\",\"pid\":\"1264622039642256521\"},{\"component\":\"system/timers/index\",\"hidden\":false,\"id\":\"1264622039642256621\",\"meta\":{\"show\":true,\"title\":\"任务管理\"},\"name\":\"sys_timers_mgr\",\"path\":\"/timers\",\"pid\":\"1264622039642256611\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256721\",\"meta\":{\"icon\":\"environment\",\"show\":true,\"title\":\"区域管理\"},\"name\":\"sys_area\",\"path\":\"/area\",\"pid\":\"0\"},{\"component\":\"system/area/index\",\"hidden\":false,\"id\":\"1264622039642256731\",\"meta\":{\"show\":true,\"title\":\"系统区域\"},\"name\":\"sys_area_mgr\",\"path\":\"/area\",\"pid\":\"1264622039642256721\"},{\"component\":\"system/dashboard/Analysis\",\"hidden\":false,\"id\":\"1264622039642255321\",\"meta\":{\"show\":true,\"title\":\"分析页\"},\"name\":\"system_index_dashboard\",\"path\":\"analysis\",\"pid\":\"1264622039642255311\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256611\",\"meta\":{\"icon\":\"dashboard\",\"show\":true,\"title\":\"定时任务\"},\"name\":\"sys_timers\",\"path\":\"/timers\",\"pid\":\"0\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:45:40', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966655498743809, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:45:45', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966656878669825, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"account\":\"yubaoshan\",\"birthday\":************,\"email\":\"<EMAIL>\",\"id\":1275735541155614721,\"name\":\"俞宝山\",\"nickName\":\"俞宝山\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"102\",\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"},\"tel\":\"\"},{\"account\":\"xuyuxiang\",\"birthday\":*************,\"id\":1280709549107552257,\"name\":\"徐玉祥\",\"nickName\":\"就是那个锅\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"100\",\"orgId\":1265476890672672770,\"orgName\":\"华夏集团成都分公司\"}}],\"totalPage\":1,\"totalRows\":2},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:45:45', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966656945778690, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"sex\"}', '{\"code\":200,\"data\":[{\"code\":\"1\",\"value\":\"男\"},{\"code\":\"2\",\"value\":\"女\"},{\"code\":\"3\",\"value\":\"未知\"},{\"code\":\"DEFAULT\",\"value\":\"默认常量\"},{\"code\":\"ALIYUN_SMS\",\"value\":\"阿里云短信\"},{\"code\":\"TENCENT_SMS\",\"value\":\"腾讯云短信\"},{\"code\":\"EMAIL\",\"value\":\"邮件配置\"},{\"code\":\"FILE_PATH\",\"value\":\"文件上传路径\"},{\"code\":\"OAUTH\",\"value\":\"Oauth配置\"},{\"code\":\"0\",\"value\":\"正常\"},{\"code\":\"1\",\"value\":\"停用\"},{\"code\":\"2\",\"value\":\"删除\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:45:45', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966656945778691, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"common_status\"}', '{\"code\":200,\"data\":[{\"code\":\"1\",\"value\":\"男\"},{\"code\":\"2\",\"value\":\"女\"},{\"code\":\"3\",\"value\":\"未知\"},{\"code\":\"DEFAULT\",\"value\":\"默认常量\"},{\"code\":\"ALIYUN_SMS\",\"value\":\"阿里云短信\"},{\"code\":\"TENCENT_SMS\",\"value\":\"腾讯云短信\"},{\"code\":\"EMAIL\",\"value\":\"邮件配置\"},{\"code\":\"FILE_PATH\",\"value\":\"文件上传路径\"},{\"code\":\"OAUTH\",\"value\":\"Oauth配置\"},{\"code\":\"0\",\"value\":\"正常\"},{\"code\":\"1\",\"value\":\"停用\"},{\"code\":\"2\",\"value\":\"删除\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:45:45', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966884226723842, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:46:39', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966884369330178, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"sex\"}', '{\"code\":200,\"data\":[{\"code\":\"1\",\"value\":\"男\"},{\"code\":\"2\",\"value\":\"女\"},{\"code\":\"3\",\"value\":\"未知\"},{\"code\":\"DEFAULT\",\"value\":\"默认常量\"},{\"code\":\"ALIYUN_SMS\",\"value\":\"阿里云短信\"},{\"code\":\"TENCENT_SMS\",\"value\":\"腾讯云短信\"},{\"code\":\"EMAIL\",\"value\":\"邮件配置\"},{\"code\":\"FILE_PATH\",\"value\":\"文件上传路径\"},{\"code\":\"OAUTH\",\"value\":\"Oauth配置\"},{\"code\":\"0\",\"value\":\"正常\"},{\"code\":\"1\",\"value\":\"停用\"},{\"code\":\"2\",\"value\":\"删除\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:46:39', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966884419661825, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"account\":\"yubaoshan\",\"birthday\":************,\"email\":\"<EMAIL>\",\"id\":1275735541155614721,\"name\":\"俞宝山\",\"nickName\":\"俞宝山\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"102\",\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"},\"tel\":\"\"},{\"account\":\"xuyuxiang\",\"birthday\":*************,\"id\":1280709549107552257,\"name\":\"徐玉祥\",\"nickName\":\"就是那个锅\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"100\",\"orgId\":1265476890672672770,\"orgName\":\"华夏集团成都分公司\"}}],\"totalPage\":1,\"totalRows\":2},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:46:39', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966884490964994, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"common_status\"}', '{\"code\":200,\"data\":[{\"code\":\"1\",\"value\":\"男\"},{\"code\":\"2\",\"value\":\"女\"},{\"code\":\"3\",\"value\":\"未知\"},{\"code\":\"DEFAULT\",\"value\":\"默认常量\"},{\"code\":\"ALIYUN_SMS\",\"value\":\"阿里云短信\"},{\"code\":\"TENCENT_SMS\",\"value\":\"腾讯云短信\"},{\"code\":\"EMAIL\",\"value\":\"邮件配置\"},{\"code\":\"FILE_PATH\",\"value\":\"文件上传路径\"},{\"code\":\"OAUTH\",\"value\":\"Oauth配置\"},{\"code\":\"0\",\"value\":\"正常\"},{\"code\":\"1\",\"value\":\"停用\"},{\"code\":\"2\",\"value\":\"删除\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:46:39', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966900920053761, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:46:43', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966902308368386, '系统机构_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/page', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"hxjt\",\"createTime\":1585212653000,\"createUser\":1265476890672672808,\"id\":\"1265476890651701250\",\"name\":\"华夏集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"remark\":\"华夏集团总公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj\",\"createTime\":1585212942000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672769\",\"name\":\"华夏集团北京分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团北京分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd\",\"createTime\":1585212962000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672770\",\"name\":\"华夏集团成都分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团成都分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_yfb\",\"createTime\":1585212996000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672771\",\"name\":\"研发部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司研发部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_qhb\",\"createTime\":1585213026000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672772\",\"name\":\"企划部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司企划部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb\",\"createTime\":1585213055000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672773\",\"name\":\"市场部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司市场部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_cwb\",\"createTime\":1585213081000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672774\",\"name\":\"财务部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司财务部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb_2b\",\"createTime\":1586158610000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672775\",\"name\":\"市场部二部\",\"pid\":\"1265476890672672773\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],[1265476890672672773],\",\"remark\":\"华夏集团成都分公司市场部二部\",\"sort\":100,\"status\":0}],\"totalPage\":1,\"totalRows\":8},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:46:44', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966902555832321, '系统机构_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/page', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"hxjt\",\"createTime\":1585212653000,\"createUser\":1265476890672672808,\"id\":\"1265476890651701250\",\"name\":\"华夏集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"remark\":\"华夏集团总公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj\",\"createTime\":1585212942000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672769\",\"name\":\"华夏集团北京分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团北京分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd\",\"createTime\":1585212962000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672770\",\"name\":\"华夏集团成都分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团成都分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_yfb\",\"createTime\":1585212996000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672771\",\"name\":\"研发部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司研发部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_qhb\",\"createTime\":1585213026000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672772\",\"name\":\"企划部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司企划部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb\",\"createTime\":1585213055000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672773\",\"name\":\"市场部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司市场部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_cwb\",\"createTime\":1585213081000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672774\",\"name\":\"财务部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司财务部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb_2b\",\"createTime\":1586158610000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672775\",\"name\":\"市场部二部\",\"pid\":\"1265476890672672773\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],[1265476890672672773],\",\"remark\":\"华夏集团成都分公司市场部二部\",\"sort\":100,\"status\":0}],\"totalPage\":1,\"totalRows\":8},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:46:44', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491966922935955458, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:46:48', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491967342798368769, '系统职位_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysPos/page', 'com.concise.com.concise.sys.modular.pos.controller.SysPosController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"zjl\",\"createTime\":1585222134000,\"createUser\":1265476890672672808,\"id\":1265476890672672787,\"name\":\"总经理\",\"remark\":\"总经理职位\",\"sort\":100,\"status\":0,\"updateTime\":1591102864000,\"updateUser\":1265476890672672808},{\"code\":\"fzjl\",\"createTime\":1585222197000,\"createUser\":1265476890672672808,\"id\":1265476890672672788,\"name\":\"副总经理\",\"remark\":\"副总经理职位\",\"sort\":100,\"status\":0},{\"code\":\"bmjl\",\"createTime\":1585222309000,\"createUser\":1265476890672672808,\"id\":1265476890672672789,\"name\":\"部门经理\",\"remark\":\"部门经理职位\",\"sort\":100,\"status\":0},{\"code\":\"gzry\",\"createTime\":1590550320000,\"createUser\":1265476890672672808,\"id\":1265476890672672790,\"name\":\"工作人员\",\"remark\":\"工作人员职位\",\"sort\":100,\"status\":0,\"updateTime\":*************,\"updateUser\":1265476890672672808}],\"totalPage\":1,\"totalRows\":4},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 10:48:29', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982017313296385, '系统职位_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysPos/page', 'com.concise.com.concise.sys.modular.pos.controller.SysPosController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"zjl\",\"createTime\":1585222134000,\"createUser\":1265476890672672808,\"id\":1265476890672672787,\"name\":\"总经理\",\"remark\":\"总经理职位\",\"sort\":100,\"status\":0,\"updateTime\":1591102864000,\"updateUser\":1265476890672672808},{\"code\":\"fzjl\",\"createTime\":1585222197000,\"createUser\":1265476890672672808,\"id\":1265476890672672788,\"name\":\"副总经理\",\"remark\":\"副总经理职位\",\"sort\":100,\"status\":0},{\"code\":\"bmjl\",\"createTime\":1585222309000,\"createUser\":1265476890672672808,\"id\":1265476890672672789,\"name\":\"部门经理\",\"remark\":\"部门经理职位\",\"sort\":100,\"status\":0},{\"code\":\"gzry\",\"createTime\":1590550320000,\"createUser\":1265476890672672808,\"id\":1265476890672672790,\"name\":\"工作人员\",\"remark\":\"工作人员职位\",\"sort\":100,\"status\":0,\"updateTime\":*************,\"updateUser\":1265476890672672808}],\"totalPage\":1,\"totalRows\":4},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:46:47', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982027115384833, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"account\":\"yubaoshan\",\"birthday\":************,\"email\":\"<EMAIL>\",\"id\":1275735541155614721,\"name\":\"俞宝山\",\"nickName\":\"俞宝山\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"102\",\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"},\"tel\":\"\"},{\"account\":\"xuyuxiang\",\"birthday\":*************,\"id\":1280709549107552257,\"name\":\"徐玉祥\",\"nickName\":\"就是那个锅\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"100\",\"orgId\":1265476890672672770,\"orgName\":\"华夏集团成都分公司\"}}],\"totalPage\":1,\"totalRows\":2},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:46:50', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982027543203841, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:46:50', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982028499505154, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"sex\"}', '{\"code\":200,\"data\":[{\"code\":\"1\",\"value\":\"男\"},{\"code\":\"2\",\"value\":\"女\"},{\"code\":\"3\",\"value\":\"未知\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:46:50', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982028541448193, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"common_status\"}', '{\"code\":200,\"data\":[{\"code\":\"0\",\"value\":\"正常\"},{\"code\":\"1\",\"value\":\"停用\"},{\"code\":\"2\",\"value\":\"删除\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:46:50', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982036342853634, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:46:52', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982036342853635, '系统职位_列表', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysPos/list', 'com.concise.com.concise.sys.modular.pos.controller.SysPosController', 'list', 'GET', '{}', '{\"code\":200,\"data\":[{\"code\":\"zjl\",\"createTime\":1585222134000,\"createUser\":1265476890672672808,\"id\":1265476890672672787,\"name\":\"总经理\",\"remark\":\"总经理职位\",\"sort\":100,\"status\":0,\"updateTime\":1591102864000,\"updateUser\":1265476890672672808},{\"code\":\"fzjl\",\"createTime\":1585222197000,\"createUser\":1265476890672672808,\"id\":1265476890672672788,\"name\":\"副总经理\",\"remark\":\"副总经理职位\",\"sort\":100,\"status\":0},{\"code\":\"bmjl\",\"createTime\":1585222309000,\"createUser\":1265476890672672808,\"id\":1265476890672672789,\"name\":\"部门经理\",\"remark\":\"部门经理职位\",\"sort\":100,\"status\":0},{\"code\":\"gzry\",\"createTime\":1590550320000,\"createUser\":1265476890672672808,\"id\":1265476890672672790,\"name\":\"工作人员\",\"remark\":\"工作人员职位\",\"sort\":100,\"status\":0,\"updateTime\":*************,\"updateUser\":1265476890672672808}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:46:52', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982036405768194, '系统组织机构_列表', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/list', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'list', 'GET', '{}', '{\"code\":200,\"data\":[{\"code\":\"hxjt\",\"createTime\":1585212653000,\"createUser\":1265476890672672808,\"id\":\"1265476890651701250\",\"name\":\"华夏集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"remark\":\"华夏集团总公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj\",\"createTime\":1585212942000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672769\",\"name\":\"华夏集团北京分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团北京分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd\",\"createTime\":1585212962000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672770\",\"name\":\"华夏集团成都分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团成都分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_yfb\",\"createTime\":1585212996000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672771\",\"name\":\"研发部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司研发部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_qhb\",\"createTime\":1585213026000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672772\",\"name\":\"企划部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司企划部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb\",\"createTime\":1585213055000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672773\",\"name\":\"市场部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司市场部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_cwb\",\"createTime\":1585213081000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672774\",\"name\":\"财务部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司财务部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb_2b\",\"createTime\":1586158610000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672775\",\"name\":\"市场部二部\",\"pid\":\"1265476890672672773\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],[1265476890672672773],\",\"remark\":\"华夏集团成都分公司市场部二部\",\"sort\":100,\"status\":0}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:46:52', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982206002450433, '系统用户_增加', 1, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/add', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'add', 'POST', '{\"account\":\"junze\",\"name\":\"junze\",\"password\":\"123456\",\"phone\":\"***********\",\"sex\":1,\"sysEmpParam\":{\"extIds\":[],\"id\":\"1491982204861599746\",\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\",\"posIdList\":[1265476890672672787]}}', '{\"code\":200,\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:47:32', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982207818584066, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"account\":\"yubaoshan\",\"birthday\":************,\"email\":\"<EMAIL>\",\"id\":1275735541155614721,\"name\":\"俞宝山\",\"nickName\":\"俞宝山\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"102\",\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"},\"tel\":\"\"},{\"account\":\"xuyuxiang\",\"birthday\":*************,\"id\":1280709549107552257,\"name\":\"徐玉祥\",\"nickName\":\"就是那个锅\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"100\",\"orgId\":1265476890672672770,\"orgName\":\"华夏集团成都分公司\"}},{\"account\":\"junze\",\"id\":1491982204861599746,\"name\":\"junze\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"}}],\"totalPage\":1,\"totalRows\":3},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:47:33', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982217041858561, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:47:35', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982218325315585, '系统组织机构_列表', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/list', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'list', 'GET', '{}', '{\"code\":200,\"data\":[{\"code\":\"hxjt\",\"createTime\":1585212653000,\"createUser\":1265476890672672808,\"id\":\"1265476890651701250\",\"name\":\"华夏集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"remark\":\"华夏集团总公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj\",\"createTime\":1585212942000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672769\",\"name\":\"华夏集团北京分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团北京分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd\",\"createTime\":1585212962000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672770\",\"name\":\"华夏集团成都分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团成都分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_yfb\",\"createTime\":1585212996000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672771\",\"name\":\"研发部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司研发部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_qhb\",\"createTime\":1585213026000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672772\",\"name\":\"企划部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司企划部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb\",\"createTime\":1585213055000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672773\",\"name\":\"市场部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司市场部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_cwb\",\"createTime\":1585213081000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672774\",\"name\":\"财务部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司财务部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb_2b\",\"createTime\":1586158610000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672775\",\"name\":\"市场部二部\",\"pid\":\"1265476890672672773\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],[1265476890672672773],\",\"remark\":\"华夏集团成都分公司市场部二部\",\"sort\":100,\"status\":0}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:47:35', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982218329509890, '系统职位_列表', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysPos/list', 'com.concise.com.concise.sys.modular.pos.controller.SysPosController', 'list', 'GET', '{}', '{\"code\":200,\"data\":[{\"code\":\"zjl\",\"createTime\":1585222134000,\"createUser\":1265476890672672808,\"id\":1265476890672672787,\"name\":\"总经理\",\"remark\":\"总经理职位\",\"sort\":100,\"status\":0,\"updateTime\":1591102864000,\"updateUser\":1265476890672672808},{\"code\":\"fzjl\",\"createTime\":1585222197000,\"createUser\":1265476890672672808,\"id\":1265476890672672788,\"name\":\"副总经理\",\"remark\":\"副总经理职位\",\"sort\":100,\"status\":0},{\"code\":\"bmjl\",\"createTime\":1585222309000,\"createUser\":1265476890672672808,\"id\":1265476890672672789,\"name\":\"部门经理\",\"remark\":\"部门经理职位\",\"sort\":100,\"status\":0},{\"code\":\"gzry\",\"createTime\":1590550320000,\"createUser\":1265476890672672808,\"id\":1265476890672672790,\"name\":\"工作人员\",\"remark\":\"工作人员职位\",\"sort\":100,\"status\":0,\"updateTime\":*************,\"updateUser\":1265476890672672808}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:47:35', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982218946072577, '系统用户_查看', 6, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/detail', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'detail', 'GET', '{\"id\":\"1491982204861599746\"}', '{\"code\":200,\"data\":{\"account\":\"junze\",\"id\":1491982204861599746,\"name\":\"junze\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"extOrgPos\":[],\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\",\"positions\":[{\"posId\":1265476890672672787,\"posCode\":\"zjl\",\"posName\":\"总经理\"}]}},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:47:35', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982266941493250, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{\"sysEmpParam\":{\"orgId\":1265476890672672769}}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1,2],\"rows\":[{\"account\":\"yubaoshan\",\"birthday\":************,\"email\":\"<EMAIL>\",\"id\":1275735541155614721,\"name\":\"俞宝山\",\"nickName\":\"俞宝山\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"102\",\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"},\"tel\":\"\"},{\"account\":\"junze\",\"id\":1491982204861599746,\"name\":\"junze\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"}},{\"account\":\"xuyuxiang\",\"birthday\":*************,\"id\":1280709549107552257,\"name\":\"徐玉祥\",\"nickName\":\"就是那个锅\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"100\",\"orgId\":1265476890672672770,\"orgName\":\"华夏集团成都分公司\"}}],\"totalPage\":2,\"totalRows\":16},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:47:47', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982282410086401, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{\"sysEmpParam\":{\"orgId\":1265476890651701250}}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1,2,3],\"rows\":[{\"account\":\"yubaoshan\",\"birthday\":************,\"email\":\"<EMAIL>\",\"id\":1275735541155614721,\"name\":\"俞宝山\",\"nickName\":\"俞宝山\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"102\",\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"},\"tel\":\"\"},{\"account\":\"xuyuxiang\",\"birthday\":*************,\"id\":1280709549107552257,\"name\":\"徐玉祥\",\"nickName\":\"就是那个锅\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"100\",\"orgId\":1265476890672672770,\"orgName\":\"华夏集团成都分公司\"}},{\"account\":\"junze\",\"id\":1491982204861599746,\"name\":\"junze\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"}}],\"totalPage\":3,\"totalRows\":21},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:47:50', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982289230024706, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{\"sysEmpParam\":{\"orgId\":1265476890672672769}}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1,2],\"rows\":[{\"account\":\"yubaoshan\",\"birthday\":************,\"email\":\"<EMAIL>\",\"id\":1275735541155614721,\"name\":\"俞宝山\",\"nickName\":\"俞宝山\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"102\",\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"},\"tel\":\"\"},{\"account\":\"junze\",\"id\":1491982204861599746,\"name\":\"junze\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"}},{\"account\":\"xuyuxiang\",\"birthday\":*************,\"id\":1280709549107552257,\"name\":\"徐玉祥\",\"nickName\":\"就是那个锅\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"100\",\"orgId\":1265476890672672770,\"orgName\":\"华夏集团成都分公司\"}}],\"totalPage\":2,\"totalRows\":16},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:47:52', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982301846491137, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{\"sysEmpParam\":{\"orgId\":1265476890672672772}}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[],\"rows\":[],\"totalPage\":0,\"totalRows\":0},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:47:55', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982307693350913, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{\"sysEmpParam\":{\"orgId\":1265476890672672771}}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[],\"rows\":[],\"totalPage\":0,\"totalRows\":0},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:47:57', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982311585665025, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{\"sysEmpParam\":{\"orgId\":1265476890672672770}}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1,2],\"rows\":[{\"account\":\"xuyuxiang\",\"birthday\":*************,\"id\":1280709549107552257,\"name\":\"徐玉祥\",\"nickName\":\"就是那个锅\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"100\",\"orgId\":1265476890672672770,\"orgName\":\"华夏集团成都分公司\"}},{\"account\":\"yubaoshan\",\"birthday\":************,\"email\":\"<EMAIL>\",\"id\":1275735541155614721,\"name\":\"俞宝山\",\"nickName\":\"俞宝山\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"102\",\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"},\"tel\":\"\"},{\"account\":\"junze\",\"id\":1491982204861599746,\"name\":\"junze\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"}}],\"totalPage\":2,\"totalRows\":13},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:47:57', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982318636290050, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{\"sysEmpParam\":{\"orgId\":1265476890672672773}}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"account\":\"yubaoshan\",\"birthday\":************,\"email\":\"<EMAIL>\",\"id\":1275735541155614721,\"name\":\"俞宝山\",\"nickName\":\"俞宝山\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"102\",\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"},\"tel\":\"\"},{\"account\":\"xuyuxiang\",\"birthday\":*************,\"id\":1280709549107552257,\"name\":\"徐玉祥\",\"nickName\":\"就是那个锅\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"100\",\"orgId\":1265476890672672770,\"orgName\":\"华夏集团成都分公司\"}},{\"account\":\"junze\",\"id\":1491982204861599746,\"name\":\"junze\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"}}],\"totalPage\":1,\"totalRows\":3},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:47:59', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982322453106689, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{\"sysEmpParam\":{\"orgId\":1265476890672672775}}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[],\"rows\":[],\"totalPage\":0,\"totalRows\":0},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:48:00', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982328962666497, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{\"sysEmpParam\":{\"orgId\":1265476890672672774}}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[],\"rows\":[],\"totalPage\":0,\"totalRows\":0},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:48:02', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982356577964034, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:48:08', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982357983055873, '系统机构_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/page', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"hxjt\",\"createTime\":1585212653000,\"createUser\":1265476890672672808,\"id\":\"1265476890651701250\",\"name\":\"华夏集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"remark\":\"华夏集团总公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj\",\"createTime\":1585212942000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672769\",\"name\":\"华夏集团北京分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团北京分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd\",\"createTime\":1585212962000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672770\",\"name\":\"华夏集团成都分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团成都分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_yfb\",\"createTime\":1585212996000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672771\",\"name\":\"研发部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司研发部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_qhb\",\"createTime\":1585213026000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672772\",\"name\":\"企划部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司企划部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb\",\"createTime\":1585213055000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672773\",\"name\":\"市场部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司市场部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_cwb\",\"createTime\":1585213081000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672774\",\"name\":\"财务部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司财务部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb_2b\",\"createTime\":1586158610000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672775\",\"name\":\"市场部二部\",\"pid\":\"1265476890672672773\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],[1265476890672672773],\",\"remark\":\"华夏集团成都分公司市场部二部\",\"sort\":100,\"status\":0}],\"totalPage\":1,\"totalRows\":8},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:48:08', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982358209548289, '系统机构_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/page', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"hxjt\",\"createTime\":1585212653000,\"createUser\":1265476890672672808,\"id\":\"1265476890651701250\",\"name\":\"华夏集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"remark\":\"华夏集团总公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj\",\"createTime\":1585212942000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672769\",\"name\":\"华夏集团北京分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团北京分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd\",\"createTime\":1585212962000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672770\",\"name\":\"华夏集团成都分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团成都分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_yfb\",\"createTime\":1585212996000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672771\",\"name\":\"研发部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司研发部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_qhb\",\"createTime\":1585213026000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672772\",\"name\":\"企划部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司企划部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb\",\"createTime\":1585213055000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672773\",\"name\":\"市场部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司市场部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_cwb\",\"createTime\":1585213081000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672774\",\"name\":\"财务部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司财务部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb_2b\",\"createTime\":1586158610000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672775\",\"name\":\"市场部二部\",\"pid\":\"1265476890672672773\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],[1265476890672672773],\",\"remark\":\"华夏集团成都分公司市场部二部\",\"sort\":100,\"status\":0}],\"totalPage\":1,\"totalRows\":8},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:48:09', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982366354886657, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:48:10', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982422881521665, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 11:48:24', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491982559716495361, '系统组织机构_增加', 1, 'N', '[com.concise.com.concise.sys.modular.org.service.impl.SysOrgServiceImpl.checkParam(SysOrgServiceImpl.java:364), com.concise.com.concise.sys.modular.org.service.impl.SysOrgServiceImpl.add(SysOrgServiceImpl.java:143), com.concise.com.concise.sys.modular.org.service.impl.SysOrgServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687), com.concise.com.concise.sys.modular.org.service.impl.SysOrgServiceImpl$$EnhancerBySpringCGLIB$$1.add(<generated>), com.concise.com.concise.sys.modular.org.controller.SysOrgController.add(SysOrgController.java:72), com.concise.com.concise.sys.modular.org.controller.SysOrgController$$FastClassBySpringCGLIB$$1.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749), org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:62), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749), org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:55), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749), org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:56), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749), org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:56), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749), org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749), org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691), com.concise.com.concise.sys.modular.org.controller.SysOrgController$$EnhancerBySpringCGLIB$$1.add(<generated>), sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method), sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62), sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.lang.reflect.Method.invoke(Method.java:498), org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190), org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138), org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105), org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:879), org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793), org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87), org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040), org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943), org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006), org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909), javax.servlet.http.HttpServlet.service(HttpServlet.java:660), org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883), javax.servlet.http.HttpServlet.service(HttpServlet.java:741), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), com.concise.com.concise.sys.core.filter.RequestNoFilter.doFilter(RequestNoFilter.java:34), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), com.concise.com.concise.sys.core.filter.xss.XssFilter.doFilter(XssFilter.java:29), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320), org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126), org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), com.concise.com.concise.sys.core.filter.security.JwtAuthenticationTokenFilter.doFilter(JwtAuthenticationTokenFilter.java:72), com.concise.com.concise.sys.core.filter.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:43), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92), org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215), org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178), org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358), org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202), org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:96), org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002), org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541), org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139), org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92), org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74), org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343), org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373), org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65), org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868), org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590), org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49), java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149), java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624), org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61), java.lang.Thread.run(Thread.java:748)]', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/add', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'add', 'POST', '{\"code\":\"dfjt\",\"name\":\"大方集团\",\"pid\":\"0\",\"sort\":100}', NULL, '2022-02-11 11:48:57', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491985522308579329, '系统组织机构_增加', 1, 'N', '[com.concise.com.concise.sys.modular.org.service.impl.SysOrgServiceImpl.fillPids(SysOrgServiceImpl.java:435), com.concise.com.concise.sys.modular.org.service.impl.SysOrgServiceImpl.add(SysOrgServiceImpl.java:167), com.concise.com.concise.sys.modular.org.service.impl.SysOrgServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687), com.concise.com.concise.sys.modular.org.service.impl.SysOrgServiceImpl$$EnhancerBySpringCGLIB$$1.add(<generated>), com.concise.com.concise.sys.modular.org.controller.SysOrgController.add(SysOrgController.java:72), com.concise.com.concise.sys.modular.org.controller.SysOrgController$$FastClassBySpringCGLIB$$1.invoke(<generated>), org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749), org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:62), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749), org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:55), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749), org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:56), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749), org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:56), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749), org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95), org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186), org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749), org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691), com.concise.com.concise.sys.modular.org.controller.SysOrgController$$EnhancerBySpringCGLIB$$1.add(<generated>), sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method), sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62), sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43), java.lang.reflect.Method.invoke(Method.java:498), org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190), org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138), org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105), org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:879), org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793), org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87), org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040), org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943), org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006), org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909), javax.servlet.http.HttpServlet.service(HttpServlet.java:660), org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883), javax.servlet.http.HttpServlet.service(HttpServlet.java:741), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), com.concise.com.concise.sys.core.filter.RequestNoFilter.doFilter(RequestNoFilter.java:34), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), com.concise.com.concise.sys.core.filter.xss.XssFilter.doFilter(XssFilter.java:29), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320), org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:126), org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:90), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:118), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:158), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), com.concise.com.concise.sys.core.filter.security.JwtAuthenticationTokenFilter.doFilter(JwtAuthenticationTokenFilter.java:72), com.concise.com.concise.sys.core.filter.security.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:43), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:92), org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:77), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119), org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334), org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215), org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178), org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358), org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201), org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119), org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193), org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166), org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202), org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:96), org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002), org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541), org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139), org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92), org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74), org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343), org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373), org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65), org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868), org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590), org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49), java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149), java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624), org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61), java.lang.Thread.run(Thread.java:748)]', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/add', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'add', 'POST', '{\"code\":\"dfjt\",\"name\":\"大方集团\",\"pid\":\"0\",\"sort\":100}', NULL, '2022-02-11 12:00:43', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491986023976718338, '系统组织机构_增加', 1, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/add', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'add', 'POST', '{\"code\":\"dfjt\",\"name\":\"大方集团\",\"pid\":\"0\",\"sort\":100}', '{\"code\":200,\"message\":\"请求成功\",\"success\":true}', '2022-02-11 12:02:43', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491986024576503810, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100},{\"children\":[],\"id\":\"1491986023494373377\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"大方集团\",\"value\":\"1491986023494373377\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 12:02:43', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491986026031927297, '系统机构_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/page', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"hxjt\",\"createTime\":1585212653000,\"createUser\":1265476890672672808,\"id\":\"1265476890651701250\",\"name\":\"华夏集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"remark\":\"华夏集团总公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj\",\"createTime\":1585212942000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672769\",\"name\":\"华夏集团北京分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团北京分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd\",\"createTime\":1585212962000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672770\",\"name\":\"华夏集团成都分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团成都分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_yfb\",\"createTime\":1585212996000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672771\",\"name\":\"研发部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司研发部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_qhb\",\"createTime\":1585213026000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672772\",\"name\":\"企划部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司企划部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb\",\"createTime\":1585213055000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672773\",\"name\":\"市场部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司市场部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_cwb\",\"createTime\":1585213081000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672774\",\"name\":\"财务部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司财务部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb_2b\",\"createTime\":1586158610000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672775\",\"name\":\"市场部二部\",\"pid\":\"1265476890672672773\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],[1265476890672672773],\",\"remark\":\"华夏集团成都分公司市场部二部\",\"sort\":100,\"status\":0},{\"code\":\"dfjt\",\"id\":\"1491986023494373377\",\"name\":\"大方集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"sort\":100,\"status\":0}],\"totalPage\":1,\"totalRows\":9},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 12:02:43', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1491986027843866626, '系统机构_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/page', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"hxjt\",\"createTime\":1585212653000,\"createUser\":1265476890672672808,\"id\":\"1265476890651701250\",\"name\":\"华夏集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"remark\":\"华夏集团总公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj\",\"createTime\":1585212942000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672769\",\"name\":\"华夏集团北京分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团北京分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd\",\"createTime\":1585212962000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672770\",\"name\":\"华夏集团成都分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团成都分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_yfb\",\"createTime\":1585212996000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672771\",\"name\":\"研发部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司研发部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_qhb\",\"createTime\":1585213026000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672772\",\"name\":\"企划部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司企划部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb\",\"createTime\":1585213055000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672773\",\"name\":\"市场部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司市场部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_cwb\",\"createTime\":1585213081000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672774\",\"name\":\"财务部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司财务部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb_2b\",\"createTime\":1586158610000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672775\",\"name\":\"市场部二部\",\"pid\":\"1265476890672672773\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],[1265476890672672773],\",\"remark\":\"华夏集团成都分公司市场部二部\",\"sort\":100,\"status\":0},{\"code\":\"dfjt\",\"id\":\"1491986023494373377\",\"name\":\"大方集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"sort\":100,\"status\":0}],\"totalPage\":1,\"totalRows\":9},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 12:02:43', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492016569649881090, '系统字典类型_树', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/tree', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'tree', 'GET', '', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[],\"code\":\"0\",\"id\":\"1265216617500102656\",\"name\":\"正常\",\"pid\":\"1265216211667636226\"},{\"children\":[],\"code\":\"1\",\"id\":\"1265216617500102657\",\"name\":\"停用\",\"pid\":\"1265216211667636226\"},{\"children\":[],\"code\":\"2\",\"id\":\"1265216938389524482\",\"name\":\"删除\",\"pid\":\"1265216211667636226\"}],\"code\":\"common_status\",\"id\":\"1265216211667636226\",\"name\":\"通用状态\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"1\",\"id\":\"1265216536659087357\",\"name\":\"男\",\"pid\":\"1265216211667636234\"},{\"children\":[],\"code\":\"2\",\"id\":\"1265216536659087358\",\"name\":\"女\",\"pid\":\"1265216211667636234\"},{\"children\":[],\"code\":\"3\",\"id\":\"1265216536659087359\",\"name\":\"未知\",\"pid\":\"1265216211667636234\"}],\"code\":\"sex\",\"id\":\"1265216211667636234\",\"name\":\"性别\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"DEFAULT\",\"id\":\"1265216536659087361\",\"name\":\"默认常量\",\"pid\":\"1265216211667636235\"},{\"children\":[],\"code\":\"ALIYUN_SMS\",\"id\":\"1265216536659087363\",\"name\":\"阿里云短信\",\"pid\":\"1265216211667636235\"},{\"children\":[],\"code\":\"TENCENT_SMS\",\"id\":\"1265216536659087364\",\"name\":\"腾讯云短信\",\"pid\":\"1265216211667636235\"},{\"children\":[],\"code\":\"EMAIL\",\"id\":\"1265216536659087365\",\"name\":\"邮件配置\",\"pid\":\"1265216211667636235\"},{\"children\":[],\"code\":\"FILE_PATH\",\"id\":\"1265216536659087366\",\"name\":\"文件上传路径\",\"pid\":\"1265216211667636235\"},{\"children\":[],\"code\":\"OAUTH\",\"id\":\"1265216536659087367\",\"name\":\"Oauth配置\",\"pid\":\"1265216211667636235\"}],\"code\":\"consts_type\",\"id\":\"1265216211667636235\",\"name\":\"常量的分类\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"N\",\"id\":\"1265217669028892673\",\"name\":\"否\",\"pid\":\"1265217074079453185\"},{\"children\":[],\"code\":\"Y\",\"id\":\"1265217706584690689\",\"name\":\"是\",\"pid\":\"1265217074079453185\"}],\"code\":\"yes_or_no\",\"id\":\"1265217074079453185\",\"name\":\"是否\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"1\",\"id\":\"1265220776437731330\",\"name\":\"登录\",\"pid\":\"1265217846770913282\"},{\"children\":[],\"code\":\"2\",\"id\":\"1265220806070489090\",\"name\":\"登出\",\"pid\":\"1265217846770913282\"}],\"code\":\"vis_type\",\"id\":\"1265217846770913282\",\"name\":\"访问类型\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"0\",\"id\":\"1265221129564573697\",\"name\":\"目录\",\"pid\":\"1265221049302372354\"},{\"children\":[],\"code\":\"1\",\"id\":\"1265221163119005697\",\"name\":\"菜单\",\"pid\":\"1265221049302372354\"},{\"children\":[],\"code\":\"2\",\"id\":\"1265221188091891713\",\"name\":\"按钮\",\"pid\":\"1265221049302372354\"}],\"code\":\"menu_type\",\"id\":\"1265221049302372354\",\"name\":\"菜单类型\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"0\",\"id\":\"1265466389204967426\",\"name\":\"未发送\",\"pid\":\"1265466149622128641\"},{\"children\":[],\"code\":\"1\",\"id\":\"1265466432670539778\",\"name\":\"发送成功\",\"pid\":\"1265466149622128641\"},{\"children\":[],\"code\":\"2\",\"id\":\"1265466486097584130\",\"name\":\"发送失败\",\"pid\":\"1265466149622128641\"},{\"children\":[],\"code\":\"3\",\"id\":\"1265466530477514754\",\"name\":\"失效\",\"pid\":\"1265466149622128641\"}],\"code\":\"send_type\",\"id\":\"1265466149622128641\",\"name\":\"发送类型\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"0\",\"id\":\"1265466835009150978\",\"name\":\"无\",\"pid\":\"1265466752209395713\"},{\"children\":[],\"code\":\"1\",\"id\":\"1265466874758569986\",\"name\":\"组件\",\"pid\":\"1265466752209395713\"},{\"children\":[],\"code\":\"2\",\"id\":\"1265466925476093953\",\"name\":\"内链\",\"pid\":\"1265466752209395713\"},{\"children\":[],\"code\":\"3\",\"id\":\"1265466962209808385\",\"name\":\"外链\",\"pid\":\"1265466752209395713\"}],\"code\":\"open_type\",\"id\":\"1265466752209395713\",\"name\":\"打开方式\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"1\",\"id\":\"1265467428423475202\",\"name\":\"系统权重\",\"pid\":\"1265467337566461954\"},{\"children\":[],\"code\":\"2\",\"id\":\"1265467503090475009\",\"name\":\"业务权重\",\"pid\":\"1265467337566461954\"}],\"code\":\"menu_weight\",\"id\":\"1265467337566461954\",\"name\":\"菜单权重\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"1\",\"id\":\"1265468138431062018\",\"name\":\"全部数据\",\"pid\":\"1265468028632571905\"},{\"children\":[],\"code\":\"2\",\"id\":\"1265468194928336897\",\"name\":\"本部门及以下数据\",\"pid\":\"1265468028632571905\"},{\"children\":[],\"code\":\"3\",\"id\":\"1265468241992622082\",\"name\":\"本部门数据\",\"pid\":\"1265468028632571905\"},{\"children\":[],\"code\":\"4\",\"id\":\"1265468273634451457\",\"name\":\"仅本人数据\",\"pid\":\"1265468028632571905\"},{\"children\":[],\"code\":\"5\",\"id\":\"1265468302046666753\",\"name\":\"自定义数据\",\"pid\":\"1265468028632571905\"}],\"code\":\"data_scope_type\",\"id\":\"1265468028632571905\",\"name\":\"数据范围类型\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"1\",\"id\":\"1265468508100239362\",\"name\":\"app\",\"pid\":\"1265468437904367618\"},{\"children\":[],\"code\":\"2\",\"id\":\"1265468543433056258\",\"name\":\"pc\",\"pid\":\"1265468437904367618\"},{\"children\":[],\"code\":\"3\",\"id\":\"1265468576874242050\",\"name\":\"其他\",\"pid\":\"1265468437904367618\"}],\"code\":\"sms_send_source\",\"id\":\"1265468437904367618\",\"name\":\"短信发送来源\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"0\",\"id\":\"1275617233011335170\",\"name\":\"其它\",\"pid\":\"1275617093517172738\"},{\"children\":[],\"code\":\"1\",\"id\":\"1275617295355469826\",\"name\":\"增加\",\"pid\":\"1275617093517172738\"},{\"children\":[],\"code\":\"2\",\"id\":\"1275617348610547714\",\"name\":\"删除\",\"pid\":\"1275617093517172738\"},{\"children\":[],\"code\":\"3\",\"id\":\"1275617395515449346\",\"name\":\"编辑\",\"pid\":\"1275617093517172738\"},{\"children\":[],\"code\":\"4\",\"id\":\"1275617433612312577\",\"name\":\"更新\",\"pid\":\"1275617093517172738\"},{\"children\":[],\"code\":\"5\",\"id\":\"1275617472707420161\",\"name\":\"查询\",\"pid\":\"1275617093517172738\"},{\"children\":[],\"code\":\"6\",\"id\":\"1275617502973517826\",\"name\":\"详情\",\"pid\":\"1275617093517172738\"},{\"children\":[],\"code\":\"7\",\"id\":\"1275617536959963137\",\"name\":\"树\",\"pid\":\"1275617093517172738\"},{\"children\":[],\"code\":\"8\",\"id\":\"1275617619524837377\",\"name\":\"导入\",\"pid\":\"1275617093517172738\"},{\"children\":[],\"code\":\"9\",\"id\":\"1275617651816783873\",\"name\":\"导出\",\"pid\":\"1275617093517172738\"},{\"children\":[],\"code\":\"10\",\"id\":\"1275617683475390465\",\"name\":\"授权\",\"pid\":\"1275617093517172738\"},{\"children\":[],\"code\":\"11\",\"id\":\"1275617709928865793\",\"name\":\"强退\",\"pid\":\"1275617093517172738\"},{\"children\":[],\"code\":\"12\",\"id\":\"1275617739091861505\",\"name\":\"清空\",\"pid\":\"1275617093517172738\"},{\"children\":[],\"code\":\"13\",\"id\":\"1275617788601425921\",\"name\":\"修改状态\",\"pid\":\"1275617093517172738\"}],\"code\":\"op_type\",\"id\":\"1275617093517172738\",\"name\":\"操作类型\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"1\",\"id\":\"1277774590944317441\",\"name\":\"阿里云\",\"pid\":\"1277774529430654977\"},{\"children\":[],\"code\":\"2\",\"id\":\"1277774666055913474\",\"name\":\"腾讯云\",\"pid\":\"1277774529430654977\"},{\"children\":[],\"code\":\"3\",\"id\":\"1277774695168577538\",\"name\":\"minio\",\"pid\":\"1277774529430654977\"},{\"children\":[],\"code\":\"4\",\"id\":\"1277774726835572737\",\"name\":\"本地\",\"pid\":\"1277774529430654977\"}],\"code\":\"file_storage_location\",\"id\":\"1277774529430654977\",\"name\":\"文件存储位置\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"1\",\"id\":\"1278607123583868929\",\"name\":\"运行\",\"pid\":\"1278606951432855553\"},{\"children\":[],\"code\":\"2\",\"id\":\"1278607162943217666\",\"name\":\"停止\",\"pid\":\"1278606951432855553\"}],\"code\":\"run_status\",\"id\":\"1278606951432855553\",\"name\":\"运行状态\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"1\",\"id\":\"1278939265862004738\",\"name\":\"通知\",\"pid\":\"1278911800547147777\"},{\"children\":[],\"code\":\"2\",\"id\":\"1278939319922388994\",\"name\":\"公告\",\"pid\":\"1278911800547147777\"}],\"code\":\"notice_type\",\"id\":\"1278911800547147777\",\"name\":\"通知公告类型\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"0\",\"id\":\"1278939399001796609\",\"name\":\"草稿\",\"pid\":\"1278911952657776642\"},{\"children\":[],\"code\":\"1\",\"id\":\"1278939432686252034\",\"name\":\"发布\",\"pid\":\"1278911952657776642\"},{\"children\":[],\"code\":\"2\",\"id\":\"1278939458804183041\",\"name\":\"撤回\",\"pid\":\"1278911952657776642\"},{\"children\":[],\"code\":\"3\",\"id\":\"1278939485878415362\",\"name\":\"删除\",\"pid\":\"1278911952657776642\"}],\"code\":\"notice_status\",\"id\":\"1278911952657776642\",\"name\":\"通知公告状态\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"1\",\"id\":\"1342446007168466945\",\"name\":\"下载压缩包\",\"pid\":\"1342445962104864770\"},{\"children\":[],\"code\":\"2\",\"id\":\"1342446035433881601\",\"name\":\"生成到本项目\",\"pid\":\"1342445962104864770\"}],\"code\":\"code_gen_create_type\",\"id\":\"1342445962104864770\",\"name\":\"代码生成方式\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"input\",\"id\":\"1358094655567454210\",\"name\":\"输入框\",\"pid\":\"1358094419419750401\"},{\"children\":[],\"code\":\"datepicker\",\"id\":\"1358094740510498817\",\"name\":\"时间选择\",\"pid\":\"1358094419419750401\"},{\"children\":[],\"code\":\"select\",\"id\":\"1358094793149014017\",\"name\":\"下拉框\",\"pid\":\"1358094419419750401\"},{\"children\":[],\"code\":\"radio\",\"id\":\"1358095496009506817\",\"name\":\"单选框\",\"pid\":\"1358094419419750401\"},{\"children\":[],\"code\":\"checkbox\",\"id\":\"1358460475682406401\",\"name\":\"多选框\",\"pid\":\"1358094419419750401\"},{\"children\":[],\"code\":\"inputnumber\",\"id\":\"1358460819019743233\",\"name\":\"数字输入框\",\"pid\":\"1358094419419750401\"},{\"children\":[],\"code\":\"textarea\",\"id\":\"1360529773814083586\",\"name\":\"文本域\",\"pid\":\"1358094419419750401\"}],\"code\":\"code_gen_effect_type\",\"id\":\"1358094419419750401\",\"name\":\"代码生成作用类型\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"eq\",\"id\":\"1358458689433190402\",\"name\":\"等于\",\"pid\":\"1358457818733428737\"},{\"children\":[],\"code\":\"like\",\"id\":\"1358458785168179202\",\"name\":\"模糊\",\"pid\":\"1358457818733428737\"},{\"children\":[],\"code\":\"gt\",\"id\":\"1358756511688761346\",\"name\":\"大于\",\"pid\":\"1358457818733428737\"},{\"children\":[],\"code\":\"lt\",\"id\":\"1358756547159990274\",\"name\":\"小于\",\"pid\":\"1358457818733428737\"},{\"children\":[],\"code\":\"ne\",\"id\":\"1358756609990664193\",\"name\":\"不等于\",\"pid\":\"1358457818733428737\"},{\"children\":[],\"code\":\"ge\",\"id\":\"1358756685030957057\",\"name\":\"大于等于\",\"pid\":\"1358457818733428737\"},{\"children\":[],\"code\":\"le\",\"id\":\"1358756800525312001\",\"name\":\"小于等于\",\"pid\":\"1358457818733428737\"},{\"children\":[],\"code\":\"isNotNull\",\"id\":\"1360606105914732545\",\"name\":\"不为空\",\"pid\":\"1358457818733428737\"}],\"code\":\"code_gen_query_type\",\"id\":\"1358457818733428737\",\"name\":\"代码生成查询类型\",\"pid\":\"0\"},{\"children\":[{\"children\":[],\"code\":\"Long\",\"id\":\"1358470210267725826\",\"name\":\"Long\",\"pid\":\"1358470065111252994\"},{\"children\":[],\"code\":\"String\",\"id\":\"1358470239351029762\",\"name\":\"String\",\"pid\":\"1358470065111252994\"},{\"children\":[],\"code\":\"Date\",\"id\":\"1358470265640927233\",\"name\":\"Date\",\"pid\":\"1358470065111252994\"},{\"children\":[],\"code\":\"Integer\",\"id\":\"1358470300168437761\",\"name\":\"Integer\",\"pid\":\"1358470065111252994\"},{\"children\":[],\"code\":\"boolean\",\"id\":\"1358470697377415169\",\"name\":\"boolean\",\"pid\":\"1358470065111252994\"},{\"children\":[],\"code\":\"int\",\"id\":\"1358471133434036226\",\"name\":\"int\",\"pid\":\"1358470065111252994\"},{\"children\":[],\"code\":\"double\",\"id\":\"1358471188291338241\",\"name\":\"double\",\"pid\":\"1358470065111252994\"}],\"code\":\"code_gen_java_type\",\"id\":\"1358470065111252994\",\"name\":\"代码生成java类型\",\"pid\":\"0\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:04:05', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492016571960942594, '系统职位_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysPos/page', 'com.concise.com.concise.sys.modular.pos.controller.SysPosController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"zjl\",\"createTime\":1585222134000,\"createUser\":1265476890672672808,\"id\":1265476890672672787,\"name\":\"总经理\",\"remark\":\"总经理职位\",\"sort\":100,\"status\":0,\"updateTime\":1591102864000,\"updateUser\":1265476890672672808},{\"code\":\"fzjl\",\"createTime\":1585222197000,\"createUser\":1265476890672672808,\"id\":1265476890672672788,\"name\":\"副总经理\",\"remark\":\"副总经理职位\",\"sort\":100,\"status\":0},{\"code\":\"bmjl\",\"createTime\":1585222309000,\"createUser\":1265476890672672808,\"id\":1265476890672672789,\"name\":\"部门经理\",\"remark\":\"部门经理职位\",\"sort\":100,\"status\":0},{\"code\":\"gzry\",\"createTime\":1590550320000,\"createUser\":1265476890672672808,\"id\":1265476890672672790,\"name\":\"工作人员\",\"remark\":\"工作人员职位\",\"sort\":100,\"status\":0,\"updateTime\":*************,\"updateUser\":1265476890672672808}],\"totalPage\":1,\"totalRows\":4},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:04:06', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017303015546881, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100},{\"children\":[],\"id\":\"1491986023494373377\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"大方集团\",\"value\":\"1491986023494373377\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:00', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017303149764610, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"account\":\"yubaoshan\",\"birthday\":************,\"email\":\"<EMAIL>\",\"id\":1275735541155614721,\"name\":\"俞宝山\",\"nickName\":\"俞宝山\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"102\",\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"},\"tel\":\"\"},{\"account\":\"xuyuxiang\",\"birthday\":*************,\"id\":1280709549107552257,\"name\":\"徐玉祥\",\"nickName\":\"就是那个锅\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"100\",\"orgId\":1265476890672672770,\"orgName\":\"华夏集团成都分公司\"}},{\"account\":\"junze\",\"id\":1491982204861599746,\"name\":\"junze\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"}}],\"totalPage\":1,\"totalRows\":3},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:00', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017303606943745, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"sex\"}', '{\"code\":200,\"data\":[{\"code\":\"1\",\"value\":\"男\"},{\"code\":\"2\",\"value\":\"女\"},{\"code\":\"3\",\"value\":\"未知\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:00', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017303669858306, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"common_status\"}', '{\"code\":200,\"data\":[{\"code\":\"0\",\"value\":\"正常\"},{\"code\":\"1\",\"value\":\"停用\"},{\"code\":\"2\",\"value\":\"删除\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:00', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017312482091009, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100},{\"children\":[],\"id\":\"1491986023494373377\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"大方集团\",\"value\":\"1491986023494373377\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:02', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017313845239809, '系统机构_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/page', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"hxjt\",\"createTime\":1585212653000,\"createUser\":1265476890672672808,\"id\":\"1265476890651701250\",\"name\":\"华夏集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"remark\":\"华夏集团总公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj\",\"createTime\":1585212942000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672769\",\"name\":\"华夏集团北京分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团北京分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd\",\"createTime\":1585212962000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672770\",\"name\":\"华夏集团成都分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团成都分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_yfb\",\"createTime\":1585212996000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672771\",\"name\":\"研发部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司研发部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_qhb\",\"createTime\":1585213026000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672772\",\"name\":\"企划部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司企划部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb\",\"createTime\":1585213055000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672773\",\"name\":\"市场部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司市场部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_cwb\",\"createTime\":1585213081000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672774\",\"name\":\"财务部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司财务部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb_2b\",\"createTime\":1586158610000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672775\",\"name\":\"市场部二部\",\"pid\":\"1265476890672672773\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],[1265476890672672773],\",\"remark\":\"华夏集团成都分公司市场部二部\",\"sort\":100,\"status\":0},{\"code\":\"dfjt\",\"id\":\"1491986023494373377\",\"name\":\"大方集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"sort\":100,\"status\":0}],\"totalPage\":1,\"totalRows\":9},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:03', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017314042372097, '系统机构_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/page', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"hxjt\",\"createTime\":1585212653000,\"createUser\":1265476890672672808,\"id\":\"1265476890651701250\",\"name\":\"华夏集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"remark\":\"华夏集团总公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj\",\"createTime\":1585212942000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672769\",\"name\":\"华夏集团北京分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团北京分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd\",\"createTime\":1585212962000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672770\",\"name\":\"华夏集团成都分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团成都分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_yfb\",\"createTime\":1585212996000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672771\",\"name\":\"研发部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司研发部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_qhb\",\"createTime\":1585213026000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672772\",\"name\":\"企划部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司企划部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb\",\"createTime\":1585213055000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672773\",\"name\":\"市场部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司市场部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_cwb\",\"createTime\":1585213081000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672774\",\"name\":\"财务部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司财务部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb_2b\",\"createTime\":1586158610000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672775\",\"name\":\"市场部二部\",\"pid\":\"1265476890672672773\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],[1265476890672672773],\",\"remark\":\"华夏集团成都分公司市场部二部\",\"sort\":100,\"status\":0},{\"code\":\"dfjt\",\"id\":\"1491986023494373377\",\"name\":\"大方集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"sort\":100,\"status\":0}],\"totalPage\":1,\"totalRows\":9},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:03', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017331754917890, '系统职位_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysPos/page', 'com.concise.com.concise.sys.modular.pos.controller.SysPosController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"zjl\",\"createTime\":1585222134000,\"createUser\":1265476890672672808,\"id\":1265476890672672787,\"name\":\"总经理\",\"remark\":\"总经理职位\",\"sort\":100,\"status\":0,\"updateTime\":1591102864000,\"updateUser\":1265476890672672808},{\"code\":\"fzjl\",\"createTime\":1585222197000,\"createUser\":1265476890672672808,\"id\":1265476890672672788,\"name\":\"副总经理\",\"remark\":\"副总经理职位\",\"sort\":100,\"status\":0},{\"code\":\"bmjl\",\"createTime\":1585222309000,\"createUser\":1265476890672672808,\"id\":1265476890672672789,\"name\":\"部门经理\",\"remark\":\"部门经理职位\",\"sort\":100,\"status\":0},{\"code\":\"gzry\",\"createTime\":1590550320000,\"createUser\":1265476890672672808,\"id\":1265476890672672790,\"name\":\"工作人员\",\"remark\":\"工作人员职位\",\"sort\":100,\"status\":0,\"updateTime\":*************,\"updateUser\":1265476890672672808}],\"totalPage\":1,\"totalRows\":4},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:07', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017341892550657, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"yes_or_no\"}', '{\"code\":200,\"data\":[{\"code\":\"N\",\"value\":\"否\"},{\"code\":\"Y\",\"value\":\"是\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:09', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017343083732994, '系统应用_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysApp/page', 'com.concise.com.concise.sys.modular.app.controller.SysAppController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"active\":\"Y\",\"code\":\"system\",\"createTime\":1585134420000,\"createUser\":1265476890672672808,\"id\":1265476890672672821,\"name\":\"系统应用\",\"status\":0,\"updateTime\":1597476185000,\"updateUser\":1280709549107552257},{\"active\":\"N\",\"code\":\"system_tool\",\"createTime\":1608898812000,\"createUser\":1265476890672672808,\"id\":1342445032647098369,\"name\":\"系统工具\",\"status\":0}],\"totalPage\":1,\"totalRows\":2},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:10', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017343171813378, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"common_status\"}', '{\"code\":200,\"data\":[{\"code\":\"0\",\"value\":\"正常\"},{\"code\":\"1\",\"value\":\"停用\"},{\"code\":\"2\",\"value\":\"删除\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:10', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017362343976962, '系统菜单_列表', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysMenu/list', 'com.concise.com.concise.sys.modular.menu.controller.SysMenuController', 'list', 'GET', '{}', '{\"code\":200,\"data\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"system_index_workplace\",\"component\":\"system/dashboard/Workplace\",\"createTime\":1590344628000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255331\",\"name\":\"工作台\",\"openType\":0,\"pid\":\"1264622039642255311\",\"pids\":\"[0],[1264622039642255311],\",\"router\":\"workplace\",\"sort\":2,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"system_index_dashboard\",\"component\":\"system/dashboard/Analysis\",\"createTime\":1590344515000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255321\",\"name\":\"分析页\",\"openType\":0,\"pid\":\"1264622039642255311\",\"pids\":\"[0],[1264622039642255311],\",\"router\":\"analysis\",\"sort\":100,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"system_index\",\"component\":\"RouteView\",\"createTime\":1590344364000,\"createUser\":1265476890672672808,\"icon\":\"home\",\"id\":\"1264622039642255311\",\"name\":\"主控面板\",\"openType\":0,\"pid\":\"0\",\"pids\":\"[0],\",\"redirect\":\"/analysis\",\"router\":\"/\",\"sort\":1,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_page\",\"createTime\":1585298209000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255361\",\"name\":\"用户查询\",\"openType\":0,\"permission\":\"sysUser:page\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_edit\",\"createTime\":1593663623000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255371\",\"name\":\"用户编辑\",\"openType\":0,\"permission\":\"sysUser:edit\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_add\",\"createTime\":1585298255000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255381\",\"name\":\"用户增加\",\"openType\":0,\"permission\":\"sysUser:add\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_delete\",\"createTime\":1585298278000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255391\",\"name\":\"用户删除\",\"openType\":0,\"permission\":\"sysUser:delete\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_detail\",\"createTime\":1585298305000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255401\",\"name\":\"用户详情\",\"openType\":0,\"permission\":\"sysUser:detail\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_export\",\"createTime\":1593663719000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255411\",\"name\":\"用户导出\",\"openType\":0,\"permission\":\"sysUser:export\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_selector\",\"createTime\":1593754214000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255421\",\"name\":\"用户选择器\",\"openType\":0,\"permission\":\"sysUser:selector\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_grant_role\",\"createTime\":1585704121000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255431\",\"name\":\"用户授权角色\",\"openType\":0,\"permission\":\"sysUser:grantRole\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_own_role\",\"createTime\":1590733642000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255441\",\"name\":\"用户拥有角色\",\"openType\":0,\"permission\":\"sysUser:ownRole\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_grant_data\",\"createTime\":1585704133000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255451\",\"name\":\"用户授权数据\",\"openType\":0,\"permission\":\"sysUser:grantData\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_own_data\",\"createTime\":1590733661000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255461\",\"name\":\"用户拥有数据\",\"openType\":0,\"permission\":\"sysUser:ownData\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_update_info\",\"createTime\":1585729172000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255471\",\"name\":\"用户更新信息\",\"openType\":0,\"permission\":\"sysUser:updateInfo\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_update_pwd\",\"createTime\":1585729225000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255481\",\"name\":\"用户修改密码\",\"openType\":0,\"permission\":\"sysUser:updatePwd\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_change_status\",\"createTime\":1592881994000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255491\",\"name\":\"用户修改状态\",\"openType\":0,\"permission\":\"sysUser:changeStatus\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_update_avatar\",\"createTime\":1593663702000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255501\",\"name\":\"用户修改头像\",\"openType\":0,\"permission\":\"sysUser:updateAvatar\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_user_mgr_reset_pwd\",\"createTime\":1590735711000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255511\",\"name\":\"用户重置密码\",\"openType\":0,\"permission\":\"sysUser:resetPwd\",\"pid\":\"1264622039642255351\",\"pids\":\"[0],[1264622039642255341],[1264622039642255351],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_user_mgr\",\"component\":\"system/user/index\",\"createTime\":1585296621000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255351\",\"name\":\"用户管理\",\"openType\":1,\"pid\":\"1264622039642255341\",\"pids\":\"[0],[1264622039642255341],\",\"router\":\"/mgr_user\",\"sort\":3,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_org_mgr_page\",\"createTime\":1585300657000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255531\",\"name\":\"机构查询\",\"openType\":0,\"permission\":\"sysOrg:page\",\"pid\":\"1264622039642255521\",\"pids\":\"[0],[1264622039642255341],[1264622039642255521]\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_org_mgr_list\",\"createTime\":1593662066000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255541\",\"name\":\"机构列表\",\"openType\":0,\"permission\":\"sysOrg:list\",\"pid\":\"1264622039642255521\",\"pids\":\"[0],[1264622039642255341],[1264622039642255521]\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_org_mgr_add\",\"createTime\":1585300793000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255551\",\"name\":\"机构增加\",\"openType\":0,\"permission\":\"sysOrg:add\",\"pid\":\"1264622039642255521\",\"pids\":\"[0],[1264622039642255341],[1264622039642255521]\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_org_mgr_edit\",\"createTime\":1593662077000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255561\",\"name\":\"机构编辑\",\"openType\":0,\"permission\":\"sysOrg:edit\",\"pid\":\"1264622039642255521\",\"pids\":\"[0],[1264622039642255341],[1264622039642255521]\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_org_mgr_delete\",\"createTime\":1585300848000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255571\",\"name\":\"机构删除\",\"openType\":0,\"permission\":\"sysOrg:delete\",\"pid\":\"1264622039642255521\",\"pids\":\"[0],[1264622039642255341],[1264622039642255521]\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_org_mgr_detail\",\"createTime\":1585300875000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255581\",\"name\":\"机构详情\",\"openType\":0,\"permission\":\"sysOrg:detail\",\"pid\":\"1264622039642255521\",\"pids\":\"[0],[1264622039642255341],[1264622039642255521]\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_org_mgr_tree\",\"createTime\":1585300918000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255591\",\"name\":\"机构树\",\"openType\":0,\"permission\":\"sysOrg:tree\",\"pid\":\"1264622039642255521\",\"pids\":\"[0],[1264622039642255341],[1264622039642255521]\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_org_mgr\",\"component\":\"system/org/index\",\"createTime\":1585300539000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255521\",\"name\":\"机构管理\",\"openType\":1,\"pid\":\"1264622039642255341\",\"pids\":\"[0],[1264622039642255341],\",\"router\":\"/org\",\"sort\":4,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_pos_mgr_page\",\"createTime\":1585305708000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255611\",\"name\":\"职位查询\",\"openType\":0,\"permission\":\"sysPos:page\",\"pid\":\"1264622039642255601\",\"pids\":\"[0],[1264622039642255341],[1264622039642255601],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_pos_mgr_list\",\"createTime\":1593662157000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255621\",\"name\":\"职位列表\",\"openType\":0,\"permission\":\"sysPos:list\",\"pid\":\"1264622039642255601\",\"pids\":\"[0],[1264622039642255341],[1264622039642255601],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_pos_mgr_add\",\"createTime\":1585305740000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255631\",\"name\":\"职位增加\",\"openType\":0,\"permission\":\"sysPos:add\",\"pid\":\"1264622039642255601\",\"pids\":\"[0],[1264622039642255341],[1264622039642255601],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_pos_mgr_edit\",\"createTime\":1593662168000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255641\",\"name\":\"职位编辑\",\"openType\":0,\"permission\":\"sysPos:edit\",\"pid\":\"1264622039642255601\",\"pids\":\"[0],[1264622039642255341],[1264622039642255601],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_pos_mgr_delete\",\"createTime\":1585305759000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255651\",\"name\":\"职位删除\",\"openType\":0,\"permission\":\"sysPos:delete\",\"pid\":\"1264622039642255601\",\"pids\":\"[0],[1264622039642255341],[1264622039642255601],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_pos_mgr_detail\",\"createTime\":1585305780000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255661\",\"name\":\"职位详情\",\"openType\":0,\"permission\":\"sysPos:detail\",\"pid\":\"1264622039642255601\",\"pids\":\"[0],[1264622039642255341],[1264622039642255601],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_pos_mgr\",\"component\":\"system/pos/index\",\"createTime\":1585305511000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255601\",\"name\":\"职位管理\",\"openType\":1,\"pid\":\"1264622039642255341\",\"pids\":\"[0],[1264622039642255341],\",\"router\":\"/pos\",\"sort\":5,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_mgr\",\"component\":\"PageView\",\"createTime\":1585295896000,\"createUser\":1265476890672672808,\"icon\":\"team\",\"id\":\"1264622039642255341\",\"name\":\"组织架构\",\"openType\":0,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/sys\",\"sort\":2,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_app_mgr_page\",\"createTime\":1585298518000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255691\",\"name\":\"应用查询\",\"openType\":0,\"permission\":\"sysApp:page\",\"pid\":\"1264622039642255681\",\"pids\":\"[0],[1264622039642255671],[1264622039642255681],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_app_mgr_list\",\"createTime\":1593655499000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255701\",\"name\":\"应用列表\",\"openType\":0,\"permission\":\"sysApp:list\",\"pid\":\"1264622039642255681\",\"pids\":\"[0],[1264622039642255671],[1264622039642255681],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_app_mgr_add\",\"createTime\":1585298650000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255711\",\"name\":\"应用增加\",\"openType\":0,\"permission\":\"sysApp:add\",\"pid\":\"1264622039642255681\",\"pids\":\"[0],[1264622039642255671],[1264622039642255681],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_app_mgr_edit\",\"createTime\":1593655474000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255721\",\"name\":\"应用编辑\",\"openType\":0,\"permission\":\"sysApp:edit\",\"pid\":\"1264622039642255681\",\"pids\":\"[0],[1264622039642255671],[1264622039642255681],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_app_mgr_delete\",\"createTime\":1585300469000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255731\",\"name\":\"应用删除\",\"openType\":0,\"permission\":\"sysApp:delete\",\"pid\":\"1264622039642255681\",\"pids\":\"[0],[1264622039642255671],[1264622039642255681],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_app_mgr_detail\",\"createTime\":1585300496000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255741\",\"name\":\"应用详情\",\"openType\":0,\"permission\":\"sysApp:detail\",\"pid\":\"1264622039642255681\",\"pids\":\"[0],[1264622039642255671],[1264622039642255681],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_app_mgr_set_as_default\",\"createTime\":1585300496000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255751\",\"name\":\"设为默认应用\",\"openType\":0,\"permission\":\"sysApp:setAsDefault\",\"pid\":\"1264622039642255681\",\"pids\":\"[0],[1264622039642255671],[1264622039642255681],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_app_mgr\",\"component\":\"system/app/index\",\"createTime\":1585298421000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255681\",\"name\":\"应用管理\",\"openType\":1,\"pid\":\"1264622039642255671\",\"pids\":\"[0],[1264622039642255671],\",\"router\":\"/app\",\"sort\":6,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_menu_mgr_list\",\"createTime\":1585305920000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255771\",\"name\":\"菜单列表\",\"openType\":0,\"permission\":\"sysMenu:list\",\"pid\":\"1264622039642255761\",\"pids\":\"[0],[1264622039642255671],[1264622039642255761],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_menu_mgr_add\",\"createTime\":1585305937000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255781\",\"name\":\"菜单增加\",\"openType\":0,\"permission\":\"sysMenu:add\",\"pid\":\"1264622039642255761\",\"pids\":\"[0],[1264622039642255671],[1264622039642255761],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_menu_mgr_edit\",\"createTime\":1593661920000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255791\",\"name\":\"菜单编辑\",\"openType\":0,\"permission\":\"sysMenu:edit\",\"pid\":\"1264622039642255761\",\"pids\":\"[0],[1264622039642255671],[1264622039642255761],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_menu_mgr_delete\",\"createTime\":1585305961000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255801\",\"name\":\"菜单删除\",\"openType\":0,\"permission\":\"sysMenu:delete\",\"pid\":\"1264622039642255761\",\"pids\":\"[0],[1264622039642255671],[1264622039642255761],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_menu_mgr_detail\",\"createTime\":1585305982000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255811\",\"name\":\"菜单详情\",\"openType\":0,\"permission\":\"sysMenu:detail\",\"pid\":\"1264622039642255761\",\"pids\":\"[0],[1264622039642255671],[1264622039642255761],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_menu_mgr_grant_tree\",\"createTime\":1591149031000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255821\",\"name\":\"菜单授权树\",\"openType\":0,\"permission\":\"sysMenu:treeForGrant\",\"pid\":\"1264622039642255761\",\"pids\":\"[0],[1264622039642255671],[1264622039642255761],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_menu_mgr_tree\",\"createTime\":1585306070000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255831\",\"name\":\"菜单树\",\"openType\":0,\"permission\":\"sysMenu:tree\",\"pid\":\"1264622039642255761\",\"pids\":\"[0],[1264622039642255671],[1264622039642255761],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_menu_mgr_change\",\"createTime\":1591149103000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255841\",\"name\":\"菜单切换\",\"openType\":0,\"permission\":\"sysMenu:change\",\"pid\":\"1264622039642255761\",\"pids\":\"[0],[1264622039642255671],[1264622039642255761],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_menu_mgr\",\"component\":\"system/menu/index\",\"createTime\":1585305875000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255761\",\"name\":\"菜单管理\",\"openType\":1,\"pid\":\"1264622039642255671\",\"pids\":\"[0],[1264622039642255671],\",\"router\":\"/menu\",\"sort\":7,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_page\",\"createTime\":1585382529000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255861\",\"name\":\"角色查询\",\"openType\":0,\"permission\":\"sysRole:page\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_add\",\"createTime\":1585382547000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255871\",\"name\":\"角色增加\",\"openType\":0,\"permission\":\"sysRole:add\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_edit\",\"createTime\":1593662247000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255881\",\"name\":\"角色编辑\",\"openType\":0,\"permission\":\"sysRole:edit\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_delete\",\"createTime\":1585382566000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255891\",\"name\":\"角色删除\",\"openType\":0,\"permission\":\"sysRole:delete\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_detail\",\"createTime\":1585382581000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255901\",\"name\":\"角色详情\",\"openType\":0,\"permission\":\"sysRole:detail\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_drop_down\",\"createTime\":1590738339000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255911\",\"name\":\"角色下拉\",\"openType\":0,\"permission\":\"sysRole:dropDown\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_grant_menu\",\"createTime\":1585703787000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255921\",\"name\":\"角色授权菜单\",\"openType\":0,\"permission\":\"sysRole:grantMenu\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_own_menu\",\"createTime\":1590733314000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255931\",\"name\":\"角色拥有菜单\",\"openType\":0,\"permission\":\"sysRole:ownMenu\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_grant_data\",\"createTime\":1585703816000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255941\",\"name\":\"角色授权数据\",\"openType\":0,\"permission\":\"sysRole:grantData\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_role_mgr_own_data\",\"createTime\":1590733388000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255951\",\"name\":\"角色拥有数据\",\"openType\":0,\"permission\":\"sysRole:ownData\",\"pid\":\"1264622039642255851\",\"pids\":\"[0],[1264622039642255671],[1264622039642255851],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_role_mgr\",\"component\":\"system/role/index\",\"createTime\":1585382469000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255851\",\"name\":\"角色管理\",\"openType\":1,\"pid\":\"1264622039642255671\",\"pids\":\"[0],[1264622039642255671],\",\"router\":\"/role\",\"sort\":8,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"auth_manager\",\"component\":\"PageView\",\"createTime\":1594799517000,\"createUser\":1265476890672672808,\"icon\":\"safety-certificate\",\"id\":\"1264622039642255671\",\"name\":\"权限管理\",\"openType\":0,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/auth\",\"sort\":3,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"system_tools_config_page\",\"createTime\":1590570142000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255981\",\"name\":\"配置查询\",\"openType\":0,\"permission\":\"sysConfig:page\",\"pid\":\"1264622039642255971\",\"pids\":\"[0],[1264622039642255961],[1264622039642255971],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"system_tools_config_list\",\"createTime\":1590570162000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255991\",\"name\":\"配置列表\",\"openType\":0,\"permission\":\"sysConfig:list\",\"pid\":\"1264622039642255971\",\"pids\":\"[0],[1264622039642255961],[1264622039642255971],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"system_tools_config_add\",\"createTime\":1590570211000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256001\",\"name\":\"配置增加\",\"openType\":0,\"permission\":\"sysConfig:add\",\"pid\":\"1264622039642255971\",\"pids\":\"[0],[1264622039642255961],[1264622039642255971],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"system_tools_config_edit\",\"createTime\":1590570235000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256011\",\"name\":\"配置编辑\",\"openType\":0,\"permission\":\"sysConfig:edit\",\"pid\":\"1264622039642255971\",\"pids\":\"[0],[1264622039642255961],[1264622039642255971],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"system_tools_config_delete\",\"createTime\":1590570224000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256021\",\"name\":\"配置删除\",\"openType\":0,\"permission\":\"sysConfig:delete\",\"pid\":\"1264622039642255971\",\"pids\":\"[0],[1264622039642255961],[1264622039642255971],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"system_tools_config_detail\",\"createTime\":1590570179000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256031\",\"name\":\"配置详情\",\"openType\":0,\"permission\":\"sysConfig:detail\",\"pid\":\"1264622039642255971\",\"pids\":\"[0],[1264622039642255961],[1264622039642255971],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"system_tools_config\",\"component\":\"system/config/index\",\"createTime\":1590343976000,\"createUser\":1265476890672672808,\"id\":\"1264622039642255971\",\"name\":\"系统配置\",\"openType\":1,\"pid\":\"1264622039642255961\",\"pids\":\"[0],[1264622039642255961],\",\"router\":\"/config\",\"sort\":9,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_email_mgr_send_email\",\"createTime\":1593661539000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256051\",\"name\":\"发送文本邮件\",\"openType\":0,\"permission\":\"email:sendEmail\",\"pid\":\"1264622039642256041\",\"pids\":\"[0],[1264622039642255961],[1264622039642256041],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_email_mgr_send_email_html\",\"createTime\":1593661557000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256061\",\"name\":\"发送html邮件\",\"openType\":0,\"permission\":\"email:sendEmailHtml\",\"pid\":\"1264622039642256041\",\"pids\":\"[0],[1264622039642255961],[1264622039642256041],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_email_mgr\",\"component\":\"system/email/index\",\"createTime\":1593661461000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256041\",\"name\":\"邮件发送\",\"openType\":1,\"pid\":\"1264622039642255961\",\"pids\":\"[0],[1264622039642255961],\",\"router\":\"/email\",\"sort\":10,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_sms_mgr_page\",\"createTime\":1593663416000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256081\",\"name\":\"短信发送查询\",\"openType\":0,\"permission\":\"sms:page\",\"pid\":\"1264622039642256071\",\"pids\":\"[0],[1264622039642255961],[1264622039642256071],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_sms_mgr_send_login_message\",\"createTime\":1593662551000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256091\",\"name\":\"发送验证码短信\",\"openType\":0,\"permission\":\"sms:sendLoginMessage\",\"pid\":\"1264622039642256071\",\"pids\":\"[0],[1264622039642255961],[1264622039642256071],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_sms_mgr_validate_message\",\"createTime\":1593662570000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256101\",\"name\":\"验证短信验证码\",\"openType\":0,\"permission\":\"sms:validateMessage\",\"pid\":\"1264622039642256071\",\"pids\":\"[0],[1264622039642255961],[1264622039642256071],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_sms_mgr\",\"component\":\"system/sms/index\",\"createTime\":1593662412000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256071\",\"name\":\"短信管理\",\"openType\":1,\"pid\":\"1264622039642255961\",\"pids\":\"[0],[1264622039642255961],\",\"router\":\"/sms\",\"sort\":11,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_type_page\",\"createTime\":1585711222000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256121\",\"name\":\"字典类型查询\",\"openType\":0,\"permission\":\"sysDictType:page\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_type_list\",\"createTime\":1590736355000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256131\",\"name\":\"字典类型列表\",\"openType\":0,\"permission\":\"sysDictType:list\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_type_add\",\"createTime\":1585711198000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256141\",\"name\":\"字典类型增加\",\"openType\":0,\"permission\":\"sysDictType:add\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_type_delete\",\"createTime\":1585711290000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256151\",\"name\":\"字典类型删除\",\"openType\":0,\"permission\":\"sysDictType:delete\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_type_edit\",\"createTime\":1585711302000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256161\",\"name\":\"字典类型编辑\",\"openType\":0,\"permission\":\"sysDictType:edit\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_type_detail\",\"createTime\":1585711326000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256171\",\"name\":\"字典类型详情\",\"openType\":0,\"permission\":\"sysDictType:detail\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_type_drop_down\",\"createTime\":1585711343000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256181\",\"name\":\"字典类型下拉\",\"openType\":0,\"permission\":\"sysDictType:dropDown\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_type_change_status\",\"createTime\":1592882150000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256191\",\"name\":\"字典类型修改状态\",\"openType\":0,\"permission\":\"sysDictType:changeStatus\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_page\",\"createTime\":1585711391000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256201\",\"name\":\"字典值查询\",\"openType\":0,\"permission\":\"sysDictData:page\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_list\",\"createTime\":1585711498000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256211\",\"name\":\"字典值列表\",\"openType\":0,\"permission\":\"sysDictData:list\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_add\",\"createTime\":1585711371000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256221\",\"name\":\"字典值增加\",\"openType\":0,\"permission\":\"sysDictData:add\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_delete\",\"createTime\":1585711406000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256231\",\"name\":\"字典值删除\",\"openType\":0,\"permission\":\"sysDictData:delete\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_edit\",\"createTime\":1585711461000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256241\",\"name\":\"字典值编辑\",\"openType\":0,\"permission\":\"sysDictData:edit\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_detail\",\"createTime\":1585711482000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256251\",\"name\":\"字典值详情\",\"openType\":0,\"permission\":\"sysDictData:detail\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_dict_mgr_dict_change_status\",\"createTime\":1592882273000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256261\",\"name\":\"字典值修改状态\",\"openType\":0,\"permission\":\"sysDictData:changeStatus\",\"pid\":\"1264622039642256111\",\"pids\":\"[0],[1264622039642255961],[1264622039642256111],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_dict_mgr\",\"component\":\"system/dict/index\",\"createTime\":1585711046000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256111\",\"name\":\"字典管理\",\"openType\":1,\"pid\":\"1264622039642255961\",\"pids\":\"[0],[1264622039642255961],\",\"router\":\"/dict\",\"sort\":12,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_swagger_mgr\",\"component\":\"Iframe\",\"createTime\":1593663416000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256271\",\"link\":\"http://localhost:82/doc.html\",\"name\":\"接口文档\",\"openType\":2,\"pid\":\"1264622039642255961\",\"pids\":\"[0],[1264622039642255961],\",\"router\":\"/swagger\",\"sort\":13,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"system_tools\",\"component\":\"PageView\",\"createTime\":1590343855000,\"createUser\":1265476890672672808,\"icon\":\"euro\",\"id\":\"1264622039642255961\",\"name\":\"开发管理\",\"openType\":1,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/tools\",\"sort\":4,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_log_mgr_vis_log_page\",\"createTime\":1593654951000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256301\",\"name\":\"访问日志查询\",\"openType\":0,\"permission\":\"sysVisLog:page\",\"pid\":\"1264622039642256291\",\"pids\":\"[0],[1264622039642256281],[1264622039642256291],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_log_mgr_vis_log_delete\",\"createTime\":1593655017000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256311\",\"name\":\"访问日志清空\",\"openType\":0,\"permission\":\"sysVisLog:delete\",\"pid\":\"1264622039642256291\",\"pids\":\"[0],[1264622039642256281],[1264622039642256291],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_log_mgr_vis_log\",\"component\":\"system/log/vislog/index\",\"createTime\":1585704400000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256291\",\"name\":\"访问日志\",\"openType\":0,\"pid\":\"1264622039642256281\",\"pids\":\"[0],[1264622039642256281],\",\"router\":\"/vislog\",\"sort\":14,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_log_mgr_op_log_page\",\"createTime\":1593655059000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256331\",\"name\":\"操作日志查询\",\"openType\":0,\"permission\":\"sysOpLog:page\",\"pid\":\"1264622039642256321\",\"pids\":\"[0],[1264622039642256281],[1264622039642256321],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_log_mgr_op_log_delete\",\"createTime\":1593655093000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256341\",\"name\":\"操作日志清空\",\"openType\":0,\"permission\":\"sysOpLog:delete\",\"pid\":\"1264622039642256321\",\"pids\":\"[0],[1264622039642256281],[1264622039642256321],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_log_mgr_op_log\",\"component\":\"system/log/oplog/index\",\"createTime\":1585704419000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256321\",\"name\":\"操作日志\",\"openType\":0,\"pid\":\"1264622039642256281\",\"pids\":\"[0],[1264622039642256281],\",\"router\":\"/oplog\",\"sort\":15,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_log_mgr\",\"component\":\"PageView\",\"createTime\":1585704301000,\"createUser\":1265476890672672808,\"icon\":\"read\",\"id\":\"1264622039642256281\",\"name\":\"日志管理\",\"openType\":1,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/log\",\"sort\":5,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_monitor_mgr_machine_monitor_query\",\"createTime\":1591344333000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256371\",\"name\":\"服务监控查询\",\"openType\":0,\"permission\":\"sysMachine:query\",\"pid\":\"1264622039642256361\",\"pids\":\"[0],[1264622039642256351],[1264622039642256361],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_monitor_mgr_machine_monitor\",\"component\":\"system/machine/index\",\"createTime\":1591344158000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256361\",\"name\":\"服务监控\",\"openType\":1,\"pid\":\"1264622039642256351\",\"pids\":\"[0],[1264622039642256351],\",\"router\":\"/machine\",\"sort\":16,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_monitor_mgr_online_user_list\",\"createTime\":1591344226000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256391\",\"name\":\"在线用户列表\",\"openType\":0,\"permission\":\"sysOnlineUser:list\",\"pid\":\"1264622039642256381\",\"pids\":\"[0],[1264622039642256351],[1264622039642256381],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_monitor_mgr_online_user_force_exist\",\"createTime\":1591344256000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256401\",\"name\":\"在线用户强退\",\"openType\":0,\"permission\":\"sysOnlineUser:forceExist\",\"pid\":\"1264622039642256381\",\"pids\":\"[0],[1264622039642256351],[1264622039642256381],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_monitor_mgr_online_user\",\"component\":\"system/onlineUser/index\",\"createTime\":1591344115000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256381\",\"name\":\"在线用户\",\"openType\":1,\"pid\":\"1264622039642256351\",\"pids\":\"[0],[1264622039642256351],\",\"router\":\"/onlineUser\",\"sort\":17,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_monitor_mgr_druid\",\"component\":\"Iframe\",\"createTime\":1593332107000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256411\",\"link\":\"http://localhost:82/druid\",\"name\":\"数据监控\",\"openType\":2,\"pid\":\"1264622039642256351\",\"pids\":\"[0],[1264622039642256351],\",\"router\":\"/druid\",\"sort\":18,\"status\":0,\"type\":1,\"updateTime\":1599961150000,\"updateUser\":1265476890672672808,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_monitor_mgr\",\"component\":\"PageView\",\"createTime\":1591344050000,\"createUser\":1265476890672672808,\"icon\":\"deployment-unit\",\"id\":\"1264622039642256351\",\"name\":\"系统监控\",\"openType\":1,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/monitor\",\"sort\":6,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_notice_mgr_page\",\"createTime\":1593416730000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256441\",\"name\":\"公告查询\",\"openType\":0,\"permission\":\"sysNotice:page\",\"pid\":\"1264622039642256431\",\"pids\":\"[0],[1264622039642256421],[1264622039642256431],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_notice_mgr_add\",\"createTime\":1593416757000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256451\",\"name\":\"公告增加\",\"openType\":0,\"permission\":\"sysNotice:add\",\"pid\":\"1264622039642256431\",\"pids\":\"[0],[1264622039642256421],[1264622039642256431],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_notice_mgr_edit\",\"createTime\":1593416782000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256461\",\"name\":\"公告编辑\",\"openType\":0,\"permission\":\"sysNotice:edit\",\"pid\":\"1264622039642256431\",\"pids\":\"[0],[1264622039642256421],[1264622039642256431],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_notice_mgr_delete\",\"createTime\":1593416771000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256471\",\"name\":\"公告删除\",\"openType\":0,\"permission\":\"sysNotice:delete\",\"pid\":\"1264622039642256431\",\"pids\":\"[0],[1264622039642256421],[1264622039642256431],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_notice_mgr_detail\",\"createTime\":1593416793000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256481\",\"name\":\"公告查看\",\"openType\":0,\"permission\":\"sysNotice:detail\",\"pid\":\"1264622039642256431\",\"pids\":\"[0],[1264622039642256421],[1264622039642256431],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_notice_mgr_changeStatus\",\"createTime\":1593416810000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256491\",\"name\":\"公告修改状态\",\"openType\":0,\"permission\":\"sysNotice:changeStatus\",\"pid\":\"1264622039642256431\",\"pids\":\"[0],[1264622039642256421],[1264622039642256431],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_notice_mgr\",\"component\":\"system/notice/index\",\"createTime\":1593416664000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256431\",\"name\":\"公告管理\",\"openType\":1,\"pid\":\"1264622039642256421\",\"pids\":\"[0],[1264622039642256421],\",\"router\":\"/notice\",\"sort\":19,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_notice_mgr_received_page\",\"createTime\":1593419623000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256511\",\"name\":\"已收公告查询\",\"openType\":0,\"permission\":\"sysNotice:received\",\"pid\":\"1264622039642256501\",\"pids\":\"[0],[1264622039642256421],[1264622039642256501],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_notice_mgr_received\",\"component\":\"system/noticeReceived/index\",\"createTime\":1593419573000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256501\",\"name\":\"已收公告\",\"openType\":1,\"pid\":\"1264622039642256421\",\"pids\":\"[0],[1264622039642256421],\",\"router\":\"/noticeReceived\",\"sort\":20,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_notice\",\"component\":\"PageView\",\"createTime\":1593416513000,\"createUser\":1265476890672672808,\"icon\":\"sound\",\"id\":\"1264622039642256421\",\"name\":\"通知公告\",\"openType\":1,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/notice\",\"sort\":7,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_file_mgr_sys_file_page\",\"createTime\":1592991338000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256541\",\"name\":\"文件查询\",\"openType\":0,\"permission\":\"sysFileInfo:page\",\"pid\":\"1264622039642256531\",\"pids\":\"[0],[1264622039642256521],[1264622039642256531],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_file_mgr_sys_file_list\",\"createTime\":1592991349000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256551\",\"name\":\"文件列表\",\"openType\":0,\"permission\":\"sysFileInfo:list\",\"pid\":\"1264622039642256531\",\"pids\":\"[0],[1264622039642256521],[1264622039642256531],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_file_mgr_sys_file_delete\",\"createTime\":1592991371000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256561\",\"name\":\"文件删除\",\"openType\":0,\"permission\":\"sysFileInfo:delete\",\"pid\":\"1264622039642256531\",\"pids\":\"[0],[1264622039642256521],[1264622039642256531],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_file_mgr_sys_file_detail\",\"createTime\":1592991361000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256571\",\"name\":\"文件详情\",\"openType\":0,\"permission\":\"sysFileInfo:detail\",\"pid\":\"1264622039642256531\",\"pids\":\"[0],[1264622039642256521],[1264622039642256531],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_file_mgr_sys_file_upload\",\"createTime\":1592991269000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256581\",\"name\":\"文件上传\",\"openType\":0,\"permission\":\"sysFileInfo:upload\",\"pid\":\"1264622039642256531\",\"pids\":\"[0],[1264622039642256521],[1264622039642256531],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_file_mgr_sys_file_download\",\"createTime\":1592991295000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256591\",\"name\":\"文件下载\",\"openType\":0,\"permission\":\"sysFileInfo:download\",\"pid\":\"1264622039642256531\",\"pids\":\"[0],[1264622039642256521],[1264622039642256531],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_file_mgr_sys_file_preview\",\"createTime\":1592991319000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256601\",\"name\":\"图片预览\",\"openType\":0,\"permission\":\"sysFileInfo:preview\",\"pid\":\"1264622039642256531\",\"pids\":\"[0],[1264622039642256521],[1264622039642256531],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_file_mgr_sys_file\",\"component\":\"system/file/index\",\"createTime\":1592991177000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256531\",\"name\":\"系统文件\",\"openType\":1,\"pid\":\"1264622039642256521\",\"pids\":\"[0],[1264622039642256521],\",\"router\":\"/file\",\"sort\":21,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_file_mgr\",\"component\":\"PageView\",\"createTime\":1592991070000,\"createUser\":1265476890672672808,\"icon\":\"file\",\"id\":\"1264622039642256521\",\"name\":\"文件管理\",\"openType\":1,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/file\",\"sort\":8,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_page\",\"createTime\":1593595183000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256631\",\"name\":\"定时任务查询\",\"openType\":0,\"permission\":\"sysTimers:page\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_list\",\"createTime\":1593595196000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256641\",\"name\":\"定时任务列表\",\"openType\":0,\"permission\":\"sysTimers:list\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_detail\",\"createTime\":1593595210000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256651\",\"name\":\"定时任务详情\",\"openType\":0,\"permission\":\"sysTimers:detail\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_add\",\"createTime\":1593595223000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256661\",\"name\":\"定时任务增加\",\"openType\":0,\"permission\":\"sysTimers:add\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_delete\",\"createTime\":1593595233000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256671\",\"name\":\"定时任务删除\",\"openType\":0,\"permission\":\"sysTimers:delete\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_edit\",\"createTime\":1593595243000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256681\",\"name\":\"定时任务编辑\",\"openType\":0,\"permission\":\"sysTimers:edit\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_get_action_classes\",\"createTime\":1593595336000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256691\",\"name\":\"定时任务可执行列表\",\"openType\":0,\"permission\":\"sysTimers:getActionClasses\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_start\",\"createTime\":1593595352000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256701\",\"name\":\"定时任务启动\",\"openType\":0,\"permission\":\"sysTimers:start\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[],\"code\":\"sys_timers_mgr_stop\",\"createTime\":1593595363000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256711\",\"name\":\"定时任务关闭\",\"openType\":0,\"permission\":\"sysTimers:stop\",\"pid\":\"1264622039642256621\",\"pids\":\"[0],[1264622039642256611],[1264622039642256621],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_timers_mgr\",\"component\":\"system/timers/index\",\"createTime\":1593595133000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256621\",\"name\":\"任务管理\",\"openType\":1,\"pid\":\"1264622039642256611\",\"pids\":\"[0],[1264622039642256611],\",\"router\":\"/timers\",\"sort\":22,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_timers\",\"component\":\"PageView\",\"createTime\":1593595040000,\"createUser\":1265476890672672808,\"icon\":\"dashboard\",\"id\":\"1264622039642256611\",\"name\":\"定时任务\",\"openType\":1,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/timers\",\"sort\":100,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[{\"application\":\"system\",\"children\":[],\"code\":\"sys_area_mgr_list\",\"createTime\":1621404099000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256741\",\"name\":\"系统区域列表\",\"openType\":0,\"permission\":\"sysArea:list\",\"pid\":\"1264622039642256731\",\"pids\":\"[0],[1264622039642256721],[1264622039642256731],\",\"sort\":100,\"status\":0,\"type\":2,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_area_mgr\",\"component\":\"system/area/index\",\"createTime\":1621403862000,\"createUser\":1265476890672672808,\"id\":\"1264622039642256731\",\"name\":\"系统区域\",\"openType\":1,\"pid\":\"1264622039642256721\",\"pids\":\"[0],[1264622039642256721],\",\"router\":\"/area\",\"sort\":100,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"code\":\"sys_area\",\"component\":\"PageView\",\"createTime\":1621403740000,\"createUser\":1265476890672672808,\"icon\":\"environment\",\"id\":\"1264622039642256721\",\"name\":\"区域管理\",\"openType\":1,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/area\",\"sort\":100,\"status\":0,\"type\":0,\"visible\":\"Y\",\"weight\":1},{\"application\":\"system_tool\",\"children\":[],\"code\":\"code_gen\",\"component\":\"gen/codeGenerate/index\",\"createTime\":1608898908000,\"createUser\":1265476890672672808,\"icon\":\"thunderbolt\",\"id\":\"1342445437296771074\",\"name\":\"代码生成\",\"openType\":1,\"pid\":\"0\",\"pids\":\"[0],\",\"router\":\"/codeGenerate/index\",\"sort\":100,\"status\":0,\"type\":1,\"visible\":\"Y\",\"weight\":1}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:14', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017369054863361, '系统应用_列表', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysApp/list', 'com.concise.com.concise.sys.modular.app.controller.SysAppController', 'list', 'GET', '{}', '{\"code\":200,\"data\":[{\"active\":\"Y\",\"code\":\"system\",\"createTime\":1585134420000,\"createUser\":1265476890672672808,\"id\":1265476890672672821,\"name\":\"系统应用\",\"status\":0,\"updateTime\":1597476185000,\"updateUser\":1280709549107552257},{\"active\":\"N\",\"code\":\"system_tool\",\"createTime\":1608898812000,\"createUser\":1265476890672672808,\"id\":1342445032647098369,\"name\":\"系统工具\",\"status\":0}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:16', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017370493509634, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"open_type\"}', '{\"code\":200,\"data\":[{\"code\":\"0\",\"value\":\"无\"},{\"code\":\"1\",\"value\":\"组件\"},{\"code\":\"2\",\"value\":\"内链\"},{\"code\":\"3\",\"value\":\"外链\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:16', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017370493509635, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"menu_weight\"}', '{\"code\":200,\"data\":[{\"code\":\"1\",\"value\":\"系统权重\"},{\"code\":\"2\",\"value\":\"业务权重\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:16', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017370493509636, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"menu_type\"}', '{\"code\":200,\"data\":[{\"code\":\"0\",\"value\":\"目录\"},{\"code\":\"1\",\"value\":\"菜单\"},{\"code\":\"2\",\"value\":\"按钮\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:16', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017387098759170, '系统菜单_切换', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysMenu/change', 'com.concise.com.concise.sys.modular.menu.controller.SysMenuController', 'change', 'POST', '{\"application\":\"system_tool\"}', '{\"code\":200,\"data\":[{\"component\":\"gen/codeGenerate/index\",\"hidden\":false,\"id\":\"1342445437296771074\",\"meta\":{\"icon\":\"thunderbolt\",\"show\":true,\"title\":\"代码生成\"},\"name\":\"code_gen\",\"path\":\"/codeGenerate/index\",\"pid\":\"0\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:20', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017393822228482, '代码生成配置_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/codeGenerate/page', 'com.concise.generate.modular.controller.CodeGenerateController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[],\"rows\":[],\"totalPage\":0,\"totalRows\":0},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:22', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017401774628866, '数据库表列表_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/codeGenerate/InformationList', 'com.concise.generate.modular.controller.CodeGenerateController', 'InformationList', 'GET', '', '{\"code\":200,\"data\":[{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"系统应用表\",\"tableName\":\"sys_app\",\"updateTime\":\"2022-02-11 10:27:42\"},{\"createTime\":\"2022-02-11 11:13:33\",\"tableComment\":\"中国行政地区表\",\"tableName\":\"sys_area\"},{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"代码生成基础配置\",\"tableName\":\"sys_code_generate\"},{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"代码生成详细配置\",\"tableName\":\"sys_code_generate_config\"},{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"系统参数配置表\",\"tableName\":\"sys_config\",\"updateTime\":\"2022-02-11 10:27:42\"},{\"createTime\":\"2022-02-11 10:44:18\",\"tableComment\":\"系统字典值表\",\"tableName\":\"sys_dict_data\",\"updateTime\":\"2022-02-11 10:44:18\"},{\"createTime\":\"2022-02-11 10:44:46\",\"tableComment\":\"系统字典类型表\",\"tableName\":\"sys_dict_type\",\"updateTime\":\"2022-02-11 10:44:46\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"员工表\",\"tableName\":\"sys_emp\",\"updateTime\":\"2022-02-11 11:47:32\"},{\"createTime\":\"2022-02-11 10:57:01\",\"tableComment\":\"员工附属机构岗位表\",\"tableName\":\"sys_emp_ext_org_pos\"},{\"createTime\":\"2022-02-11 11:11:28\",\"tableComment\":\"员工职位关联表\",\"tableName\":\"sys_emp_pos\",\"updateTime\":\"2022-02-11 11:47:32\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"文件信息表\",\"tableName\":\"sys_file_info\"},{\"createTime\":\"2022-02-11 10:56:12\",\"tableComment\":\"系统菜单表\",\"tableName\":\"sys_menu\",\"updateTime\":\"2022-02-11 10:56:12\"},{\"createTime\":\"2022-02-11 10:54:42\",\"tableComment\":\"通知表\",\"tableName\":\"sys_notice\"},{\"createTime\":\"2022-02-11 10:55:03\",\"tableComment\":\"系统用户数据范围表\",\"tableName\":\"sys_notice_user\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"第三方认证用户信息表\",\"tableName\":\"sys_oauth_user\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统操作日志表\",\"tableName\":\"sys_op_log\",\"updateTime\":\"2022-02-11 14:07:22\"},{\"createTime\":\"2022-02-11 10:46:18\",\"tableComment\":\"系统组织机构表\",\"tableName\":\"sys_org\",\"updateTime\":\"2022-02-11 12:02:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统职位表\",\"tableName\":\"sys_pos\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:49:33\",\"tableComment\":\"系统角色表\",\"tableName\":\"sys_role\",\"updateTime\":\"2022-02-11 10:49:33\"},{\"createTime\":\"2022-02-11 10:49:58\",\"tableComment\":\"系统角色数据范围表\",\"tableName\":\"sys_role_data_scope\",\"updateTime\":\"2022-02-11 10:49:58\"},{\"createTime\":\"2022-02-11 10:50:19\",\"tableComment\":\"系统角色菜单表\",\"tableName\":\"sys_role_menu\",\"updateTime\":\"2022-02-11 10:50:19\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"短信信息发送表\",\"tableName\":\"sys_sms\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"定时任务\",\"tableName\":\"sys_timers\",\"updateTime\":\"2022-02-11 10:37:12\"},{\"createTime\":\"2022-02-11 10:48:54\",\"tableComment\":\"系统用户表\",\"tableName\":\"sys_user\",\"updateTime\":\"2022-02-11 14:04:05\"},{\"createTime\":\"2022-02-11 10:50:56\",\"tableComment\":\"系统用户数据范围表\",\"tableName\":\"sys_user_data_scope\",\"updateTime\":\"2022-02-11 10:50:56\"},{\"createTime\":\"2022-02-11 10:51:11\",\"tableComment\":\"系统用户角色表\",\"tableName\":\"sys_user_role\",\"updateTime\":\"2022-02-11 10:51:11\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统访问日志表\",\"tableName\":\"sys_vis_log\",\"updateTime\":\"2022-02-11 14:04:05\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:24', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492017456489324545, '数据库表列表_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/codeGenerate/InformationList', 'com.concise.generate.modular.controller.CodeGenerateController', 'InformationList', 'GET', '', '{\"code\":200,\"data\":[{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"系统应用表\",\"tableName\":\"sys_app\",\"updateTime\":\"2022-02-11 10:27:42\"},{\"createTime\":\"2022-02-11 11:13:33\",\"tableComment\":\"中国行政地区表\",\"tableName\":\"sys_area\"},{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"代码生成基础配置\",\"tableName\":\"sys_code_generate\"},{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"代码生成详细配置\",\"tableName\":\"sys_code_generate_config\"},{\"createTime\":\"2022-02-11 10:27:42\",\"tableComment\":\"系统参数配置表\",\"tableName\":\"sys_config\",\"updateTime\":\"2022-02-11 10:27:42\"},{\"createTime\":\"2022-02-11 10:44:18\",\"tableComment\":\"系统字典值表\",\"tableName\":\"sys_dict_data\",\"updateTime\":\"2022-02-11 10:44:18\"},{\"createTime\":\"2022-02-11 10:44:46\",\"tableComment\":\"系统字典类型表\",\"tableName\":\"sys_dict_type\",\"updateTime\":\"2022-02-11 10:44:46\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"员工表\",\"tableName\":\"sys_emp\",\"updateTime\":\"2022-02-11 11:47:32\"},{\"createTime\":\"2022-02-11 10:57:01\",\"tableComment\":\"员工附属机构岗位表\",\"tableName\":\"sys_emp_ext_org_pos\"},{\"createTime\":\"2022-02-11 11:11:28\",\"tableComment\":\"员工职位关联表\",\"tableName\":\"sys_emp_pos\",\"updateTime\":\"2022-02-11 11:47:32\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"文件信息表\",\"tableName\":\"sys_file_info\"},{\"createTime\":\"2022-02-11 10:56:12\",\"tableComment\":\"系统菜单表\",\"tableName\":\"sys_menu\",\"updateTime\":\"2022-02-11 10:56:12\"},{\"createTime\":\"2022-02-11 10:54:42\",\"tableComment\":\"通知表\",\"tableName\":\"sys_notice\"},{\"createTime\":\"2022-02-11 10:55:03\",\"tableComment\":\"系统用户数据范围表\",\"tableName\":\"sys_notice_user\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"第三方认证用户信息表\",\"tableName\":\"sys_oauth_user\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统操作日志表\",\"tableName\":\"sys_op_log\",\"updateTime\":\"2022-02-11 14:07:24\"},{\"createTime\":\"2022-02-11 10:46:18\",\"tableComment\":\"系统组织机构表\",\"tableName\":\"sys_org\",\"updateTime\":\"2022-02-11 12:02:43\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统职位表\",\"tableName\":\"sys_pos\",\"updateTime\":\"2022-02-11 10:27:43\"},{\"createTime\":\"2022-02-11 10:49:33\",\"tableComment\":\"系统角色表\",\"tableName\":\"sys_role\",\"updateTime\":\"2022-02-11 10:49:33\"},{\"createTime\":\"2022-02-11 10:49:58\",\"tableComment\":\"系统角色数据范围表\",\"tableName\":\"sys_role_data_scope\",\"updateTime\":\"2022-02-11 10:49:58\"},{\"createTime\":\"2022-02-11 10:50:19\",\"tableComment\":\"系统角色菜单表\",\"tableName\":\"sys_role_menu\",\"updateTime\":\"2022-02-11 10:50:19\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"短信信息发送表\",\"tableName\":\"sys_sms\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"定时任务\",\"tableName\":\"sys_timers\",\"updateTime\":\"2022-02-11 10:37:12\"},{\"createTime\":\"2022-02-11 10:48:54\",\"tableComment\":\"系统用户表\",\"tableName\":\"sys_user\",\"updateTime\":\"2022-02-11 14:04:05\"},{\"createTime\":\"2022-02-11 10:50:56\",\"tableComment\":\"系统用户数据范围表\",\"tableName\":\"sys_user_data_scope\",\"updateTime\":\"2022-02-11 10:50:56\"},{\"createTime\":\"2022-02-11 10:51:11\",\"tableComment\":\"系统用户角色表\",\"tableName\":\"sys_user_role\",\"updateTime\":\"2022-02-11 10:51:11\"},{\"createTime\":\"2022-02-11 10:27:43\",\"tableComment\":\"系统访问日志表\",\"tableName\":\"sys_vis_log\",\"updateTime\":\"2022-02-11 14:04:05\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:07:37', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492018187749449730, '系统菜单_切换', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysMenu/change', 'com.concise.com.concise.sys.modular.menu.controller.SysMenuController', 'change', 'POST', '{\"application\":\"system\"}', '{\"code\":200,\"data\":[{\"component\":\"RouteView\",\"hidden\":false,\"id\":\"1264622039642255311\",\"meta\":{\"icon\":\"home\",\"show\":true,\"title\":\"主控面板\"},\"name\":\"system_index\",\"path\":\"/\",\"pid\":\"0\",\"redirect\":\"/analysis\"},{\"component\":\"system/dashboard/Workplace\",\"hidden\":false,\"id\":\"1264622039642255331\",\"meta\":{\"show\":true,\"title\":\"工作台\"},\"name\":\"system_index_workplace\",\"path\":\"workplace\",\"pid\":\"1264622039642255311\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642255341\",\"meta\":{\"icon\":\"team\",\"show\":true,\"title\":\"组织架构\"},\"name\":\"sys_mgr\",\"path\":\"/sys\",\"pid\":\"0\"},{\"component\":\"system/user/index\",\"hidden\":false,\"id\":\"1264622039642255351\",\"meta\":{\"show\":true,\"title\":\"用户管理\"},\"name\":\"sys_user_mgr\",\"path\":\"/mgr_user\",\"pid\":\"1264622039642255341\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642255671\",\"meta\":{\"icon\":\"safety-certificate\",\"show\":true,\"title\":\"权限管理\"},\"name\":\"auth_manager\",\"path\":\"/auth\",\"pid\":\"0\"},{\"component\":\"system/org/index\",\"hidden\":false,\"id\":\"1264622039642255521\",\"meta\":{\"show\":true,\"title\":\"机构管理\"},\"name\":\"sys_org_mgr\",\"path\":\"/org\",\"pid\":\"1264622039642255341\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642255961\",\"meta\":{\"icon\":\"euro\",\"show\":true,\"title\":\"开发管理\"},\"name\":\"system_tools\",\"path\":\"/tools\",\"pid\":\"0\"},{\"component\":\"system/pos/index\",\"hidden\":false,\"id\":\"1264622039642255601\",\"meta\":{\"show\":true,\"title\":\"职位管理\"},\"name\":\"sys_pos_mgr\",\"path\":\"/pos\",\"pid\":\"1264622039642255341\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256281\",\"meta\":{\"icon\":\"read\",\"show\":true,\"title\":\"日志管理\"},\"name\":\"sys_log_mgr\",\"path\":\"/log\",\"pid\":\"0\"},{\"component\":\"system/app/index\",\"hidden\":false,\"id\":\"1264622039642255681\",\"meta\":{\"show\":true,\"title\":\"应用管理\"},\"name\":\"sys_app_mgr\",\"path\":\"/app\",\"pid\":\"1264622039642255671\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256351\",\"meta\":{\"icon\":\"deployment-unit\",\"show\":true,\"title\":\"系统监控\"},\"name\":\"sys_monitor_mgr\",\"path\":\"/monitor\",\"pid\":\"0\"},{\"component\":\"system/menu/index\",\"hidden\":false,\"id\":\"1264622039642255761\",\"meta\":{\"show\":true,\"title\":\"菜单管理\"},\"name\":\"sys_menu_mgr\",\"path\":\"/menu\",\"pid\":\"1264622039642255671\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256421\",\"meta\":{\"icon\":\"sound\",\"show\":true,\"title\":\"通知公告\"},\"name\":\"sys_notice\",\"path\":\"/notice\",\"pid\":\"0\"},{\"component\":\"system/role/index\",\"hidden\":false,\"id\":\"1264622039642255851\",\"meta\":{\"show\":true,\"title\":\"角色管理\"},\"name\":\"sys_role_mgr\",\"path\":\"/role\",\"pid\":\"1264622039642255671\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256521\",\"meta\":{\"icon\":\"file\",\"show\":true,\"title\":\"文件管理\"},\"name\":\"sys_file_mgr\",\"path\":\"/file\",\"pid\":\"0\"},{\"component\":\"system/config/index\",\"hidden\":false,\"id\":\"1264622039642255971\",\"meta\":{\"show\":true,\"title\":\"系统配置\"},\"name\":\"system_tools_config\",\"path\":\"/config\",\"pid\":\"1264622039642255961\"},{\"component\":\"system/email/index\",\"hidden\":false,\"id\":\"1264622039642256041\",\"meta\":{\"show\":true,\"title\":\"邮件发送\"},\"name\":\"sys_email_mgr\",\"path\":\"/email\",\"pid\":\"1264622039642255961\"},{\"component\":\"system/sms/index\",\"hidden\":false,\"id\":\"1264622039642256071\",\"meta\":{\"show\":true,\"title\":\"短信管理\"},\"name\":\"sys_sms_mgr\",\"path\":\"/sms\",\"pid\":\"1264622039642255961\"},{\"component\":\"system/dict/index\",\"hidden\":false,\"id\":\"1264622039642256111\",\"meta\":{\"show\":true,\"title\":\"字典管理\"},\"name\":\"sys_dict_mgr\",\"path\":\"/dict\",\"pid\":\"1264622039642255961\"},{\"component\":\"Iframe\",\"hidden\":false,\"id\":\"1264622039642256271\",\"meta\":{\"link\":\"http://localhost:82/doc.html\",\"show\":true,\"title\":\"接口文档\"},\"name\":\"sys_swagger_mgr\",\"path\":\"/swagger\",\"pid\":\"1264622039642255961\"},{\"component\":\"system/log/vislog/index\",\"hidden\":false,\"id\":\"1264622039642256291\",\"meta\":{\"show\":true,\"title\":\"访问日志\"},\"name\":\"sys_log_mgr_vis_log\",\"path\":\"/vislog\",\"pid\":\"1264622039642256281\"},{\"component\":\"system/log/oplog/index\",\"hidden\":false,\"id\":\"1264622039642256321\",\"meta\":{\"show\":true,\"title\":\"操作日志\"},\"name\":\"sys_log_mgr_op_log\",\"path\":\"/oplog\",\"pid\":\"1264622039642256281\"},{\"component\":\"system/machine/index\",\"hidden\":false,\"id\":\"1264622039642256361\",\"meta\":{\"show\":true,\"title\":\"服务监控\"},\"name\":\"sys_monitor_mgr_machine_monitor\",\"path\":\"/machine\",\"pid\":\"1264622039642256351\"},{\"component\":\"system/onlineUser/index\",\"hidden\":false,\"id\":\"1264622039642256381\",\"meta\":{\"show\":true,\"title\":\"在线用户\"},\"name\":\"sys_monitor_mgr_online_user\",\"path\":\"/onlineUser\",\"pid\":\"1264622039642256351\"},{\"component\":\"Iframe\",\"hidden\":false,\"id\":\"1264622039642256411\",\"meta\":{\"link\":\"http://localhost:82/druid\",\"show\":true,\"title\":\"数据监控\"},\"name\":\"sys_monitor_mgr_druid\",\"path\":\"/druid\",\"pid\":\"1264622039642256351\"},{\"component\":\"system/notice/index\",\"hidden\":false,\"id\":\"1264622039642256431\",\"meta\":{\"show\":true,\"title\":\"公告管理\"},\"name\":\"sys_notice_mgr\",\"path\":\"/notice\",\"pid\":\"1264622039642256421\"},{\"component\":\"system/noticeReceived/index\",\"hidden\":false,\"id\":\"1264622039642256501\",\"meta\":{\"show\":true,\"title\":\"已收公告\"},\"name\":\"sys_notice_mgr_received\",\"path\":\"/noticeReceived\",\"pid\":\"1264622039642256421\"},{\"component\":\"system/file/index\",\"hidden\":false,\"id\":\"1264622039642256531\",\"meta\":{\"show\":true,\"title\":\"系统文件\"},\"name\":\"sys_file_mgr_sys_file\",\"path\":\"/file\",\"pid\":\"1264622039642256521\"},{\"component\":\"system/timers/index\",\"hidden\":false,\"id\":\"1264622039642256621\",\"meta\":{\"show\":true,\"title\":\"任务管理\"},\"name\":\"sys_timers_mgr\",\"path\":\"/timers\",\"pid\":\"1264622039642256611\"},{\"component\":\"system/dashboard/Analysis\",\"hidden\":false,\"id\":\"1264622039642255321\",\"meta\":{\"show\":true,\"title\":\"分析页\"},\"name\":\"system_index_dashboard\",\"path\":\"analysis\",\"pid\":\"1264622039642255311\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256611\",\"meta\":{\"icon\":\"dashboard\",\"show\":true,\"title\":\"定时任务\"},\"name\":\"sys_timers\",\"path\":\"/timers\",\"pid\":\"0\"},{\"component\":\"PageView\",\"hidden\":false,\"id\":\"1264622039642256721\",\"meta\":{\"icon\":\"environment\",\"show\":true,\"title\":\"区域管理\"},\"name\":\"sys_area\",\"path\":\"/area\",\"pid\":\"0\"},{\"component\":\"system/area/index\",\"hidden\":false,\"id\":\"1264622039642256731\",\"meta\":{\"show\":true,\"title\":\"系统区域\"},\"name\":\"sys_area_mgr\",\"path\":\"/area\",\"pid\":\"1264622039642256721\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:10:31', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492018207303294977, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100},{\"children\":[],\"id\":\"1491986023494373377\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"大方集团\",\"value\":\"1491986023494373377\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:10:36', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492018208649666561, '系统用户_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysUser/page', 'com.concise.com.concise.sys.modular.user.controller.SysUserController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"account\":\"yubaoshan\",\"birthday\":************,\"email\":\"<EMAIL>\",\"id\":1275735541155614721,\"name\":\"俞宝山\",\"nickName\":\"俞宝山\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"102\",\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"},\"tel\":\"\"},{\"account\":\"xuyuxiang\",\"birthday\":*************,\"id\":1280709549107552257,\"name\":\"徐玉祥\",\"nickName\":\"就是那个锅\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"jobNum\":\"100\",\"orgId\":1265476890672672770,\"orgName\":\"华夏集团成都分公司\"}},{\"account\":\"junze\",\"id\":1491982204861599746,\"name\":\"junze\",\"phone\":\"***********\",\"sex\":1,\"status\":0,\"sysEmpInfo\":{\"orgId\":1265476890672672769,\"orgName\":\"华夏集团北京分公司\"}}],\"totalPage\":1,\"totalRows\":3},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:10:36', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492018208754524162, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"sex\"}', '{\"code\":200,\"data\":[{\"code\":\"1\",\"value\":\"男\"},{\"code\":\"2\",\"value\":\"女\"},{\"code\":\"3\",\"value\":\"未知\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:10:36', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492018208771301378, '系统字典类型_下拉', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysDictType/dropDown', 'com.concise.com.concise.sys.modular.dict.controller.SysDictTypeController', 'dropDown', 'GET', '{\"code\":\"common_status\"}', '{\"code\":200,\"data\":[{\"code\":\"0\",\"value\":\"正常\"},{\"code\":\"1\",\"value\":\"停用\"},{\"code\":\"2\",\"value\":\"删除\"}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:10:36', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492018220477603841, '系统组织机构_树', 7, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/tree', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'tree', 'GET', '{}', '{\"code\":200,\"data\":[{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672771\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"研发部\",\"value\":\"1265476890672672771\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672772\",\"parentId\":\"1265476890672672769\",\"pid\":\"1265476890672672769\",\"title\":\"企划部\",\"value\":\"1265476890672672772\",\"weight\":100}],\"id\":\"1265476890672672769\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团北京分公司\",\"value\":\"1265476890672672769\",\"weight\":100},{\"children\":[{\"children\":[{\"children\":[],\"id\":\"1265476890672672775\",\"parentId\":\"1265476890672672773\",\"pid\":\"1265476890672672773\",\"title\":\"市场部二部\",\"value\":\"1265476890672672775\",\"weight\":100}],\"id\":\"1265476890672672773\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"市场部\",\"value\":\"1265476890672672773\",\"weight\":100},{\"children\":[],\"id\":\"1265476890672672774\",\"parentId\":\"1265476890672672770\",\"pid\":\"1265476890672672770\",\"title\":\"财务部\",\"value\":\"1265476890672672774\",\"weight\":100}],\"id\":\"1265476890672672770\",\"parentId\":\"1265476890651701250\",\"pid\":\"1265476890651701250\",\"title\":\"华夏集团成都分公司\",\"value\":\"1265476890672672770\",\"weight\":100}],\"id\":\"1265476890651701250\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"华夏集团\",\"value\":\"1265476890651701250\",\"weight\":100},{\"children\":[],\"id\":\"1491986023494373377\",\"parentId\":\"0\",\"pid\":\"0\",\"title\":\"大方集团\",\"value\":\"1491986023494373377\",\"weight\":100}],\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:10:39', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492018220544712705, '系统机构_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/page', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"hxjt\",\"createTime\":1585212653000,\"createUser\":1265476890672672808,\"id\":\"1265476890651701250\",\"name\":\"华夏集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"remark\":\"华夏集团总公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj\",\"createTime\":1585212942000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672769\",\"name\":\"华夏集团北京分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团北京分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd\",\"createTime\":1585212962000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672770\",\"name\":\"华夏集团成都分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团成都分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_yfb\",\"createTime\":1585212996000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672771\",\"name\":\"研发部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司研发部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_qhb\",\"createTime\":1585213026000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672772\",\"name\":\"企划部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司企划部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb\",\"createTime\":1585213055000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672773\",\"name\":\"市场部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司市场部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_cwb\",\"createTime\":1585213081000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672774\",\"name\":\"财务部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司财务部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb_2b\",\"createTime\":1586158610000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672775\",\"name\":\"市场部二部\",\"pid\":\"1265476890672672773\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],[1265476890672672773],\",\"remark\":\"华夏集团成都分公司市场部二部\",\"sort\":100,\"status\":0},{\"code\":\"dfjt\",\"id\":\"1491986023494373377\",\"name\":\"大方集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"sort\":100,\"status\":0}],\"totalPage\":1,\"totalRows\":9},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:10:39', 'superAdmin');
INSERT INTO `sys_op_log` VALUES (1492018221631037441, '系统机构_查询', 5, 'Y', '成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', '/api/sysOrg/page', 'com.concise.com.concise.sys.modular.org.controller.SysOrgController', 'page', 'GET', '{}', '{\"code\":200,\"data\":{\"pageNo\":1,\"pageSize\":10,\"rainbow\":[1],\"rows\":[{\"code\":\"hxjt\",\"createTime\":1585212653000,\"createUser\":1265476890672672808,\"id\":\"1265476890651701250\",\"name\":\"华夏集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"remark\":\"华夏集团总公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj\",\"createTime\":1585212942000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672769\",\"name\":\"华夏集团北京分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团北京分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd\",\"createTime\":1585212962000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672770\",\"name\":\"华夏集团成都分公司\",\"pid\":\"1265476890651701250\",\"pids\":\"[0],[1265476890651701250],\",\"remark\":\"华夏集团成都分公司\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_yfb\",\"createTime\":1585212996000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672771\",\"name\":\"研发部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司研发部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_bj_qhb\",\"createTime\":1585213026000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672772\",\"name\":\"企划部\",\"pid\":\"1265476890672672769\",\"pids\":\"[0],[1265476890651701250],[1265476890672672769],\",\"remark\":\"华夏集团北京分公司企划部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb\",\"createTime\":1585213055000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672773\",\"name\":\"市场部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司市场部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_cwb\",\"createTime\":1585213081000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672774\",\"name\":\"财务部\",\"pid\":\"1265476890672672770\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],\",\"remark\":\"华夏集团成都分公司财务部\",\"sort\":100,\"status\":0},{\"code\":\"hxjt_cd_scb_2b\",\"createTime\":1586158610000,\"createUser\":1265476890672672808,\"id\":\"1265476890672672775\",\"name\":\"市场部二部\",\"pid\":\"1265476890672672773\",\"pids\":\"[0],[1265476890651701250],[1265476890672672770],[1265476890672672773],\",\"remark\":\"华夏集团成都分公司市场部二部\",\"sort\":100,\"status\":0},{\"code\":\"dfjt\",\"id\":\"1491986023494373377\",\"name\":\"大方集团\",\"pid\":\"0\",\"pids\":\"[0],\",\"sort\":100,\"status\":0}],\"totalPage\":1,\"totalRows\":9},\"message\":\"请求成功\",\"success\":true}', '2022-02-11 14:10:39', 'superAdmin');

-- ----------------------------
-- Table structure for sys_org
-- ----------------------------
DROP TABLE IF EXISTS `sys_org`;
CREATE TABLE `sys_org`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `pid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '父id',
  `pids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '父ids',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
  `sort` int(11) NOT NULL COMMENT '排序',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统组织机构表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_org
-- ----------------------------
INSERT INTO `sys_org` VALUES ('1265476890651701250', '0', '[0],', '华夏集团', 'hxjt', 100, '华夏集团总公司', 0, '2020-03-26 16:50:53', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_org` VALUES ('1265476890672672769', '1265476890651701250', '[0],[1265476890651701250],', '华夏集团北京分公司', 'hxjt_bj', 100, '华夏集团北京分公司', 0, '2020-03-26 16:55:42', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_org` VALUES ('1265476890672672770', '1265476890651701250', '[0],[1265476890651701250],', '华夏集团成都分公司', 'hxjt_cd', 100, '华夏集团成都分公司', 0, '2020-03-26 16:56:02', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_org` VALUES ('1265476890672672771', '1265476890672672769', '[0],[1265476890651701250],[1265476890672672769],', '研发部', 'hxjt_bj_yfb', 100, '华夏集团北京分公司研发部', 0, '2020-03-26 16:56:36', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_org` VALUES ('1265476890672672772', '1265476890672672769', '[0],[1265476890651701250],[1265476890672672769],', '企划部', 'hxjt_bj_qhb', 100, '华夏集团北京分公司企划部', 0, '2020-03-26 16:57:06', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_org` VALUES ('1265476890672672773', '1265476890672672770', '[0],[1265476890651701250],[1265476890672672770],', '市场部', 'hxjt_cd_scb', 100, '华夏集团成都分公司市场部', 0, '2020-03-26 16:57:35', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_org` VALUES ('1265476890672672774', '1265476890672672770', '[0],[1265476890651701250],[1265476890672672770],', '财务部', 'hxjt_cd_cwb', 100, '华夏集团成都分公司财务部', 0, '2020-03-26 16:58:01', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_org` VALUES ('1265476890672672775', '1265476890672672773', '[0],[1265476890651701250],[1265476890672672770],[1265476890672672773],', '市场部二部', 'hxjt_cd_scb_2b', 100, '华夏集团成都分公司市场部二部', 0, '2020-04-06 15:36:50', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_org` VALUES ('1491986023494373377', '0', '[0],', '大方集团', 'dfjt', 100, NULL, 0, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_pos
-- ----------------------------
DROP TABLE IF EXISTS `sys_pos`;
CREATE TABLE `sys_pos`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
  `sort` int(11) NOT NULL COMMENT '排序',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `CODE_UNI`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统职位表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_pos
-- ----------------------------
INSERT INTO `sys_pos` VALUES (1265476890672672787, '总经理', 'zjl', 100, '总经理职位', 0, '2020-03-26 19:28:54', 1265476890672672808, '2020-06-02 21:01:04', 1265476890672672808);
INSERT INTO `sys_pos` VALUES (1265476890672672788, '副总经理', 'fzjl', 100, '副总经理职位', 0, '2020-03-26 19:29:57', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_pos` VALUES (1265476890672672789, '部门经理', 'bmjl', 100, '部门经理职位', 0, '2020-03-26 19:31:49', 1265476890672672808, NULL, NULL);
INSERT INTO `sys_pos` VALUES (1265476890672672790, '工作人员', 'gzry', 100, '工作人员职位', 0, '2020-05-27 11:32:00', 1265476890672672808, '2020-06-01 10:51:35', 1265476890672672808);

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
  `sort` int(11) NOT NULL COMMENT '序号',
  `data_scope_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '数据范围类型（字典 1全部数据 2本部门及以下数据 3本部门数据 4仅本人数据 5自定义数据）',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1停用 2删除）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统角色表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES ('1265476890672672817', '组织架构管理员', 'ent_manager_role', 100, 1, '组织架构管理员', 0, '2020-04-02 19:27:26', 1265476890672672808, '2020-09-12 15:54:07', 1265476890672672808);
INSERT INTO `sys_role` VALUES ('1265476890672672818', '权限管理员', 'auth_role', 101, 5, '权限管理员', 0, '2020-04-02 19:28:40', 1265476890672672808, '2020-07-16 10:52:21', 1265476890672672808);

-- ----------------------------
-- Table structure for sys_role_data_scope
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_data_scope`;
CREATE TABLE `sys_role_data_scope`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `role_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色id',
  `org_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '机构id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统角色数据范围表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_role_data_scope
-- ----------------------------
INSERT INTO `sys_role_data_scope` VALUES (1277435908822102018, '1265476890672672818', '1265476890651701250');
INSERT INTO `sys_role_data_scope` VALUES (1277435909635796993, '1265476890672672818', '1265476890672672769');
INSERT INTO `sys_role_data_scope` VALUES (1277435910432714754, '1265476890672672818', '1265476890672672771');
INSERT INTO `sys_role_data_scope` VALUES (1277435911233826818, '1265476890672672818', '1265476890672672772');
INSERT INTO `sys_role_data_scope` VALUES (1277435912018161666, '1265476890672672818', '1265476890672672770');
INSERT INTO `sys_role_data_scope` VALUES (1277435912810885122, '1265476890672672818', '1265476890672672773');
INSERT INTO `sys_role_data_scope` VALUES (1277435913595219970, '1265476890672672818', '1265476890672672775');
INSERT INTO `sys_role_data_scope` VALUES (1277435914392137730, '1265476890672672818', '1265476890672672774');
INSERT INTO `sys_role_data_scope` VALUES (1292060127645429762, '1265476890672672819', '1265476890672672774');

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `role_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色id',
  `menu_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统角色菜单表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (1304366872187256834, '1265476890672672818', '1264622039642255671');
INSERT INTO `sys_role_menu` VALUES (1304366872602492929, '1265476890672672818', '1264622039642255681');
INSERT INTO `sys_role_menu` VALUES (1304366873026117634, '1265476890672672818', '1264622039642255761');
INSERT INTO `sys_role_menu` VALUES (1304366873449742337, '1265476890672672818', '1264622039642255851');
INSERT INTO `sys_role_menu` VALUES (1304366873864978433, '1265476890672672818', '1264622039642255691');
INSERT INTO `sys_role_menu` VALUES (1304366874284408834, '1265476890672672818', '1264622039642255701');
INSERT INTO `sys_role_menu` VALUES (1304366874703839233, '1265476890672672818', '1264622039642255711');
INSERT INTO `sys_role_menu` VALUES (1304366875119075330, '1265476890672672818', '1264622039642255721');
INSERT INTO `sys_role_menu` VALUES (1304366875538505730, '1265476890672672818', '1264622039642255731');
INSERT INTO `sys_role_menu` VALUES (1304366875962130433, '1265476890672672818', '1264622039642255741');
INSERT INTO `sys_role_menu` VALUES (1304366876377366529, '1265476890672672818', '1264622039642255751');
INSERT INTO `sys_role_menu` VALUES (1304366876800991233, '1265476890672672818', '1264622039642255771');
INSERT INTO `sys_role_menu` VALUES (1304366877216227330, '1265476890672672818', '1264622039642255781');
INSERT INTO `sys_role_menu` VALUES (1304366877639852033, '1265476890672672818', '1264622039642255791');
INSERT INTO `sys_role_menu` VALUES (1304366878067671041, '1265476890672672818', '1264622039642255801');
INSERT INTO `sys_role_menu` VALUES (1304366878487101441, '1265476890672672818', '1264622039642255811');
INSERT INTO `sys_role_menu` VALUES (1304366878898143233, '1265476890672672818', '1264622039642255821');
INSERT INTO `sys_role_menu` VALUES (1304366879325962242, '1265476890672672818', '1264622039642255831');
INSERT INTO `sys_role_menu` VALUES (1304366879745392641, '1265476890672672818', '1264622039642255841');
INSERT INTO `sys_role_menu` VALUES (1304366880160628738, '1265476890672672818', '1264622039642255881');
INSERT INTO `sys_role_menu` VALUES (1304366880580059138, '1265476890672672818', '1264622039642255891');
INSERT INTO `sys_role_menu` VALUES (1304366880999489537, '1265476890672672818', '1264622039642255901');
INSERT INTO `sys_role_menu` VALUES (1304366881423114242, '1265476890672672818', '1264622039642255911');
INSERT INTO `sys_role_menu` VALUES (1304366881838350338, '1265476890672672818', '1264622039642255921');
INSERT INTO `sys_role_menu` VALUES (1304366882261975042, '1265476890672672818', '1264622039642255931');
INSERT INTO `sys_role_menu` VALUES (1304366882685599745, '1265476890672672818', '1264622039642255941');
INSERT INTO `sys_role_menu` VALUES (1304366883100835842, '1265476890672672818', '1264622039642255951');
INSERT INTO `sys_role_menu` VALUES (1304366883520266242, '1265476890672672818', '1264622039642255861');
INSERT INTO `sys_role_menu` VALUES (1304366883939696642, '1265476890672672818', '1264622039642255871');
INSERT INTO `sys_role_menu` VALUES (1304366884363321346, '1265476890672672818', '1264622039642257021');
INSERT INTO `sys_role_menu` VALUES (1304366884782751746, '1265476890672672818', '1264622039642257031');
INSERT INTO `sys_role_menu` VALUES (1304366885197987842, '1265476890672672818', '1264622039642256831');
INSERT INTO `sys_role_menu` VALUES (1304366885617418242, '1265476890672672818', '1264622039642257261');
INSERT INTO `sys_role_menu` VALUES (1304366886045237250, '1265476890672672818', '1264622039642257271');
INSERT INTO `sys_role_menu` VALUES (1304366886473056258, '1265476890672672818', '1264622039642257301');
INSERT INTO `sys_role_menu` VALUES (1304366886884098050, '1265476890672672818', '1264622039642257321');
INSERT INTO `sys_role_menu` VALUES (1304366887307722754, '1265476890672672818', '1264622039642257331');
INSERT INTO `sys_role_menu` VALUES (1304366887722958850, '1265476890672672818', '1264622039642257471');
INSERT INTO `sys_role_menu` VALUES (1304366888142389250, '1265476890672672818', '1264622039642257481');
INSERT INTO `sys_role_menu` VALUES (1304366888566013954, '1265476890672672818', '1264622039642257341');
INSERT INTO `sys_role_menu` VALUES (1304366888981250049, '1265476890672672818', '1264622039642257411');
INSERT INTO `sys_role_menu` VALUES (1304366889404874754, '1265476890672672818', '1264622039642257421');
INSERT INTO `sys_role_menu` VALUES (1304366889820110850, '1265476890672672818', '1264622039642257431');
INSERT INTO `sys_role_menu` VALUES (1304366890235346946, '1265476890672672818', '1264622039642257441');
INSERT INTO `sys_role_menu` VALUES (1304366890663165954, '1265476890672672818', '1264622039642257451');
INSERT INTO `sys_role_menu` VALUES (1304366891082596354, '1265476890672672818', '1264622039642257461');
INSERT INTO `sys_role_menu` VALUES (1304366891506221057, '1265476890672672818', '1264622039642257351');
INSERT INTO `sys_role_menu` VALUES (1304366891925651458, '1265476890672672818', '1264622039642257361');
INSERT INTO `sys_role_menu` VALUES (1304366892345081858, '1265476890672672818', '1264622039642257371');
INSERT INTO `sys_role_menu` VALUES (1304366892764512258, '1265476890672672818', '1264622039642257381');
INSERT INTO `sys_role_menu` VALUES (1304366893183942658, '1265476890672672818', '1264622039642257391');
INSERT INTO `sys_role_menu` VALUES (1304366893607567361, '1265476890672672818', '1264622039642257401');
INSERT INTO `sys_role_menu` VALUES (1304366894031192065, '1265476890672672818', '1264622039642257491');
INSERT INTO `sys_role_menu` VALUES (1304366894446428162, '1265476890672672818', '1264622039642257501');
INSERT INTO `sys_role_menu` VALUES (1304366894865858562, '1265476890672672818', '1264622039642257511');
INSERT INTO `sys_role_menu` VALUES (1304366895285288961, '1265476890672672818', '1264622039642255591');
INSERT INTO `sys_role_menu` VALUES (1304366895708913665, '1265476890672672818', '1264622039642257061');
INSERT INTO `sys_role_menu` VALUES (1304366896132538369, '1265476890672672818', '1264622039642257462');
INSERT INTO `sys_role_menu` VALUES (1304366896556163074, '1265476890672672818', '1264622039642256912');
INSERT INTO `sys_role_menu` VALUES (1304366896979787777, '1265476890672672818', '1264622039642255421');
INSERT INTO `sys_role_menu` VALUES (1304366897399218178, '1265476890672672818', '1264622039642257022');
INSERT INTO `sys_role_menu` VALUES (1304366897827037185, '1265476890672672818', '1264622039642256821');
INSERT INTO `sys_role_menu` VALUES (1304366898242273282, '1265476890672672818', '1264622039642257311');
INSERT INTO `sys_role_menu` VALUES (1304366898670092290, '1265476890672672818', '1264622039642257312');
INSERT INTO `sys_role_menu` VALUES (1304366899089522690, '1265476890672672818', '1264622039642257313');
INSERT INTO `sys_role_menu` VALUES (1304366899508953089, '1265476890672672818', '1264622039642257314');
INSERT INTO `sys_role_menu` VALUES (1304366899932577793, '1265476890672672818', '1264622039642257522');
INSERT INTO `sys_role_menu` VALUES (1304366900352008193, '1265476890672672818', '1264622039642257523');
INSERT INTO `sys_role_menu` VALUES (1304366900771438594, '1265476890672672818', '1264622039642257524');
INSERT INTO `sys_role_menu` VALUES (1304366901190868994, '1265476890672672818', '1264622039642257525');
INSERT INTO `sys_role_menu` VALUES (1304366901610299394, '1265476890672672818', '1264622039642257531');
INSERT INTO `sys_role_menu` VALUES (1304366902033924097, '1265476890672672818', '1264622039642257532');
INSERT INTO `sys_role_menu` VALUES (1307864929906434049, '1265476890672672817', '1264622039642255311');
INSERT INTO `sys_role_menu` VALUES (1307864930338447362, '1265476890672672817', '1264622039642255331');
INSERT INTO `sys_role_menu` VALUES (1307864930753683457, '1265476890672672817', '1264622039642255321');
INSERT INTO `sys_role_menu` VALUES (1307864931181502465, '1265476890672672817', '1264622039642255341');
INSERT INTO `sys_role_menu` VALUES (1307864931596738561, '1265476890672672817', '1264622039642255351');
INSERT INTO `sys_role_menu` VALUES (1307864932020363266, '1265476890672672817', '1264622039642255361');
INSERT INTO `sys_role_menu` VALUES (1307864932439793666, '1265476890672672817', '1264622039642255371');
INSERT INTO `sys_role_menu` VALUES (1307864932863418369, '1265476890672672817', '1264622039642255381');
INSERT INTO `sys_role_menu` VALUES (1307864933287043073, '1265476890672672817', '1264622039642255391');
INSERT INTO `sys_role_menu` VALUES (1307864933706473474, '1265476890672672817', '1264622039642255401');
INSERT INTO `sys_role_menu` VALUES (1307864934130098177, '1265476890672672817', '1264622039642255411');
INSERT INTO `sys_role_menu` VALUES (1307864934553722881, '1265476890672672817', '1264622039642255421');
INSERT INTO `sys_role_menu` VALUES (1307864934973153281, '1265476890672672817', '1264622039642255431');
INSERT INTO `sys_role_menu` VALUES (1307864935392583681, '1265476890672672817', '1264622039642255441');
INSERT INTO `sys_role_menu` VALUES (1307864935820402689, '1265476890672672817', '1264622039642255451');
INSERT INTO `sys_role_menu` VALUES (1307864936239833090, '1265476890672672817', '1264622039642255461');
INSERT INTO `sys_role_menu` VALUES (1307864936663457793, '1265476890672672817', '1264622039642255471');
INSERT INTO `sys_role_menu` VALUES (1307864937087082498, '1265476890672672817', '1264622039642255481');
INSERT INTO `sys_role_menu` VALUES (1307864937506512898, '1265476890672672817', '1264622039642255491');
INSERT INTO `sys_role_menu` VALUES (1307864937938526210, '1265476890672672817', '1264622039642255501');
INSERT INTO `sys_role_menu` VALUES (1307864938357956610, '1265476890672672817', '1264622039642255511');
INSERT INTO `sys_role_menu` VALUES (1307864938777387010, '1265476890672672817', '1264622039642255521');
INSERT INTO `sys_role_menu` VALUES (1307864939201011713, '1265476890672672817', '1264622039642255531');
INSERT INTO `sys_role_menu` VALUES (1307864939624636418, '1265476890672672817', '1264622039642255541');
INSERT INTO `sys_role_menu` VALUES (1307864940044066817, '1265476890672672817', '1264622039642255551');
INSERT INTO `sys_role_menu` VALUES (1307864940467691522, '1265476890672672817', '1264622039642255561');
INSERT INTO `sys_role_menu` VALUES (1307864940933259265, '1265476890672672817', '1264622039642255571');
INSERT INTO `sys_role_menu` VALUES (1307864941356883970, '1265476890672672817', '1264622039642255581');
INSERT INTO `sys_role_menu` VALUES (1307864941776314369, '1265476890672672817', '1264622039642255591');
INSERT INTO `sys_role_menu` VALUES (1307864942195744769, '1265476890672672817', '1264622039642255601');
INSERT INTO `sys_role_menu` VALUES (1307864942619369473, '1265476890672672817', '1264622039642255621');
INSERT INTO `sys_role_menu` VALUES (1307864943042994178, '1265476890672672817', '1264622039642255631');
INSERT INTO `sys_role_menu` VALUES (1307864943462424577, '1265476890672672817', '1264622039642255641');
INSERT INTO `sys_role_menu` VALUES (1307864943886049282, '1265476890672672817', '1264622039642255651');
INSERT INTO `sys_role_menu` VALUES (1307864944309673986, '1265476890672672817', '1264622039642255661');
INSERT INTO `sys_role_menu` VALUES (1307864944733298690, '1265476890672672817', '1264622039642255611');
INSERT INTO `sys_role_menu` VALUES (1307864945156923393, '1265476890672672817', '1264622039642257321');
INSERT INTO `sys_role_menu` VALUES (1307864945576353793, '1265476890672672817', '1264622039642257331');
INSERT INTO `sys_role_menu` VALUES (1307864945999978497, '1265476890672672817', '1264622039642257471');
INSERT INTO `sys_role_menu` VALUES (1307864946423603201, '1265476890672672817', '1264622039642257481');
INSERT INTO `sys_role_menu` VALUES (1307864946847227905, '1265476890672672817', '1264622039642257341');
INSERT INTO `sys_role_menu` VALUES (1307864947266658305, '1265476890672672817', '1264622039642257411');
INSERT INTO `sys_role_menu` VALUES (1307864947681894402, '1265476890672672817', '1264622039642257421');
INSERT INTO `sys_role_menu` VALUES (1307864948109713409, '1265476890672672817', '1264622039642257431');
INSERT INTO `sys_role_menu` VALUES (1307864948529143810, '1265476890672672817', '1264622039642257441');
INSERT INTO `sys_role_menu` VALUES (1307864948952768513, '1265476890672672817', '1264622039642257451');
INSERT INTO `sys_role_menu` VALUES (1307864949380587522, '1265476890672672817', '1264622039642257461');
INSERT INTO `sys_role_menu` VALUES (1307864949804212225, '1265476890672672817', '1264622039642257351');
INSERT INTO `sys_role_menu` VALUES (1307864950223642626, '1265476890672672817', '1264622039642257361');
INSERT INTO `sys_role_menu` VALUES (1307864950638878721, '1265476890672672817', '1264622039642257371');
INSERT INTO `sys_role_menu` VALUES (1307864951066697729, '1265476890672672817', '1264622039642257381');
INSERT INTO `sys_role_menu` VALUES (1307864951490322433, '1265476890672672817', '1264622039642257391');
INSERT INTO `sys_role_menu` VALUES (1307864951913947137, '1265476890672672817', '1264622039642257401');
INSERT INTO `sys_role_menu` VALUES (1307864952329183233, '1265476890672672817', '1264622039642257491');
INSERT INTO `sys_role_menu` VALUES (1307864952757002241, '1265476890672672817', '1264622039642257501');
INSERT INTO `sys_role_menu` VALUES (1307864953176432642, '1265476890672672817', '1264622039642257511');
INSERT INTO `sys_role_menu` VALUES (1307864953600057346, '1265476890672672817', '1264622039642256831');
INSERT INTO `sys_role_menu` VALUES (1307864954019487746, '1265476890672672817', '1264622039642257031');
INSERT INTO `sys_role_menu` VALUES (1307864954447306754, '1265476890672672817', '1264622039642257021');
INSERT INTO `sys_role_menu` VALUES (1307864954870931458, '1265476890672672817', '1264622039642257061');
INSERT INTO `sys_role_menu` VALUES (1307864955290361857, '1265476890672672817', '1264622039642257261');
INSERT INTO `sys_role_menu` VALUES (1307864955709792258, '1265476890672672817', '1264622039642257301');
INSERT INTO `sys_role_menu` VALUES (1307864956133416962, '1265476890672672817', '1264622039642257271');
INSERT INTO `sys_role_menu` VALUES (1307864956557041665, '1265476890672672817', '1264622039642257462');
INSERT INTO `sys_role_menu` VALUES (1307864956976472066, '1265476890672672817', '1264622039642256912');
INSERT INTO `sys_role_menu` VALUES (1307864957400096770, '1265476890672672817', '1264622039642255911');
INSERT INTO `sys_role_menu` VALUES (1307864957861470210, '1265476890672672817', '1264622039642257522');
INSERT INTO `sys_role_menu` VALUES (1307864958280900610, '1265476890672672817', '1264622039642257523');
INSERT INTO `sys_role_menu` VALUES (1307864958704525314, '1265476890672672817', '1264622039642257524');
INSERT INTO `sys_role_menu` VALUES (1307864959132344321, '1265476890672672817', '1264622039642257525');
INSERT INTO `sys_role_menu` VALUES (1307864959555969026, '1265476890672672817', '1264622039642257532');
INSERT INTO `sys_role_menu` VALUES (1307864959975399425, '1265476890672672817', '1264622039642257531');
INSERT INTO `sys_role_menu` VALUES (1307864960399024129, '1265476890672672817', '1264622039642257311');
INSERT INTO `sys_role_menu` VALUES (1307864960822648833, '1265476890672672817', '1264622039642257312');
INSERT INTO `sys_role_menu` VALUES (1307864961242079233, '1265476890672672817', '1264622039642257313');
INSERT INTO `sys_role_menu` VALUES (1307864961657315330, '1265476890672672817', '1264622039642257314');
INSERT INTO `sys_role_menu` VALUES (1307864962085134337, '1265476890672672817', '1264622039642256821');
INSERT INTO `sys_role_menu` VALUES (1307864962504564737, '1265476890672672817', '1264622039642257022');

-- ----------------------------
-- Table structure for sys_sms
-- ----------------------------
DROP TABLE IF EXISTS `sys_sms`;
CREATE TABLE `sys_sms`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `phone_numbers` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号',
  `validate_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '短信验证码',
  `template_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '短信模板ID',
  `biz_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回执id，可根据该id查询具体的发送状态',
  `status` tinyint(4) NOT NULL COMMENT '发送状态（字典 0 未发送，1 发送成功，2 发送失败，3 失效）',
  `source` tinyint(4) NOT NULL COMMENT '来源（字典 1 app， 2 pc， 3 其他）',
  `invalid_time` datetime NULL DEFAULT NULL COMMENT '失效时间',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '短信信息发送表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_sms
-- ----------------------------

-- ----------------------------
-- Table structure for sys_timers
-- ----------------------------
DROP TABLE IF EXISTS `sys_timers`;
CREATE TABLE `sys_timers`  (
  `id` bigint(20) NOT NULL COMMENT '定时器id',
  `timer_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '任务名称',
  `action_class` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行任务的class的类名（实现了TimerTaskRunner接口的类的全称）',
  `cron` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '定时任务表达式',
  `job_status` tinyint(4) NULL DEFAULT 0 COMMENT '状态（字典 1运行  2停止）',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注信息',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_timers
-- ----------------------------
INSERT INTO `sys_timers` VALUES (1288760324837851137, '定时同步缓存常量', 'com.concise.com.concise.sys.modular.timer.tasks.RefreshConstantsTaskRunner', '0 0/1 * * * ?', 1, '定时同步sys_config表的数据到缓存常量中', '2020-07-30 16:56:20', 1265476890672672808, '2020-07-30 16:58:52', 1265476890672672808);
INSERT INTO `sys_timers` VALUES (1304971718170832898, '定时打印一句话', 'com.concise.com.concise.sys.modular.timer.tasks.SystemOutTaskRunner', '0 0 * * * ? *', 2, '定时打印一句话', '2020-09-13 10:34:37', 1265476890672672808, '2020-09-23 20:37:48', 1265476890672672808);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账号',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码',
  `nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `avatar` bigint(20) NULL DEFAULT NULL COMMENT '头像',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `sex` tinyint(4) NOT NULL COMMENT '性别(字典 1男 2女 3未知)',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机',
  `tel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电话',
  `last_login_ip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最后登陆IP',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登陆时间',
  `admin_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '管理员类型（0超级管理员 1非管理员）',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态（字典 0正常 1冻结 2删除）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint(20) NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint(20) NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统用户表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES ('1265476890672672808', 'superAdmin', '123456', '超级管理员', '超级管理员', NULL, '2020-03-18', 1, '<EMAIL>', '15228937093', '1234567890', '127.0.0.1', '2022-02-11 14:04:05', 1, 0, '2020-05-29 16:39:28', -1, '2020-12-25 20:24:27', -1);
INSERT INTO `sys_user` VALUES ('1275735541155614721', 'yubaoshan', '123456', '俞宝山', '俞宝山', NULL, '1992-10-03', 1, '<EMAIL>', '***********', '', '127.0.0.1', '2020-09-23 10:15:10', 2, 0, '2020-06-24 18:20:30', 1265476890672672808, '2020-09-23 10:15:10', -1);
INSERT INTO `sys_user` VALUES ('1280709549107552257', 'xuyuxiang', '123456', '就是那个锅', '徐玉祥', NULL, '2020-07-01', 1, NULL, '***********', NULL, '127.0.0.1', '2020-09-23 10:16:54', 2, 0, '2020-07-08 11:45:26', 1265476890672672808, '2020-09-23 10:16:54', -1);
INSERT INTO `sys_user` VALUES ('1491982204861599746', 'junze', '$2a$10$ZADrjHbso8ViLBBvrK.1k.ovAItkweSrOmvrnVN/RehsD1CCUyiqm', NULL, 'junze', NULL, NULL, 1, NULL, '***********', NULL, NULL, NULL, 2, 0, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_user_data_scope
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_data_scope`;
CREATE TABLE `sys_user_data_scope`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户id',
  `org_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '机构id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统用户数据范围表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_user_data_scope
-- ----------------------------
INSERT INTO `sys_user_data_scope` VALUES (1277459951742840834, '1266277099455635457', '1265476890672672770');
INSERT INTO `sys_user_data_scope` VALUES (1277459952577507330, '1266277099455635457', '1265476890672672773');
INSERT INTO `sys_user_data_scope` VALUES (1277459953424756737, '1266277099455635457', '1265476890672672775');
INSERT INTO `sys_user_data_scope` VALUES (1277459954267811841, '1266277099455635457', '1265476890672672774');
INSERT INTO `sys_user_data_scope` VALUES (1280712071570366466, '1275735541155614721', '1265476890672672769');
INSERT INTO `sys_user_data_scope` VALUES (1280712071570366467, '1275735541155614721', '1265476890672672771');
INSERT INTO `sys_user_data_scope` VALUES (1280712071578755074, '1275735541155614721', '1265476890672672772');

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户id',
  `role_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统用户角色表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1283596900713574402, '1275735541155614721', '1265476890672672817');
INSERT INTO `sys_user_role` VALUES (1283596949627547649, '1280709549107552257', '1265476890672672818');

-- ----------------------------
-- Table structure for sys_vis_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_vis_log`;
CREATE TABLE `sys_vis_log`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `success` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否执行成功（Y-是，N-否）',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '具体消息',
  `ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'ip',
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `browser` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '浏览器',
  `os` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作系统',
  `vis_type` tinyint(4) NOT NULL COMMENT '操作类型（字典 1登入 2登出）',
  `vis_time` datetime NULL DEFAULT NULL COMMENT '访问时间',
  `account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '访问账号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统访问日志表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of sys_vis_log
-- ----------------------------
INSERT INTO `sys_vis_log` VALUES (1491965334062288897, '登录', 'Y', '登录成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', 1, '2022-02-11 10:40:30', 'superAdmin');
INSERT INTO `sys_vis_log` VALUES (1492016567779221505, '登录', 'Y', '登录成功', '127.0.0.1', '-', 'Chrome', 'Windows 10 or Windows Server 2016', 1, '2022-02-11 14:04:05', 'superAdmin');

SET FOREIGN_KEY_CHECKS = 1;
