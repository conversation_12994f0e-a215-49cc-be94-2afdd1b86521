-- ----------------------------
-- 浙政钉用户信息表
-- ----------------------------
DROP TABLE IF EXISTS `lx_user_info`;
CREATE TABLE `lx_user_info` (
  `id` varchar(50) NOT NULL COMMENT '用户id（浙政钉employeeCode）',
  `name` varchar(100) DEFAULT NULL COMMENT '姓名',
  `user_name` varchar(100) DEFAULT NULL COMMENT '用户的显示名称,唯一,支持中文',
  `sex` int(1) DEFAULT NULL COMMENT '性别（0:未知 1:男 2:女 9：未说明的性别）',
  `account_id` varchar(100) DEFAULT NULL COMMENT '浙政钉accountId',
  `dept_id` varchar(500) DEFAULT NULL COMMENT '机构id(一个或多个)多个用英文逗号隔开',
  `operator_type` int(1) DEFAULT NULL COMMENT '动作类型 0:新增或修改 1:新增或修改 2:删除',
  `main_dept_id` varchar(100) DEFAULT NULL COMMENT '主要部门ID（第一个部门）',
  `main_dept_name` varchar(200) DEFAULT NULL COMMENT '主要部门名称（第一个部门）',
  `main_position` varchar(100) DEFAULT NULL COMMENT '主要职务',
  `main_rank` varchar(100) DEFAULT NULL COMMENT '主要职级',
  `main_work_address` varchar(500) DEFAULT NULL COMMENT '主要办公地址',
  `main_work_phone` varchar(50) DEFAULT NULL COMMENT '主要办公电话',
  `main_jzjg` varchar(100) DEFAULT NULL COMMENT '主要矫正机构id',
  `main_jzjg_name` varchar(200) DEFAULT NULL COMMENT '主要矫正机构名称',
  `extend_infos_json` text COMMENT '扩展信息JSON字符串',
  `raw_data_json` text COMMENT '原始推送数据JSON字符串',
  `receive_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '接收时间',
  `process_status` int(1) DEFAULT 0 COMMENT '处理状态（0:未处理 1:已处理 2:处理失败）',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `process_message` varchar(500) DEFAULT NULL COMMENT '处理消息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_receive_time` (`receive_time`),
  KEY `idx_process_status` (`process_status`),
  KEY `idx_operator_type` (`operator_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='浙政钉用户信息表';
