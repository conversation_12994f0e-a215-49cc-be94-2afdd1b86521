-- ----------------------------
-- 推送记录表
-- ----------------------------
DROP TABLE IF EXISTS `push_record`;
CREATE TABLE `push_record` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `source_table` varchar(100) NOT NULL COMMENT '源数据表名',
  `source_id` varchar(100) NOT NULL COMMENT '源数据ID',
  `target_system` varchar(100) NOT NULL COMMENT '目标系统名称',
  `target_url` varchar(500) NOT NULL COMMENT '目标系统接口地址',
  `push_type` varchar(50) NOT NULL COMMENT '推送类型（ADD:新增 UPDATE:更新 DELETE:删除）',
  `push_data` text COMMENT '推送的数据内容（JSON格式）',
  `push_status` int(1) DEFAULT 0 COMMENT '推送状态（0:待推送 1:推送成功 2:推送失败 3:推送中）',
  `push_time` datetime DEFAULT NULL COMMENT '推送时间',
  `response_code` varchar(20) DEFAULT NULL COMMENT '响应状态码',
  `response_message` text COMMENT '响应消息',
  `retry_count` int(3) DEFAULT 0 COMMENT '重试次数',
  `max_retry_count` int(3) DEFAULT 3 COMMENT '最大重试次数',
  `next_retry_time` datetime DEFAULT NULL COMMENT '下次重试时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user_id` varchar(50) DEFAULT NULL COMMENT '创建人ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_source_table_id` (`source_table`, `source_id`),
  KEY `idx_push_status` (`push_status`),
  KEY `idx_target_system` (`target_system`),
  KEY `idx_push_time` (`push_time`),
  KEY `idx_next_retry_time` (`next_retry_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推送记录表';
