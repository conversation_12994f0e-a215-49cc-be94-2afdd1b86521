-- ----------------------------
-- 推送目标系统表
-- ----------------------------
DROP TABLE IF EXISTS `push_target_system`;
CREATE TABLE `push_target_system` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `system_name` varchar(100) NOT NULL COMMENT '系统名称',
  `system_code` varchar(50) NOT NULL COMMENT '系统编码（唯一）',
  `target_url` varchar(500) NOT NULL COMMENT '目标接口地址',
  `description` varchar(500) DEFAULT NULL COMMENT '系统描述',
  `enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用（0:禁用 1:启用）',
  `timeout` int(6) DEFAULT 30000 COMMENT '超时时间（毫秒）',
  `max_retry_count` int(3) DEFAULT 3 COMMENT '最大重试次数',
  `retry_delay_minutes` varchar(100) DEFAULT '2,4,8' COMMENT '重试延迟时间（分钟），逗号分隔',
  `auth_type` varchar(20) DEFAULT 'NONE' COMMENT '认证类型（NONE:无认证 BASIC:基础认证 TOKEN:令牌认证）',
  `auth_config` text COMMENT '认证配置（JSON格式）',
  `headers` text COMMENT '请求头配置（JSON格式）',
  `sort_order` int(3) DEFAULT 0 COMMENT '排序顺序',
  `status` int(1) DEFAULT 1 COMMENT '状态（0:无效 1:有效）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(50) DEFAULT NULL COMMENT '更新人',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_system_code` (`system_code`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推送目标系统表';

-- 插入示例数据
INSERT INTO `push_target_system` VALUES 
('1', '系统A', 'SYSTEM_A', 'http://system-a.example.com/api/user/receive', '系统A用户数据接收接口', 1, 30000, 3, '2,4,8', 'NONE', NULL, NULL, 1, 1, NOW(), NOW(), 'system', 'system', '示例目标系统A'),
('2', '系统B', 'SYSTEM_B', 'http://system-b.example.com/api/user/receive', '系统B用户数据接收接口', 0, 30000, 5, '1,2,4,8,16', 'TOKEN', '{"token":"your-token-here"}', '{"Content-Type":"application/json","X-API-Key":"your-api-key"}', 2, 1, NOW(), NOW(), 'system', 'system', '示例目标系统B（已禁用）');
