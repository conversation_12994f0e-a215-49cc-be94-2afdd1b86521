import { axios } from '@/utils/request'

/**
 * 查询申报活动
 *
 * <AUTHOR>
 * @date 2021-07-18 14:38:22
 */
export function declarActivitiesPage (parameter) {
  return axios({
    url: '/declarActivities/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 申报活动列表
 *
 * <AUTHOR>
 * @date 2021-07-18 14:38:22
 */
export function declarActivitiesList (parameter) {
  return axios({
    url: '/declarActivities/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加申报活动
 *
 * <AUTHOR>
 * @date 2021-07-18 14:38:22
 */
export function declarActivitiesAdd (parameter) {
  return axios({
    url: '/declarActivities/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑申报活动
 *
 * <AUTHOR>
 * @date 2021-07-18 14:38:22
 */
export function declarActivitiesEdit (parameter) {
  return axios({
    url: '/declarActivities/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除申报活动
 *
 * <AUTHOR>
 * @date 2021-07-18 14:38:22
 */
export function declarActivitiesDelete (parameter) {
  return axios({
    url: '/declarActivities/delete',
    method: 'post',
    data: parameter
  })
}
