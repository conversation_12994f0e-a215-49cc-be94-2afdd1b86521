import { axios } from '@/utils/request'

/**
 * 查询省下发_社会保险个人参保信息
 *
 * <AUTHOR>
 * @date 2023-03-15 14:07:21
 */
export function zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqPage (parameter) {
  return axios({
    url: '/zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 省下发_社会保险个人参保信息列表
 *
 * <AUTHOR>
 * @date 2023-03-15 14:07:21
 */
export function zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqList (parameter) {
  return axios({
    url: '/zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加省下发_社会保险个人参保信息
 *
 * <AUTHOR>
 * @date 2023-03-15 14:07:21
 */
export function zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqAdd (parameter) {
  return axios({
    url: '/zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑省下发_社会保险个人参保信息
 *
 * <AUTHOR>
 * @date 2023-03-15 14:07:21
 */
export function zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqEdit (parameter) {
  return axios({
    url: '/zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除省下发_社会保险个人参保信息
 *
 * <AUTHOR>
 * @date 2023-03-15 14:07:21
 */
export function zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqDelete (parameter) {
  return axios({
    url: '/zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq/delete',
    method: 'post',
    data: parameter
  })
}
