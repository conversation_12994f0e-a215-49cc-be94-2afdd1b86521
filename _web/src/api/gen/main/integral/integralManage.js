import { axios } from '@/utils/request'

/**
 * 查询积分
 *
 * <AUTHOR>
 * @date 2021-07-19 10:32:12
 */
export function integralPage (parameter) {
  return axios({
    url: '/integral/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 积分列表
 *
 * <AUTHOR>
 * @date 2021-07-19 10:32:12
 */
export function integralList (parameter) {
  return axios({
    url: '/integral/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加积分
 *
 * <AUTHOR>
 * @date 2021-07-19 10:32:12
 */
export function integralAdd (parameter) {
  return axios({
    url: '/integral/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑积分
 *
 * <AUTHOR>
 * @date 2021-07-19 10:32:12
 */
export function integralEdit (parameter) {
  return axios({
    url: '/integral/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除积分
 *
 * <AUTHOR>
 * @date 2021-07-19 10:32:12
 */
export function integralDelete (parameter) {
  return axios({
    url: '/integral/delete',
    method: 'post',
    data: parameter
  })
}
