import { axios } from '@/utils/request'

/**
 * 查询申报
 *
 * <AUTHOR>
 * @date 2021-07-18 14:37:44
 */
export function declarPage (parameter) {
  return axios({
    url: '/declar/page',
    method: 'get',
    params: parameter
  })
}

/**
 * 申报列表
 *
 * <AUTHOR>
 * @date 2021-07-18 14:37:44
 */
export function declarList (parameter) {
  return axios({
    url: '/declar/list',
    method: 'get',
    params: parameter
  })
}

/**
 * 添加申报
 *
 * <AUTHOR>
 * @date 2021-07-18 14:37:44
 */
export function declarAdd (parameter) {
  return axios({
    url: '/declar/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑申报
 *
 * <AUTHOR>
 * @date 2021-07-18 14:37:44
 */
export function declarEdit (parameter) {
  return axios({
    url: '/declar/edit',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除申报
 *
 * <AUTHOR>
 * @date 2021-07-18 14:37:44
 */
export function declarDelete (parameter) {
  return axios({
    url: '/declar/delete',
    method: 'post',
    data: parameter
  })
}
