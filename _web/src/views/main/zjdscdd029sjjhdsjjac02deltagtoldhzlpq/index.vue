<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="基准参保关系ID">
                <a-input v-model="queryParam.baz159" allow-clear placeholder="请输入基准参保关系ID"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="个人编号">
                <a-input v-model="queryParam.aac001" allow-clear placeholder="请输入个人编号"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="社会保障号码">
                  <a-input v-model="queryParam.aac002" allow-clear placeholder="请输入社会保障号码"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="姓名">
                  <a-input v-model="queryParam.aac003" allow-clear placeholder="请输入姓名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="证件类型">
                  <a-input v-model="queryParam.aac058" allow-clear placeholder="请输入证件类型"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="单位编号">
                  <a-input v-model="queryParam.aab001" allow-clear placeholder="请输入单位编号"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="统一社会信用代码">
                  <a-input v-model="queryParam.bab010" allow-clear placeholder="请输入统一社会信用代码"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="单位名称">
                  <a-input v-model="queryParam.aab004" allow-clear placeholder="请输入单位名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="险种类型">
                  <a-input v-model="queryParam.aae140" allow-clear placeholder="请输入险种类型"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="人员参保状态">
                  <a-input v-model="queryParam.aac008" allow-clear placeholder="请输入人员参保状态"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="个人缴费状态">
                  <a-input v-model="queryParam.aac031" allow-clear placeholder="请输入个人缴费状态"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="行政区划代码">
                  <a-input v-model="queryParam.aab301" allow-clear placeholder="请输入行政区划代码"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="所属地市">
                  <a-input v-model="queryParam.dscCity" allow-clear placeholder="请输入所属地市"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="所需区/县">
                  <a-input v-model="queryParam.dscAdmRegion" allow-clear placeholder="请输入所需区/县"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="数源单位代码">
                  <a-input v-model="queryParam.dscSydepCode" allow-clear placeholder="请输入数源单位代码"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="数源单位">
                  <a-input v-model="queryParam.dscSydepName" allow-clear placeholder="请输入数源单位"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="数据所属系统名称">
                  <a-input v-model="queryParam.dscSydepSys" allow-clear placeholder="请输入数据所属系统名称"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="数源单位表名">
                  <a-input v-model="queryParam.dscSydepTblname" allow-clear placeholder="请输入数源单位表名"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="唯一自增序列号">
                  <a-input v-model="queryParam.dscBizRecordId" allow-clear placeholder="请输入唯一自增序列号"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="I 插入 U 更新 D 删除">
                  <a-input v-model="queryParam.dscBizOperation" allow-clear placeholder="请输入I 插入 U 更新 D 删除"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="源表数据同步时间">
                  <a-date-picker style="width: 100%" placeholder="请选择源表数据同步时间" v-model="queryParam.dscBizTimestampDate" @change="onChangedscBizTimestamp"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="数据来源表名(清洗库或基础库 表名)">
                  <a-input v-model="queryParam.dscDatasrTblname" allow-clear placeholder="请输入数据来源表名(清洗库或基础库 表名)"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="业务主键MD5值（清洗增加）">
                  <a-input v-model="queryParam.dscHashUnique" allow-clear placeholder="请输入业务主键MD5值（清洗增加）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="清洗时间（清洗增加）">
                  <a-input v-model="queryParam.dscCleanTimestamp" allow-clear placeholder="请输入清洗时间（清洗增加）"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="地市仓数据入库时间">
                  <a-date-picker style="width: 100%" placeholder="请选择地市仓数据入库时间" v-model="queryParam.dscDwRksjDate" @change="onChangedscDwRksj"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="区县仓数据入库时间">
                  <a-date-picker style="width: 100%" placeholder="请选择区县仓数据入库时间" v-model="queryParam.etlDateDate" @change="onChangeetlDate"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.aac147"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq:add')" >
          <a-button type="primary" v-if="hasPerm('zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq:add')" icon="plus" @click="$refs.addForm.add()">新增省下发_社会保险个人参保信息</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq:edit') & hasPerm('zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq:delete')"/>
          <a-popconfirm v-if="hasPerm('zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq:delete')" placement="topRight" title="确认删除？" @confirm="() => zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqPage, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqDelete } from '@/api/modular/main/zjdscdd029sjjhdsjjac02deltagtoldhzlpq/zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '基准参保关系ID',
            align: 'center',
            dataIndex: 'baz159'
          },
          {
            title: '个人编号',
            align: 'center',
            dataIndex: 'aac001'
          },
          {
            title: '社会保障号码',
            align: 'center',
            dataIndex: 'aac002'
          },
          {
            title: '姓名',
            align: 'center',
            dataIndex: 'aac003'
          },
          {
            title: '证件类型',
            align: 'center',
            dataIndex: 'aac058'
          },
          {
            title: '单位编号',
            align: 'center',
            dataIndex: 'aab001'
          },
          {
            title: '统一社会信用代码',
            align: 'center',
            dataIndex: 'bab010'
          },
          {
            title: '单位名称',
            align: 'center',
            dataIndex: 'aab004'
          },
          {
            title: '险种类型',
            align: 'center',
            dataIndex: 'aae140'
          },
          {
            title: '人员参保状态',
            align: 'center',
            dataIndex: 'aac008'
          },
          {
            title: '个人缴费状态',
            align: 'center',
            dataIndex: 'aac031'
          },
          {
            title: '开始日期',
            align: 'center',
            dataIndex: 'aae030'
          },
          {
            title: '终止日期',
            align: 'center',
            dataIndex: 'aae031'
          },
          {
            title: '行政区划代码',
            align: 'center',
            dataIndex: 'aab301'
          },
          {
            title: '所属地市',
            align: 'center',
            dataIndex: 'dscCity'
          },
          {
            title: '所需区/县',
            align: 'center',
            dataIndex: 'dscAdmRegion'
          },
          {
            title: '数源单位代码',
            align: 'center',
            dataIndex: 'dscSydepCode'
          },
          {
            title: '数源单位',
            align: 'center',
            dataIndex: 'dscSydepName'
          },
          {
            title: '数据所属系统名称',
            align: 'center',
            dataIndex: 'dscSydepSys'
          },
          {
            title: '数源单位表名',
            align: 'center',
            dataIndex: 'dscSydepTblname'
          },
          {
            title: '唯一自增序列号',
            align: 'center',
            dataIndex: 'dscBizRecordId'
          },
          {
            title: 'I 插入 U 更新 D 删除',
            align: 'center',
            dataIndex: 'dscBizOperation'
          },
          {
            title: '源表数据同步时间',
            align: 'center',
            dataIndex: 'dscBizTimestamp'
          },
          {
            title: '数据来源表名(清洗库或基础库 表名)',
            align: 'center',
            dataIndex: 'dscDatasrTblname'
          },
          {
            title: '业务主键MD5值（清洗增加）',
            align: 'center',
            dataIndex: 'dscHashUnique'
          },
          {
            title: '清洗时间（清洗增加）',
            align: 'center',
            dataIndex: 'dscCleanTimestamp'
          },
          {
            title: '地市仓数据入库时间',
            align: 'center',
            dataIndex: 'dscDwRksj'
          },
          {
            title: '区县仓数据入库时间',
            align: 'center',
            dataIndex: 'etlDate'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq:edit') || this.hasPerm('zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const queryParamdscBizTimestamp = this.queryParam.dscBizTimestampDate
        if (queryParamdscBizTimestamp != null) {
            this.queryParam.dscBizTimestamp = moment(queryParamdscBizTimestamp).format('YYYY-MM-DD')
            if (queryParamdscBizTimestamp.length < 1) {
                delete this.queryParam.dscBizTimestamp
            }
        }
        const queryParamdscDwRksj = this.queryParam.dscDwRksjDate
        if (queryParamdscDwRksj != null) {
            this.queryParam.dscDwRksj = moment(queryParamdscDwRksj).format('YYYY-MM-DD')
            if (queryParamdscDwRksj.length < 1) {
                delete this.queryParam.dscDwRksj
            }
        }
        const queryParametlDate = this.queryParam.etlDateDate
        if (queryParametlDate != null) {
            this.queryParam.etlDate = moment(queryParametlDate).format('YYYY-MM-DD')
            if (queryParametlDate.length < 1) {
                delete this.queryParam.etlDate
            }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        return obj
      },
      zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqDelete (record) {
        zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      onChangedscBizTimestamp(date, dateString) {
        this.dscBizTimestampDateString = dateString
      },
      onChangedscDwRksj(date, dateString) {
        this.dscDwRksjDateString = dateString
      },
      onChangeetlDate(date, dateString) {
        this.etlDateDateString = dateString
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
