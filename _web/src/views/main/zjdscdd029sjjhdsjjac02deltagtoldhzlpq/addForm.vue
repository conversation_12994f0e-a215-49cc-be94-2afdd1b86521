<template>
  <a-modal
    title="新增省下发_社会保险个人参保信息"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          label="基准参保关系ID"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入基准参保关系ID" v-decorator="['baz159', {rules: [{required: true, message: '请输入基准参保关系ID！'}]}]" />
        </a-form-item>
        <a-form-item
          label="个人编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入个人编号" v-decorator="['aac001', {rules: [{required: true, message: '请输入个人编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="社会保障号码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入社会保障号码" v-decorator="['aac002', {rules: [{required: true, message: '请输入社会保障号码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="姓名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入姓名" v-decorator="['aac003', {rules: [{required: true, message: '请输入姓名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="证件类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入证件类型" v-decorator="['aac058', {rules: [{required: true, message: '请输入证件类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="单位编号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入单位编号" v-decorator="['aab001', {rules: [{required: true, message: '请输入单位编号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="统一社会信用代码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入统一社会信用代码" v-decorator="['bab010', {rules: [{required: true, message: '请输入统一社会信用代码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="单位名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入单位名称" v-decorator="['aab004', {rules: [{required: true, message: '请输入单位名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="险种类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入险种类型" v-decorator="['aae140', {rules: [{required: true, message: '请输入险种类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="人员参保状态"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入人员参保状态" v-decorator="['aac008', {rules: [{required: true, message: '请输入人员参保状态！'}]}]" />
        </a-form-item>
        <a-form-item
          label="个人缴费状态"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入个人缴费状态" v-decorator="['aac031', {rules: [{required: true, message: '请输入个人缴费状态！'}]}]" />
        </a-form-item>
        <a-form-item
          label="开始日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="终止日期"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
        </a-form-item>
        <a-form-item
          label="行政区划代码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入行政区划代码" v-decorator="['aab301', {rules: [{required: true, message: '请输入行政区划代码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="所属地市"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入所属地市" v-decorator="['dscCity', {rules: [{required: true, message: '请输入所属地市！'}]}]" />
        </a-form-item>
        <a-form-item
          label="所需区/县"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入所需区/县" v-decorator="['dscAdmRegion', {rules: [{required: true, message: '请输入所需区/县！'}]}]" />
        </a-form-item>
        <a-form-item
          label="数源单位代码"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入数源单位代码" v-decorator="['dscSydepCode', {rules: [{required: true, message: '请输入数源单位代码！'}]}]" />
        </a-form-item>
        <a-form-item
          label="数源单位"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入数源单位" v-decorator="['dscSydepName', {rules: [{required: true, message: '请输入数源单位！'}]}]" />
        </a-form-item>
        <a-form-item
          label="数据所属系统名称"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入数据所属系统名称" v-decorator="['dscSydepSys', {rules: [{required: true, message: '请输入数据所属系统名称！'}]}]" />
        </a-form-item>
        <a-form-item
          label="数源单位表名"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入数源单位表名" v-decorator="['dscSydepTblname', {rules: [{required: true, message: '请输入数源单位表名！'}]}]" />
        </a-form-item>
        <a-form-item
          label="唯一自增序列号"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入唯一自增序列号" v-decorator="['dscBizRecordId', {rules: [{required: true, message: '请输入唯一自增序列号！'}]}]" />
        </a-form-item>
        <a-form-item
          label="I 插入 U 更新 D 删除"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入I 插入 U 更新 D 删除" v-decorator="['dscBizOperation', {rules: [{required: true, message: '请输入I 插入 U 更新 D 删除！'}]}]" />
        </a-form-item>
        <a-form-item
          label="源表数据同步时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择源表数据同步时间" v-decorator="['dscBizTimestamp',{rules: [{ required: true, message: '请选择源表数据同步时间！' }]}]" @change="onChangedscBizTimestamp"/>
        </a-form-item>
        <a-form-item
          label="数据来源表名(清洗库或基础库 表名)"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入数据来源表名(清洗库或基础库 表名)" v-decorator="['dscDatasrTblname', {rules: [{required: true, message: '请输入数据来源表名(清洗库或基础库 表名)！'}]}]" />
        </a-form-item>
        <a-form-item
          label="业务主键MD5值（清洗增加）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入业务主键MD5值（清洗增加）" v-decorator="['dscHashUnique', {rules: [{required: true, message: '请输入业务主键MD5值（清洗增加）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="清洗时间（清洗增加）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入清洗时间（清洗增加）" v-decorator="['dscCleanTimestamp', {rules: [{required: true, message: '请输入清洗时间（清洗增加）！'}]}]" />
        </a-form-item>
        <a-form-item
          label="地市仓数据入库时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择地市仓数据入库时间" v-decorator="['dscDwRksj',{rules: [{ required: true, message: '请选择地市仓数据入库时间！' }]}]" @change="onChangedscDwRksj"/>
        </a-form-item>
        <a-form-item
          label="区县仓数据入库时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择区县仓数据入库时间" v-decorator="['etlDate',{rules: [{ required: true, message: '请选择区县仓数据入库时间！' }]}]" @change="onChangeetlDate"/>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqAdd } from '@/api/modular/main/zjdscdd029sjjhdsjjac02deltagtoldhzlpq/zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        dscBizTimestampDateString: '',
        dscDwRksjDateString: '',
        etlDateDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      add (record) {
        this.visible = true
      },
      /**
       * 提交表单
       */
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.dscBizTimestamp = this.dscBizTimestampDateString
            values.dscDwRksj = this.dscDwRksjDateString
            values.etlDate = this.etlDateDateString
            zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqAdd(values).then((res) => {
              if (res.success) {
                this.$message.success('新增成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('新增失败')// + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangedscBizTimestamp(date, dateString) {
        this.dscBizTimestampDateString = dateString
      },
      onChangedscDwRksj(date, dateString) {
        this.dscDwRksjDateString = dateString
      },
      onChangeetlDate(date, dateString) {
        this.etlDateDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
