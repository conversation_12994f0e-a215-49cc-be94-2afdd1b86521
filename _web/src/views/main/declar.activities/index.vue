<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('declarActivities:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="活动主题">
                <a-input v-model="queryParam.activityTheme" allow-clear placeholder="请输入活动主题"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="活动简介">
                <a-input v-model="queryParam.activityRemarks" allow-clear placeholder="请输入活动简介"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="服务种类">
                  <a-input v-model="queryParam.serviceCategory" allow-clear placeholder="请输入服务种类"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="服务事项">
                  <a-input v-model="queryParam.serviceItems" allow-clear placeholder="请输入服务事项"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="活动开始时间">
                  <a-date-picker style="width: 100%" placeholder="请选择活动开始时间" v-model="queryParam.activitieStartTimeDate" @change="onChangeactivitieStartTime"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="活动开始时间">
                  <a-date-picker style="width: 100%" placeholder="请选择活动开始时间" v-model="queryParam.activityEndTimeDate" @change="onChangeactivityEndTime"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="服务人员类型">
                  <a-input v-model="queryParam.servicePersonnelType" allow-clear placeholder="请输入服务人员类型"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="状态（字典 0正常 1停用 2删除）">
                  <a-input v-model="queryParam.status" allow-clear placeholder="请输入状态（字典 0正常 1停用 2删除）"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('declarActivities:add')" >
          <a-button type="primary" v-if="hasPerm('declarActivities:add')" icon="plus" @click="$refs.addForm.add()">新增申报活动</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('declarActivities:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('declarActivities:edit') & hasPerm('declarActivities:delete')"/>
          <a-popconfirm v-if="hasPerm('declarActivities:delete')" placement="topRight" title="确认删除？" @confirm="() => declarActivitiesDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import moment from 'moment'
  import { declarActivitiesPage, declarActivitiesDelete } from '@/api/modular/main/declar.activities/declarActivitiesManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '活动主题',
            align: 'center',
            dataIndex: 'activityTheme'
          },
          {
            title: '活动简介',
            align: 'center',
            dataIndex: 'activityRemarks'
          },
          {
            title: '服务种类',
            align: 'center',
            dataIndex: 'serviceCategory'
          },
          {
            title: '服务事项',
            align: 'center',
            dataIndex: 'serviceItems'
          },
          {
            title: '活动开始时间',
            align: 'center',
            dataIndex: 'activitieStartTime'
          },
          {
            title: '活动开始时间',
            align: 'center',
            dataIndex: 'activityEndTime'
          },
          {
            title: '服务人员类型',
            align: 'center',
            dataIndex: 'servicePersonnelType'
          },
          {
            title: '状态（字典 0正常 1停用 2删除）',
            align: 'center',
            dataIndex: 'status'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return declarActivitiesPage(Object.assign(parameter, this.switchingDate())).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('declarActivities:edit') || this.hasPerm('declarActivities:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      moment,
      /**
       * 查询参数组装
       */
      switchingDate () {
        const queryParamactivitieStartTime = this.queryParam.activitieStartTimeDate
        if (queryParamactivitieStartTime != null) {
            this.queryParam.activitieStartTime = moment(queryParamactivitieStartTime).format('YYYY-MM-DD')
            if (queryParamactivitieStartTime.length < 1) {
                delete this.queryParam.activitieStartTime
            }
        }
        const queryParamactivityEndTime = this.queryParam.activityEndTimeDate
        if (queryParamactivityEndTime != null) {
            this.queryParam.activityEndTime = moment(queryParamactivityEndTime).format('YYYY-MM-DD')
            if (queryParamactivityEndTime.length < 1) {
                delete this.queryParam.activityEndTime
            }
        }
        const obj = JSON.parse(JSON.stringify(this.queryParam))
        return obj
      },
      declarActivitiesDelete (record) {
        declarActivitiesDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      onChangeactivitieStartTime(date, dateString) {
        this.activitieStartTimeDateString = dateString
      },
      onChangeactivityEndTime(date, dateString) {
        this.activityEndTimeDateString = dateString
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
