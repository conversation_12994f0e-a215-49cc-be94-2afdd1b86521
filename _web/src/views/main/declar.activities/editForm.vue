<template>
  <a-modal
    title="编辑申报活动"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="活动主题"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入活动主题" v-decorator="['activityTheme', {rules: [{required: true, message: '请输入活动主题！'}]}]" />
        </a-form-item>
        <a-form-item
          label="活动简介"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入活动简介" v-decorator="['activityRemarks', {rules: [{required: true, message: '请输入活动简介！'}]}]" />
        </a-form-item>
        <a-form-item
          label="服务种类"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入服务种类" v-decorator="['serviceCategory', {rules: [{required: true, message: '请输入服务种类！'}]}]" />
        </a-form-item>
        <a-form-item
          label="服务事项"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入服务事项" v-decorator="['serviceItems', {rules: [{required: true, message: '请输入服务事项！'}]}]" />
        </a-form-item>
        <a-form-item
          label="活动开始时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择活动开始时间" v-decorator="['activitieStartTime',{rules: [{ required: true, message: '请选择活动开始时间！' }]}]" @change="onChangeactivitieStartTime"/>
        </a-form-item>
        <a-form-item
          label="活动开始时间"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-date-picker style="width: 100%" placeholder="请选择活动开始时间" v-decorator="['activityEndTime',{rules: [{ required: true, message: '请选择活动开始时间！' }]}]" @change="onChangeactivityEndTime"/>
        </a-form-item>
        <a-form-item
          label="服务人员类型"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入服务人员类型" v-decorator="['servicePersonnelType', {rules: [{required: true, message: '请输入服务人员类型！'}]}]" />
        </a-form-item>
        <a-form-item
          label="状态（字典 0正常 1停用 2删除）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入状态（字典 0正常 1停用 2删除）" v-decorator="['status', {rules: [{required: true, message: '请输入状态（字典 0正常 1停用 2删除）！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import moment from 'moment'
  import { declarActivitiesEdit } from '@/api/modular/main/declar.activities/declarActivitiesManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        activitieStartTimeDateString: '',
        activityEndTimeDateString: '',
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      moment,
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              activityTheme: record.activityTheme,
              activityRemarks: record.activityRemarks,
              serviceCategory: record.serviceCategory,
              serviceItems: record.serviceItems,
              servicePersonnelType: record.servicePersonnelType,
              status: record.status
            }
          )
        }, 100)
        // 时间单独处理
        if (record.activitieStartTime != null) {
            this.form.getFieldDecorator('activitieStartTime', { initialValue: moment(record.activitieStartTime, 'YYYY-MM-DD') })
        }
        this.activitieStartTimeDateString = moment(record.activitieStartTime).format('YYYY-MM-DD')
        // 时间单独处理
        if (record.activityEndTime != null) {
            this.form.getFieldDecorator('activityEndTime', { initialValue: moment(record.activityEndTime, 'YYYY-MM-DD') })
        }
        this.activityEndTimeDateString = moment(record.activityEndTime).format('YYYY-MM-DD')
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            values.activitieStartTime = this.activitieStartTimeDateString
            values.activityEndTime = this.activityEndTimeDateString
            declarActivitiesEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      onChangeactivitieStartTime(date, dateString) {
        this.activitieStartTimeDateString = dateString
      },
      onChangeactivityEndTime(date, dateString) {
        this.activityEndTimeDateString = dateString
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
