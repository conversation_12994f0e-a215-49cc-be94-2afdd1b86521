<template>
  <a-modal
    title="编辑申报"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item v-show="false"><a-input v-decorator="['id']" /></a-form-item>
        <a-form-item
          label="审核结果"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入审核结果" v-decorator="['reviewResults', {rules: [{required: true, message: '请输入审核结果！'}]}]" />
        </a-form-item>
        <a-form-item
          label="审核意见"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入审核意见" v-decorator="['auditOpinion', {rules: [{required: true, message: '请输入审核意见！'}]}]" />
        </a-form-item>
        <a-form-item
          label="填报人ID"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入填报人ID" v-decorator="['resultId', {rules: [{required: true, message: '请输入填报人ID！'}]}]" />
        </a-form-item>
        <a-form-item
          label="服务种类"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入服务种类" v-decorator="['serviceCategory', {rules: [{required: true, message: '请输入服务种类！'}]}]" />
        </a-form-item>
        <a-form-item
          label="服务事项"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入服务事项" v-decorator="['serviceItems', {rules: [{required: true, message: '请输入服务事项！'}]}]" />
        </a-form-item>
        <a-form-item
          label="工作简介"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入工作简介" v-decorator="['remarks', {rules: [{required: true, message: '请输入工作简介！'}]}]" />
        </a-form-item>
        <a-form-item
          label="申报附件"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入申报附件" v-decorator="['declareAtt', {rules: [{required: true, message: '请输入申报附件！'}]}]" />
        </a-form-item>
        <a-form-item
          label="状态（字典 0正常 1停用 2删除）"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          has-feedback
        >
          <a-input placeholder="请输入状态（字典 0正常 1停用 2删除）" v-decorator="['status', {rules: [{required: true, message: '请输入状态（字典 0正常 1停用 2删除）！'}]}]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
  import { declarEdit } from '@/api/modular/main/declar/declarManage'
  export default {
    data () {
      return {
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 }
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 15 }
        },
        visible: false,
        confirmLoading: false,
        form: this.$form.createForm(this)
      }
    },
    methods: {
      // 初始化方法
      edit (record) {
        this.visible = true
        setTimeout(() => {
          this.form.setFieldsValue(
            {
              id: record.id,
              reviewResults: record.reviewResults,
              auditOpinion: record.auditOpinion,
              resultId: record.resultId,
              serviceCategory: record.serviceCategory,
              serviceItems: record.serviceItems,
              remarks: record.remarks,
              declareAtt: record.declareAtt,
              status: record.status
            }
          )
        }, 100)
      },
      handleSubmit () {
        const { form: { validateFields } } = this
        this.confirmLoading = true
        validateFields((errors, values) => {
          if (!errors) {
            for (const key in values) {
              if (typeof (values[key]) === 'object') {
                values[key] = JSON.stringify(values[key])
              }
            }
            declarEdit(values).then((res) => {
              if (res.success) {
                this.$message.success('编辑成功')
                this.confirmLoading = false
                this.$emit('ok', values)
                this.handleCancel()
              } else {
                this.$message.error('编辑失败')//  + res.message
              }
            }).finally((res) => {
              this.confirmLoading = false
            })
          } else {
            this.confirmLoading = false
          }
        })
      },
      handleCancel () {
        this.form.resetFields()
        this.visible = false
      }
    }
  }
</script>
