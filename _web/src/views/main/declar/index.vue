<template>
  <div>
    <a-card :bordered="false" :bodyStyle="tstyle">
      <div class="table-page-search-wrapper" v-if="hasPerm('declar:page')">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="审核结果">
                <a-input v-model="queryParam.reviewResults" allow-clear placeholder="请输入审核结果"/>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="审核意见">
                <a-input v-model="queryParam.auditOpinion" allow-clear placeholder="请输入审核意见"/>
              </a-form-item>
            </a-col>
            <template v-if="advanced">
              <a-col :md="8" :sm="24">
                <a-form-item label="填报人ID">
                  <a-input v-model="queryParam.resultId" allow-clear placeholder="请输入填报人ID"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="服务种类">
                  <a-input v-model="queryParam.serviceCategory" allow-clear placeholder="请输入服务种类"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="服务事项">
                  <a-input v-model="queryParam.serviceItems" allow-clear placeholder="请输入服务事项"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="工作简介">
                  <a-input v-model="queryParam.remarks" allow-clear placeholder="请输入工作简介"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="申报附件">
                  <a-input v-model="queryParam.declareAtt" allow-clear placeholder="请输入申报附件"/>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="24">
                <a-form-item label="状态（字典 0正常 1停用 2删除）">
                  <a-input v-model="queryParam.status" allow-clear placeholder="请输入状态（字典 0正常 1停用 2删除）"/>
                </a-form-item>
              </a-col>
            </template>
            <a-col :md="8" :sm="24" >
              <span class="table-page-search-submitButtons">
                <a-button type="primary" @click="$refs.table.refresh(true)" >查询</a-button>
                <a-button style="margin-left: 8px" @click="() => queryParam = {}">重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false">
      <s-table
        ref="table"
        :columns="columns"
        :data="loadData"
        :alert="true"
        :rowKey="(record) => record.id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template class="table-operator" slot="operator" v-if="hasPerm('declar:add')" >
          <a-button type="primary" v-if="hasPerm('declar:add')" icon="plus" @click="$refs.addForm.add()">新增申报</a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a v-if="hasPerm('declar:edit')" @click="$refs.editForm.edit(record)">编辑</a>
          <a-divider type="vertical" v-if="hasPerm('declar:edit') & hasPerm('declar:delete')"/>
          <a-popconfirm v-if="hasPerm('declar:delete')" placement="topRight" title="确认删除？" @confirm="() => declarDelete(record)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </s-table>
      <add-form ref="addForm" @ok="handleOk" />
      <edit-form ref="editForm" @ok="handleOk" />
    </a-card>
  </div>
</template>
<script>
  import { STable } from '@/components'
  import { declarPage, declarDelete } from '@/api/modular/main/declar/declarManage'
  import addForm from './addForm.vue'
  import editForm from './editForm.vue'
  export default {
    components: {
      STable,
      addForm,
      editForm
    },
    data () {
      return {
        // 高级搜索 展开/关闭
        advanced: false,
        // 查询参数
        queryParam: {},
        // 表头
        columns: [
          {
            title: '审核结果',
            align: 'center',
            dataIndex: 'reviewResults'
          },
          {
            title: '审核意见',
            align: 'center',
            dataIndex: 'auditOpinion'
          },
          {
            title: '填报人ID',
            align: 'center',
            dataIndex: 'resultId'
          },
          {
            title: '服务种类',
            align: 'center',
            dataIndex: 'serviceCategory'
          },
          {
            title: '服务事项',
            align: 'center',
            dataIndex: 'serviceItems'
          },
          {
            title: '工作简介',
            align: 'center',
            dataIndex: 'remarks'
          },
          {
            title: '申报附件',
            align: 'center',
            dataIndex: 'declareAtt'
          },
          {
            title: '状态（字典 0正常 1停用 2删除）',
            align: 'center',
            dataIndex: 'status'
          }
        ],
        tstyle: { 'padding-bottom': '0px', 'margin-bottom': '10px' },
        // 加载数据方法 必须为 Promise 对象
        loadData: parameter => {
          return declarPage(Object.assign(parameter, this.queryParam)).then((res) => {
            return res.data
          })
        },
        selectedRowKeys: [],
        selectedRows: []
      }
    },
    created () {
      if (this.hasPerm('declar:edit') || this.hasPerm('declar:delete')) {
        this.columns.push({
          title: '操作',
          width: '150px',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' }
        })
      }
    },
    methods: {
      declarDelete (record) {
        declarDelete(record).then((res) => {
          if (res.success) {
            this.$message.success('删除成功')
            this.$refs.table.refresh()
          } else {
            this.$message.error('删除失败') // + res.message
          }
        })
      },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleOk () {
        this.$refs.table.refresh()
      },
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
      }
    }
  }
</script>
<style lang="less">
  .table-operator {
    margin-bottom: 18px;
  }
  button {
    margin-right: 8px;
  }
</style>
