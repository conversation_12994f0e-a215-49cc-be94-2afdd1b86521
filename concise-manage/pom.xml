<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.integralN</groupId>
        <artifactId>concise</artifactId>
        <version>1.6.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>concise-manage</artifactId>
    <packaging>jar</packaging>
    <dependencies>

        <dependency>
            <groupId>com.integralN</groupId>
            <artifactId>concise-system</artifactId>
            <version>1.6.0</version>
        </dependency>

        <!-- 代码生成模块 -->
        <dependency>
            <groupId>com.integralN</groupId>
            <artifactId>concise-gen</artifactId>
            <version>1.6.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.xxpt.gateway.shared</groupId>
            <artifactId>zwdd-sdk-java-1.2.0</artifactId>
            <version>1.2.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/zwdd-sdk-java-1.2.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.nimbusds</groupId>
            <artifactId>nimbus-jose-jwt</artifactId>
            <version>9.12.1</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>digital-cockpit-lp-boot</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <fork>true</fork>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
