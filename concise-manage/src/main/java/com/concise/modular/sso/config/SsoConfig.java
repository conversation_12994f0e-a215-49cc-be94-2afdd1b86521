package com.concise.modular.sso.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * SSO配置类
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Data
@Component
@ConfigurationProperties(prefix = "sso")
public class SsoConfig {

    /**
     * 是否启用SSO功能
     */
    private Boolean enabled = true;

    /**
     * 默认令牌有效期（秒）
     */
    private Integer defaultTokenExpireSeconds = 300;

    /**
     * 最大令牌有效期（秒）
     */
    private Integer maxTokenExpireSeconds = 1800;

    /**
     * 加密算法
     */
    private String encryptionAlgorithm = "SM4";

    /**
     * 密钥长度
     */
    private Integer keyLength = 16;

    /**
     * 基础密钥（用于生成动态密钥）
     */
    private String baseSecretKey = "ADHFMUudFU1DHKHB";

    /**
     * 支持的目标系统配置
     */
    private Map<String, SystemConfig> systems;

    /**
     * 是否启用时间戳验证
     */
    private Boolean enableTimestampValidation = true;

    /**
     * 是否启用IP白名单验证
     */
    private Boolean enableIpWhitelist = false;

    /**
     * IP白名单
     */
    private List<String> ipWhitelist;

    /**
     * 是否记录SSO日志
     */
    private Boolean enableLogging = true;

    /**
     * 系统配置内部类
     */
    @Data
    public static class SystemConfig {
        /**
         * 系统名称
         */
        private String name;

        /**
         * 系统描述
         */
        private String description;

        /**
         * 系统URL
         */
        private String url;

        /**
         * 是否启用
         */
        private Boolean enabled = true;

        /**
         * 自定义令牌有效期（秒）
         */
        private Integer tokenExpireSeconds;

        /**
         * 自定义密钥
         */
        private String customSecretKey;

        /**
         * 回调URL
         */
        private String callbackUrl;

        /**
         * 扩展配置
         */
        private Map<String, Object> extendConfig;
    }

    /**
     * 获取系统配置
     *
     * @param systemCode 系统代码
     * @return 系统配置
     */
    public SystemConfig getSystemConfig(String systemCode) {
        if (systems != null && systems.containsKey(systemCode)) {
            return systems.get(systemCode);
        }
        return null;
    }

    /**
     * 获取系统的令牌有效期
     *
     * @param systemCode 系统代码
     * @return 令牌有效期（秒）
     */
    public Integer getTokenExpireSeconds(String systemCode) {
        SystemConfig systemConfig = getSystemConfig(systemCode);
        if (systemConfig != null && systemConfig.getTokenExpireSeconds() != null) {
            return systemConfig.getTokenExpireSeconds();
        }
        return defaultTokenExpireSeconds;
    }

    /**
     * 获取系统的密钥
     *
     * @param systemCode 系统代码
     * @return 密钥
     */
    public String getSecretKey(String systemCode) {
        SystemConfig systemConfig = getSystemConfig(systemCode);
        if (systemConfig != null && systemConfig.getCustomSecretKey() != null) {
            return systemConfig.getCustomSecretKey();
        }
        return baseSecretKey;
    }

    /**
     * 检查系统是否启用
     *
     * @param systemCode 系统代码
     * @return 是否启用
     */
    public Boolean isSystemEnabled(String systemCode) {
        SystemConfig systemConfig = getSystemConfig(systemCode);
        if (systemConfig != null) {
            return systemConfig.getEnabled();
        }
        return false;
    }
}
