package com.concise.modular.sso.service;

import java.util.List;
import java.util.Map;

import com.concise.modular.sso.entity.SsoTokenData;
import com.concise.modular.sso.entity.SsoUserInfo;

/**
 * 单点登录服务接口
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
public interface SsoService {

    /**
     * 生成SSO令牌
     *
     * @param userId 用户ID
     * @param targetSystem 目标系统标识
     * @return SSO令牌数据
     */
    SsoTokenData generateSsoToken(String userId, String targetSystem);

    /**
     * 解析SSO令牌（旧版本，兼容性保留）
     *
     * @param ssoToken 加密的SSO令牌
     * @param timestamp 时间戳
     * @param userId 用户ID
     * @return 用户信息
     */
    SsoUserInfo parseSsoToken(String ssoToken, String timestamp, String userId);

    /**
     * 解析SSO令牌（新版本，不需要用户ID）
     *
     * @param ssoToken 加密的SSO令牌
     * @return 用户信息
     */
    SsoUserInfo parseSsoToken(String ssoToken);

    /**
     * 验证SSO令牌是否有效（旧版本，兼容性保留）
     *
     * @param ssoToken 加密的SSO令牌
     * @param timestamp 时间戳
     * @param userId 用户ID
     * @return 是否有效
     */
    boolean validateSsoToken(String ssoToken, String timestamp, String userId);

    /**
     * 验证SSO令牌是否有效（新版本，不需要用户ID）
     *
     * @param ssoToken 加密的SSO令牌
     * @return 是否有效
     */
    boolean validateSsoToken(String ssoToken);

    /**
     * 生成单点登录跳转URL
     *
     * @param targetUrl 目标系统URL
     * @param userId 用户ID
     * @param targetSystem 目标系统标识
     * @return 完整的跳转URL
     */
    String generateSsoUrl(String targetUrl, String userId, String targetSystem);

    /**
     * 刷新SSO令牌
     *
     * @param oldToken 旧令牌
     * @param userId 用户ID
     * @return 新的SSO令牌数据
     */
    SsoTokenData refreshSsoToken(String oldToken, String userId);

    /**
     * 获取用户所有机构列表
     *
     * @param userId 用户ID
     * @return 机构列表
     */
    List<Map<String, Object>> getUserOrganizations(String userId);

    /**
     * 切换用户当前机构
     *
     * @param userId 用户ID
     * @param orgId 目标机构ID
     * @return 是否切换成功
     */
    boolean switchUserOrganization(String userId, Long orgId);
}
