package com.concise.modular.sso.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.concise.common.context.constant.ConstantContextHolder;
import com.concise.common.util.HttpServletUtil;
import com.concise.core.context.login.LoginContext;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.core.login.SysLoginUser;
import com.concise.modular.sso.config.SsoConfig;
import com.concise.modular.sso.entity.SsoTokenData;
import com.concise.modular.sso.entity.SsoUserInfo;
import com.concise.modular.sso.exception.SsoException;
import com.concise.modular.sso.service.SsoService;
import com.concise.modular.sso.util.SsoTokenEncoder;
import com.concise.modular.sso.util.SsoUtil;
import com.concise.sys.core.cache.UserCache;
import com.concise.sys.core.jwt.JwtPayLoad;
import com.concise.sys.core.jwt.JwtTokenUtil;
import com.concise.sys.modular.auth.service.AuthService;
import com.concise.sys.modular.emp.entity.SysEmp;
import com.concise.sys.modular.emp.service.SysEmpService;
import com.concise.sys.modular.org.entity.SysOrg;
import com.concise.sys.modular.org.service.SysOrgService;
import com.concise.sys.modular.user.entity.SysUser;
import com.concise.sys.modular.user.service.SysUserService;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 单点登录服务实现类
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Slf4j
@Service
public class SsoServiceImpl implements SsoService {

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysEmpService sysEmpService;

    @Resource
    private SysOrgService sysOrgService;

    @Resource
    private SsoConfig ssoConfig;

    @Resource
    private SsoUtil ssoUtil;

    @Resource
    private SsoTokenEncoder ssoTokenEncoder;

    @Resource
    private AuthService authService;

    @Resource
    private UserCache userCache;

    @Override
    public SsoTokenData generateSsoToken(String userId, String targetSystem) {
        try {
            // 检查SSO功能是否启用
            if (!ssoConfig.getEnabled()) {
                throw SsoException.SsoErrorCode.SYSTEM_NOT_ENABLED.toException("SSO功能未启用");
            }

            // 检查目标系统是否启用
            if (StringUtils.isNotBlank(targetSystem) && !ssoUtil.isSystemEnabled(targetSystem)) {
                throw SsoException.SsoErrorCode.SYSTEM_NOT_ENABLED.toException("目标系统未启用: " + targetSystem);
            }

            // 验证用户是否存在
            SysUser sysUser = sysUserService.getById(userId);
            if (ObjectUtil.isNull(sysUser)) {
                throw SsoException.SsoErrorCode.USER_NOT_FOUND.toException("用户不存在: " + userId);
            }

            // 获取用户的机构信息
            SysEmp sysEmp = sysEmpService.getById(userId);

            // 构建SSO数据
            Map<String, Object> ssoData = new HashMap<>();
            ssoData.put("userId", userId);
            ssoData.put("account", sysUser.getAccount());
            ssoData.put("userName", sysUser.getName());
            ssoData.put("nickName", sysUser.getNickName());
            ssoData.put("timestamp", System.currentTimeMillis());

            // 添加机构信息
            if (ObjectUtil.isNotNull(sysEmp) && StringUtils.isNotBlank(sysEmp.getOrgId())) {
                ssoData.put("orgId", sysEmp.getOrgId());
                ssoData.put("orgName", sysEmp.getOrgName());

                // 获取详细机构信息
                SysOrg sysOrg = sysOrgService.getById(sysEmp.getOrgId());
                if (ObjectUtil.isNotNull(sysOrg)) {
                    ssoData.put("orgFullName", sysOrg.getName());
                    ssoData.put("orgCode", sysOrg.getCode());
                }
            }

            // 添加目标系统标识
            if (StringUtils.isNotBlank(targetSystem)) {
                ssoData.put("targetSystem", targetSystem);
            }

            // 添加安全标识
            ssoData.put("nonce", ssoUtil.generateNonce());

            // 使用固定密钥生成令牌（便于解析时不需要用户ID）
            String timestamp = ssoData.get("timestamp").toString();
            String secretKey = ssoConfig.getSecretKey(targetSystem);

            // 生成URL安全的令牌
            String urlSafeToken = ssoTokenEncoder.generateFullUrlSafeToken(ssoData, secretKey);

            // 构建返回结果
            SsoTokenData tokenData = new SsoTokenData(urlSafeToken, targetSystem);
            tokenData.setTimestamp(Long.parseLong(timestamp));
            tokenData.setExpireSeconds(ssoConfig.getTokenExpireSeconds(targetSystem));
            tokenData.setKeyId(ssoUtil.generateKeyId(userId, timestamp));


            return tokenData;

        } catch (SsoException e) {
            throw e;
        } catch (Exception e) {
            throw SsoException.SsoErrorCode.GENERATE_TOKEN_FAILED.toException("生成SSO令牌失败");
        }
    }

    @Override
    public SsoUserInfo parseSsoToken(String ssoToken, String timestamp, String userId) {
        try {
            // 验证时间戳
            if (!ssoUtil.validateTimestamp(timestamp)) {
                throw SsoException.SsoErrorCode.TIMESTAMP_INVALID.toException("SSO令牌已过期或时间戳无效");
            }

            // 生成解密密钥
            String dynamicKey = ssoUtil.generateDynamicKey(userId, timestamp);

            // 解密数据
            String decryptedData = ssoUtil.decryptData(ssoToken, dynamicKey);
            if (StringUtils.isBlank(decryptedData)) {
                throw SsoException.SsoErrorCode.TOKEN_DECRYPT_FAILED.toException("SSO令牌解密失败");
            }

            // 解析JSON数据
            JSONObject ssoData = JSONObject.parseObject(decryptedData);

            // 验证数据完整性
            if (!userId.equals(ssoData.getString("userId")) ||
                !timestamp.equals(ssoData.getString("timestamp"))) {
                throw SsoException.SsoErrorCode.TOKEN_INVALID.toException("SSO令牌数据不一致");
            }

            // 验证用户是否存在且有效
            SysUser sysUser = sysUserService.getById(userId);
            if (ObjectUtil.isNull(sysUser)) {
                throw SsoException.SsoErrorCode.USER_NOT_FOUND.toException("用户不存在");
            }

            // 构建用户信息
            SsoUserInfo userInfo = new SsoUserInfo();
            userInfo.setUserId(ssoData.getString("userId"));
            userInfo.setAccount(ssoData.getString("account"));
            userInfo.setUserName(ssoData.getString("userName"));
            userInfo.setNickName(ssoData.getString("nickName"));
            userInfo.setOrgId(ssoData.getString("orgId"));
            userInfo.setOrgName(ssoData.getString("orgName"));
            userInfo.setTargetSystem(ssoData.getString("targetSystem"));
            userInfo.setTokenTime(new Date(Long.parseLong(timestamp)));

            // 获取用户的所有机构信息
            List<SsoUserInfo.OrgInfo> orgList = getUserOrgList(userId);
            userInfo.setOrgList(orgList);

            log.info("SSO令牌解析成功，用户: {}, 机构: {}", userId, ssoData.getString("orgId"));
            return userInfo;

        } catch (SsoException e) {
            throw e;
        } catch (Exception e) {
            // 禁止输出堆栈信息，只记录异常类型
            log.error("解析SSO令牌失败，用户: {}，异常类型: {}", userId, e.getClass().getSimpleName());
            throw SsoException.SsoErrorCode.PARSE_TOKEN_FAILED.toException("解析SSO令牌失败");
        }
    }

    @Override
    public boolean validateSsoToken(String ssoToken, String timestamp, String userId) {
        try {
            parseSsoToken(ssoToken, timestamp, userId);
            return true;
        } catch (Exception e) {
            // 禁止输出堆栈信息，只记录异常类型
            log.warn("SSO令牌验证失败，用户: {}，异常类型: {}", userId, e.getClass().getSimpleName());
            return false;
        }
    }

    @Override
    public String generateSsoUrl(String targetUrl, String userId, String targetSystem) {
        try {
            // 生成SSO令牌
            SsoTokenData tokenData = generateSsoToken(userId, targetSystem);

            // 使用工具类构建URL
            String finalUrl = ssoUtil.buildSsoUrl(targetUrl, tokenData.getSsoToken(),
                                                 tokenData.getTimestamp().toString(), targetSystem);
            tokenData.setRedirectUrl(finalUrl);

            log.info("生成SSO跳转URL成功，用户: {}, 目标系统: {}", userId, targetSystem);
            return finalUrl;

        } catch (SsoException e) {
            throw e;
        } catch (Exception e) {
            // 禁止输出堆栈信息，只记录异常类型
            log.error("生成SSO跳转URL失败，用户: {}，目标系统: {}，异常类型: {}", userId, targetSystem, e.getClass().getSimpleName());
            throw new RuntimeException("生成SSO跳转URL失败");
        }
    }

    @Override
    public SsoTokenData refreshSsoToken(String oldToken, String userId) {
        // 刷新令牌实际上就是重新生成一个新的令牌
        return generateSsoToken(userId, null);
    }

    @Override
    public List<Map<String, Object>> getUserOrganizations(String userId) {
        try {
            log.info("获取用户{}的机构列表", userId);
            
            List<Map<String, Object>> result = new ArrayList<>();
            
            // 获取用户的所有机构信息
            List<SsoUserInfo.OrgInfo> orgInfoList = getUserOrgList(userId);
            
            // 转换为前端需要的格式
            for (SsoUserInfo.OrgInfo orgInfo : orgInfoList) {
                Map<String, Object> orgMap = new HashMap<>();
                orgMap.put("orgId", orgInfo.getOrgId());
                orgMap.put("orgName", orgInfo.getOrgName());
                orgMap.put("isPrimary", orgInfo.getIsPrimary());
                orgMap.put("position", orgInfo.getPosition());
                
                // 获取更多机构信息
                SysOrg sysOrg = sysOrgService.getById(orgInfo.getOrgId());
                if (ObjectUtil.isNotNull(sysOrg)) {
                    orgMap.put("orgCode", sysOrg.getCode());
                    orgMap.put("parentId", sysOrg.getPid());
                    orgMap.put("sortCode", sysOrg.getSort());
                }
                
                result.add(orgMap);
            }
            
            return result;
            
        } catch (Exception e) {
            // 禁止输出堆栈信息，只记录异常类型
            log.error("获取用户机构列表失败，用户: {}，异常类型: {}", userId, e.getClass().getSimpleName());
            throw new RuntimeException("获取用户机构列表失败");
        }
    }

    @Override
    public boolean switchUserOrganization(String userId, Long orgId) {
        try {
            log.info("用户{}请求切换到机构{}", userId, orgId);

            // 验证用户是否存在
            SysUser sysUser = sysUserService.getById(userId);
            if (ObjectUtil.isNull(sysUser)) {
                log.warn("用户不存在: {}", userId);
                return false;
            }

            // 判断用户是否有权限访问该机构
            boolean hasPermission = false;
            List<SsoUserInfo.OrgInfo> orgInfoList = getUserOrgList(userId);
            for (SsoUserInfo.OrgInfo orgInfo : orgInfoList) {
                if (orgInfo.getOrgId().equals(String.valueOf(orgId))) {
                    hasPermission = true;
                    break;
                }
            }

            if (!hasPermission) {
                log.warn("用户{}没有访问机构{}的权限", userId, orgId);
                return false;
            }

            // 获取机构信息
            SysOrg sysOrg = sysOrgService.getById(String.valueOf(orgId));
            if (ObjectUtil.isNull(sysOrg)) {
                log.warn("机构不存在: {}", orgId);
                return false;
            }

            // 正确的机构切换实现：更新Redis缓存中的用户信息
            try {
                // 获取当前登录用户信息
                LoginContext loginContext = LoginContextHolder.me();
                SysLoginUser currentLoginUser = loginContext.getSysLoginUser();

                if (currentLoginUser != null) {
                    // 创建新的登录用户信息，更新机构信息
                    SysLoginUser updatedLoginUser = authService.genSysLoginUser(sysUser);

                    // 手动设置新的机构信息
                    if (updatedLoginUser.getLoginEmpInfo() != null) {
                        updatedLoginUser.getLoginEmpInfo().setOrgId(String.valueOf(orgId));
                        updatedLoginUser.getLoginEmpInfo().setOrgName(sysOrg.getName());

                        // 重新计算数据范围（基于新机构）
                        List<String> dataScopes = sysUserService.getUserDataScopeIdList(userId, String.valueOf(orgId));
                        updatedLoginUser.setDataScopes(dataScopes);
                    }

                    // 获取当前用户的JWT token信息
                    String token = HttpServletUtil.getRequest().getHeader("Authorization");
                    if (StrUtil.isNotBlank(token) && token.startsWith("Bearer ")) {
                        token = token.substring(7);
                        JwtPayLoad jwtPayLoad = JwtTokenUtil.getJwtPayLoad(token);

                        // 更新Redis缓存中的用户信息
                        userCache.put(jwtPayLoad.getUuid(), updatedLoginUser,
                            Convert.toLong(ConstantContextHolder.getSessionTokenExpireSec()));

                        // 更新SpringSecurity上下文
                        authService.setSpringSecurityContextAuthentication(updatedLoginUser);

                        log.info("用户{}成功切换到机构{}，已更新缓存", userId, orgId);
                        return true;
                    } else {
                        log.warn("无法获取用户token信息");
                        return false;
                    }
                } else {
                    log.warn("无法获取当前登录用户信息");
                    return false;
                }
            } catch (Exception e) {
                // 禁止输出堆栈信息，只记录异常类型
                log.error("更新用户会话缓存失败，异常类型: {}", e.getClass().getSimpleName());
                return false;
            }

        } catch (Exception e) {
            // 禁止输出堆栈信息，只记录异常类型
            log.error("切换用户机构失败，用户: {}，机构: {}，异常类型: {}", userId, orgId, e.getClass().getSimpleName());
            throw new RuntimeException("切换用户机构失败");
        }
    }

    /**
     * 获取用户的所有机构信息
     */
    private List<SsoUserInfo.OrgInfo> getUserOrgList(String userId) {
        List<SsoUserInfo.OrgInfo> orgList = new ArrayList<>();
        
        try {
            // 获取用户的主要机构
            SysEmp sysEmp = sysEmpService.getById(userId);
            if (ObjectUtil.isNotNull(sysEmp) && StringUtils.isNotBlank(sysEmp.getOrgId())) {
                SsoUserInfo.OrgInfo orgInfo = new SsoUserInfo.OrgInfo();
                orgInfo.setOrgId(sysEmp.getOrgId());
                orgInfo.setOrgName(sysEmp.getOrgName());
                orgInfo.setIsPrimary(true);
                orgList.add(orgInfo);
            }
            
            // 这里可以扩展获取用户的其他机构信息
            // 例如：附属机构、兼职机构等
            
        } catch (Exception e) {
            // 禁止输出堆栈信息，只记录异常类型
            log.warn("获取用户机构信息失败，用户: {}，异常类型: {}", userId, e.getClass().getSimpleName());
        }
        
        return orgList;
    }

    @Override
    public SsoUserInfo parseSsoToken(String ssoToken) {
        try {
            // 检查是否为URL安全令牌格式
            if (!ssoTokenEncoder.isUrlSafeToken(ssoToken)) {
                throw SsoException.SsoErrorCode.TOKEN_INVALID.toException("令牌格式不正确");
            }

            // 尝试使用不同的密钥解析令牌
            Map<String, Object> ssoData = null;
            String targetSystem = null;

            // 首先尝试使用默认密钥
            try {
                String defaultKey = ssoConfig.getSecretKey(null);
                ssoData = ssoTokenEncoder.parseFullUrlSafeToken(ssoToken, defaultKey);
            } catch (Exception e) {
                // 如果默认密钥失败，尝试其他系统的密钥
                // 这里可以根据实际情况扩展支持的系统列表
                String[] supportedSystems = {"system1", "system2", "system3"};
                for (String system : supportedSystems) {
                    try {
                        String systemKey = ssoConfig.getSecretKey(system);
                        ssoData = ssoTokenEncoder.parseFullUrlSafeToken(ssoToken, systemKey);
                        targetSystem = system;
                        break;
                    } catch (Exception ex) {
                        // 继续尝试下一个密钥
                    }
                }

                if (ssoData == null) {
                    throw SsoException.SsoErrorCode.TOKEN_DECRYPT_FAILED.toException("无法解析SSO令牌，可能密钥不匹配");
                }
            }

            // 验证时间戳
            Long timestamp = (Long) ssoData.get("timestamp");
            if (timestamp == null || !ssoUtil.validateTimestamp(timestamp.toString())) {
                throw SsoException.SsoErrorCode.TIMESTAMP_INVALID.toException("SSO令牌已过期或时间戳无效");
            }

            // 获取用户ID
            String userId = (String) ssoData.get("userId");
            if (StringUtils.isBlank(userId)) {
                throw SsoException.SsoErrorCode.TOKEN_INVALID.toException("令牌中缺少用户ID");
            }

            // 验证用户是否存在且有效
            SysUser sysUser = sysUserService.getById(userId);
            if (ObjectUtil.isNull(sysUser)) {
                throw SsoException.SsoErrorCode.USER_NOT_FOUND.toException("用户不存在");
            }

            // 构建用户信息
            SsoUserInfo userInfo = new SsoUserInfo();
            userInfo.setUserId((String) ssoData.get("userId"));
            userInfo.setAccount((String) ssoData.get("account"));
            userInfo.setUserName((String) ssoData.get("userName"));
            userInfo.setNickName((String) ssoData.get("nickName"));
            userInfo.setOrgId((String) ssoData.get("orgId"));
            userInfo.setOrgName((String) ssoData.get("orgName"));
            // 优先使用令牌中的目标系统，如果没有则使用解析时确定的系统
            String tokenTargetSystem = (String) ssoData.get("targetSystem");
            userInfo.setTargetSystem(tokenTargetSystem != null ? tokenTargetSystem : targetSystem);
            userInfo.setTokenTime(new Date(timestamp));

            // 获取用户的所有机构信息
            List<SsoUserInfo.OrgInfo> orgList = getUserOrgList(userId);
            userInfo.setOrgList(orgList);

            log.info("SSO令牌解析成功，用户: {}, 机构: {}", userId, ssoData.get("orgId"));
            return userInfo;

        } catch (SsoException e) {
            throw e;
        } catch (Exception e) {
            // 禁止输出堆栈信息，只记录异常类型
            log.error("解析SSO令牌失败，异常类型: {}", e.getClass().getSimpleName());
            throw SsoException.SsoErrorCode.PARSE_TOKEN_FAILED.toException("解析SSO令牌失败");
        }
    }

    @Override
    public boolean validateSsoToken(String ssoToken) {
        try {
            parseSsoToken(ssoToken);
            return true;
        } catch (Exception e) {
            // 禁止输出堆栈信息，只记录异常类型
            log.warn("SSO令牌验证失败，异常类型: {}", e.getClass().getSimpleName());
            return false;
        }
    }
}
