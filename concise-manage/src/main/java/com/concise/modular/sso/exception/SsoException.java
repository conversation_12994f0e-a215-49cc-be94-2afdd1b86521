package com.concise.modular.sso.exception;

/**
 * SSO异常类
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
public class SsoException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误详情
     */
    private String errorDetail;

    public SsoException(String message) {
        super(message);
    }

    public SsoException(String message, Throwable cause) {
        super(message, cause);
    }

    public SsoException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public SsoException(String errorCode, String message, String errorDetail) {
        super(message);
        this.errorCode = errorCode;
        this.errorDetail = errorDetail;
    }

    public SsoException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorDetail() {
        return errorDetail;
    }

    public void setErrorDetail(String errorDetail) {
        this.errorDetail = errorDetail;
    }

    /**
     * SSO异常枚举
     */
    public enum SsoErrorCode {
        TOKEN_EXPIRED("SSO_001", "SSO令牌已过期"),
        TOKEN_INVALID("SSO_002", "SSO令牌无效"),
        TOKEN_DECRYPT_FAILED("SSO_003", "SSO令牌解密失败"),
        USER_NOT_FOUND("SSO_004", "用户不存在"),
        SYSTEM_NOT_ENABLED("SSO_005", "目标系统未启用"),
        TIMESTAMP_INVALID("SSO_006", "时间戳无效"),
        IP_NOT_ALLOWED("SSO_007", "IP地址不在白名单中"),
        SYSTEM_NOT_CONFIGURED("SSO_008", "目标系统未配置"),
        GENERATE_TOKEN_FAILED("SSO_009", "生成SSO令牌失败"),
        PARSE_TOKEN_FAILED("SSO_010", "解析SSO令牌失败");

        private final String code;
        private final String message;

        SsoErrorCode(String code, String message) {
            this.code = code;
            this.message = message;
        }

        public String getCode() {
            return code;
        }

        public String getMessage() {
            return message;
        }

        public SsoException toException() {
            return new SsoException(this.code, this.message);
        }

        public SsoException toException(String detail) {
            return new SsoException(this.code, this.message, detail);
        }

        public SsoException toException(Throwable cause) {
            return new SsoException(this.code, this.message, cause);
        }
    }
}
