package com.concise.modular.sso.entity;

import java.io.Serializable;

import lombok.Data;

/**
 * SSO令牌数据传输对象
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Data
public class SsoTokenData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 加密的SSO令牌
     */
    private String ssoToken;

    /**
     * 时间戳
     */
    private Long timestamp;



    /**
     * 目标系统标识
     */
    private String targetSystem;

    /**
     * 令牌有效期（秒）
     */
    private Integer expireSeconds;

    /**
     * 动态密钥标识
     */
    private String keyId;

    /**
     * 完整的跳转URL（包含令牌参数）
     */
    private String redirectUrl;

    public SsoTokenData() {
        this.timestamp = System.currentTimeMillis();
        this.expireSeconds = 300; // 默认5分钟有效期
    }

    public SsoTokenData(String ssoToken) {
        this();
        this.ssoToken = ssoToken;
    }

    public SsoTokenData(String ssoToken, String targetSystem) {
        this(ssoToken);
        this.targetSystem = targetSystem;
    }
}
