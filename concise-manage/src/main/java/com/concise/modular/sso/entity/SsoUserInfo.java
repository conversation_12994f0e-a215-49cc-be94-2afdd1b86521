package com.concise.modular.sso.entity;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * SSO用户信息传输对象
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Data
public class SsoUserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户账号
     */
    private String account;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 主要机构ID
     */
    private String orgId;

    /**
     * 主要机构名称
     */
    private String orgName;

    /**
     * 所属机构列表（用户可能属于多个机构）
     */
    private List<OrgInfo> orgList;

    /**
     * 目标系统标识
     */
    private String targetSystem;

    /**
     * 登录时间
     */
    private Date loginTime;

    /**
     * 令牌生成时间
     */
    private Date tokenTime;

    /**
     * 用户角色列表
     */
    private List<String> roles;

    /**
     * 用户权限列表
     */
    private List<String> permissions;

    /**
     * 扩展属性
     */
    private java.util.Map<String, Object> extendProps;

    /**
     * 机构信息内部类
     */
    @Data
    public static class OrgInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 机构ID
         */
        private String orgId;
        
        /**
         * 机构名称
         */
        private String orgName;
        
        /**
         * 是否主要机构
         */
        private Boolean isPrimary;
        
        /**
         * 职位信息
         */
        private String position;
    }

    public SsoUserInfo() {
        this.loginTime = new Date();
        this.tokenTime = new Date();
    }
}
