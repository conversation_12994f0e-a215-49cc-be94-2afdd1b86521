package com.concise.modular.sso.util;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.concise.modular.sso.config.SsoConfig;
import com.concise.modular.unify.utils.SM4Util;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpUtil;


/**
 * SSO工具类
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Component
public class SsoUtil {

    @Resource
    private SsoConfig ssoConfig;

    /**
     * 生成动态密钥
     *
     * @param userId 用户ID
     * @param timestamp 时间戳
     * @param systemCode 系统代码（可选）
     * @return 动态密钥
     */
    public String generateDynamicKey(String userId, String timestamp, String systemCode) {
        String baseKey = ssoConfig.getSecretKey(systemCode);
        String combinedString = baseKey + userId + timestamp;
        String md5Hash = DigestUtil.md5Hex(combinedString);
        return md5Hash.substring(0, ssoConfig.getKeyLength());
    }

    /**
     * 生成动态密钥（使用默认系统配置）
     *
     * @param userId 用户ID
     * @param timestamp 时间戳
     * @return 动态密钥
     */
    public String generateDynamicKey(String userId, String timestamp) {
        return generateDynamicKey(userId, timestamp, null);
    }

    /**
     * 生成密钥标识
     *
     * @param userId 用户ID
     * @param timestamp 时间戳
     * @return 密钥标识
     */
    public String generateKeyId(String userId, String timestamp) {
        return DigestUtil.md5Hex(userId + timestamp).substring(0, 8);
    }

    /**
     * 生成随机字符串
     *
     * @return 随机字符串
     */
    public String generateNonce() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 验证时间戳是否有效
     *
     * @param timestamp 时间戳字符串
     * @param systemCode 系统代码（可选）
     * @return 是否有效
     */
    public boolean validateTimestamp(String timestamp, String systemCode) {
        if (!ssoConfig.getEnableTimestampValidation()) {
            return true;
        }

        try {
            long tokenTime = Long.parseLong(timestamp);
            long currentTime = System.currentTimeMillis();
            long timeDiff = currentTime - tokenTime;
            
            int expireSeconds = ssoConfig.getTokenExpireSeconds(systemCode);
            return timeDiff >= 0 && timeDiff <= expireSeconds * 1000L;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证时间戳是否有效（使用默认配置）
     *
     * @param timestamp 时间戳字符串
     * @return 是否有效
     */
    public boolean validateTimestamp(String timestamp) {
        return validateTimestamp(timestamp, null);
    }

    /**
     * 验证IP是否在白名单中
     *
     * @param request HTTP请求
     * @return 是否在白名单中
     */
    public boolean validateIpWhitelist(HttpServletRequest request) {
        if (!ssoConfig.getEnableIpWhitelist() || ssoConfig.getIpWhitelist() == null) {
            return true;
        }

        String clientIp = getClientIp(request);
        return ssoConfig.getIpWhitelist().contains(clientIp);
    }

    /**
     * 获取客户端IP地址
     *
     * @param request HTTP请求
     * @return 客户端IP
     */
    public String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 构建SSO跳转URL
     *
     * @param baseUrl 基础URL
     * @param ssoToken SSO令牌
     * @param timestamp 时间戳
     * @param systemCode 系统代码
     * @return 完整的跳转URL
     */
    public String buildSsoUrl(String baseUrl, String ssoToken, String timestamp, String systemCode) {
        try {
            StringBuilder urlBuilder = new StringBuilder(baseUrl);
            String separator = baseUrl.contains("?") ? "&" : "?";

            urlBuilder.append(separator)
                     .append("ssoToken=").append(ssoToken)  // URL安全令牌不需要编码
                     .append("&timestamp=").append(timestamp);

            if (StringUtils.isNotBlank(systemCode)) {
                urlBuilder.append("&targetSystem=").append(URLEncoder.encode(systemCode, StandardCharsets.UTF_8.name()));
            }
            
            return urlBuilder.toString();
        } catch (Exception e) {

            throw new RuntimeException("构建SSO URL失败: " + e.getMessage());
        }
    }

    /**
     * 解析URL中的SSO参数
     *
     * @param request HTTP请求
     * @return SSO参数Map
     */
    public Map<String, String> parseSsoParams(HttpServletRequest request) {
        Map<String, String> params = new HashMap<>();

        String ssoToken = request.getParameter("ssoToken");
        String timestamp = request.getParameter("timestamp");
        String targetSystem = request.getParameter("targetSystem");

        if (StringUtils.isNotBlank(ssoToken)) {
            // URL安全令牌不需要解码
            params.put("ssoToken", ssoToken);
        }

        if (StringUtils.isNotBlank(timestamp)) {
            params.put("timestamp", timestamp);
        }

        if (StringUtils.isNotBlank(targetSystem)) {
            try {
                params.put("targetSystem", URLDecoder.decode(targetSystem, StandardCharsets.UTF_8.name()));
            } catch (Exception e) {
                params.put("targetSystem", targetSystem);
            }
        }

        return params;
    }

    /**
     * 加密数据
     *
     * @param data 原始数据
     * @param key 密钥
     * @return 加密后的数据
     */
    public String encryptData(String data, String key) {
        return SM4Util.encryptDatas(data, key);
    }

    /**
     * 解密数据
     *
     * @param encryptedData 加密数据
     * @param key 密钥
     * @return 解密后的数据
     */
    public String decryptData(String encryptedData, String key) {
        return SM4Util.decryptDatas(encryptedData, key);
    }

    /**
     * 发送HTTP POST请求到目标系统
     *
     * @param url 目标URL
     * @param data 请求数据
     * @return 响应结果
     */
    public String sendHttpPost(String url, Map<String, Object> data) {
        try {
            String jsonData = JSONObject.toJSONString(data);
            return HttpUtil.post(url, jsonData);
        } catch (Exception e) {

            throw new RuntimeException("发送HTTP POST请求失败: " + e.getMessage());
        }
    }

    /**
     * 验证系统是否启用
     *
     * @param systemCode 系统代码
     * @return 是否启用
     */
    public boolean isSystemEnabled(String systemCode) {
        return ssoConfig.isSystemEnabled(systemCode);
    }

    /**
     * 获取系统配置
     *
     * @param systemCode 系统代码
     * @return 系统配置
     */
    public SsoConfig.SystemConfig getSystemConfig(String systemCode) {
        return ssoConfig.getSystemConfig(systemCode);
    }
}
