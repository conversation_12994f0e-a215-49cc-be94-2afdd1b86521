package com.concise.modular.correctionobjectbasic.service;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.modular.correctionobjectbasic.entity.CorrectionObjectBasic;
import com.concise.modular.correctionobjectbasic.mapper.CorrectionObjectBasicMapper;
import lombok.extern.slf4j.Slf4j;


import org.springframework.stereotype.Service;



/**
 * @Description: 社区矫正对象信息-基本信息
 * @Author: LiuQC
 * @Date: 2021-04-21
 * @Version: V1.0
 */
@Service
@Slf4j
@DS(value = "jyfx")
public class CorrectionObjectBasicServiceImpl extends ServiceImpl<CorrectionObjectBasicMapper, CorrectionObjectBasic> implements ICorrectionObjectBasicService {


}
