package com.concise.modular.correctionobjectbasic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.modular.correctionobjectbasic.entity.CorrectionObjectBasic;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 社区矫正对象信息-基本信息
 * @Author: LiuQC
 * @Date:   2021-04-21
 * @Version: V1.0
 */
public interface CorrectionObjectBasicMapper extends BaseMapper<CorrectionObjectBasic> {

    int count();

    /**
     * @param status
     * @param delFlag
     * @return
     */
    List<CorrectionObjectBasic> getAllByZhuangtaiAndDelFlag(@Param("status") String status, @Param("delFlag") int delFlag);

    /**
     * @param sfzh
     * @return
     */
    List<String> selectIdListBySfzh(@Param("sfzh") String sfzh);

    List<CorrectionObjectBasic> bindPage(CorrectionObjectBasic correctionObjectBasic);
}
