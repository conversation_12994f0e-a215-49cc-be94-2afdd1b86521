package com.concise.modular.correctionobjectbasic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 社区矫正对象信息-基本信息
 * @Author: LiuQC
 * @Date: 2021-04-21
 * @Version: V1.0
 */
@Data
@TableName("correction_object_basic")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "correction_object_basic对象", description = "社区矫正对象信息-基本信息")
public class CorrectionObjectBasic {

    /** ID */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "ID")
    private String id;
    /** 社区矫正人员编号 */
    @ApiModelProperty(value = "社区矫正人员编号")
    private String sqjzrybh;
    /** 矫正机构 */
    @ApiModelProperty(value = "矫正机构")
    private String jzjg;
    /** 矫正机构中文值 */
    @ApiModelProperty(value = "矫正机构中文值")
    private String jzjgName;
    /** 是否调查评估 */
    @ApiModelProperty(value = "是否调查评估")
    private String sfdcpg;
    /** 矫正类别 */
    @ApiModelProperty(value = "矫正类别(1:管制, 2:缓刑, 3:假释, 4:暂予监外执, 5:其他)")
    private String jzlb;
    /** 矫正类别中文值 */
    @ApiModelProperty(value = "矫正类别中文值")
    private String jzlbName;
    /** 姓名 */
    @ApiModelProperty(value = "姓名")
    private String xm;
    /** 曾用名 */
    @ApiModelProperty(value = "曾用名")
    private String cym;
    /** 性别 */
    @ApiModelProperty(value = "性别")
    private String xb;
    /** 性别中文值 */
    @ApiModelProperty(value = "性别中文值")
    private String xbName;
    /** 民族 */
    @ApiModelProperty(value = "民族")
    private String mz;
    /** 民族中文值 */
    @ApiModelProperty(value = "民族中文值")
    private String mzName;
    /** 身份证号 */
    @ApiModelProperty(value = "身份证号")
    private String sfzh;
    /** 出生日期 */
    @ApiModelProperty(value = "出生日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date csrq;
    /** 个人联系电话 */
    @ApiModelProperty(value = "个人联系电话")
    private String grlxdh;
    /** 定位号码 */
    @ApiModelProperty(value = "定位号码")
    private String dwhm;
    /** 社区矫正开始日期 */
    @ApiModelProperty(value = "社区矫正开始日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date sqjzksrq;

    /** 入矫时间 */
    @ApiModelProperty(value = "入矫时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date rujiaoriqi;

    /** 社区矫正结束日期 */
    @ApiModelProperty(value = "社区矫正结束日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date sqjzjsrq;
    /** 社区矫正期限 */
    @ApiModelProperty(value = "社区矫正期限")
    private String sqjzqx;
    /** 是否采用电子定位管理(1实施定位、2免除定位、3解除定位） */
    @ApiModelProperty(value = "是否采用电子定位管理(1:实施定位、2:免除定位、3:解除定位）")
    private String sfcydzdwgl;
    /** 电子定位方式 */
    @ApiModelProperty(value = "电子定位方式")
    private String dzdwfs;
    /** 电子定位方式中文值 */
    @ApiModelProperty(value = "电子定位方式中文值")
    private String dzdwfsName;
    /** 矫正状态(01:期满解矫,02:收监执行,200在册,03:死亡,04:居住地变更,5:其他(余罪),6:重新犯罪,7:矫正终止,8:特赦解矫,99:其他) */
    @ApiModelProperty(value = "矫正状态(01:期满解矫,02:收监执行,200在册,03:死亡,04:居住地变更,5:其他(余罪),6:重新犯罪,7:矫正终止,8:特赦解矫,99:其他)")
    private String zhuangtai;
    /** 解矫类型 */
    @ApiModelProperty(value = "解矫类型")
    private String relieveType;
    /** 运营商类型（1：移动，2：联通，3：电信） */
    @ApiModelProperty(value = "运营商类型（1：移动，2：联通，3：电信）")
    private Integer operatorType;
    /** 是否开通基站电子定位管理(0:否,1:是,2:退订,3:开通失败) */
    @ApiModelProperty(value = "是否开通基站电子定位管理(0:否,1:是,2:退订,3:开通失败)")
    private Integer isElectronicPositioning;
    /** 是否开通基站电子定位管理(0:否,1:是,2:退订,3:开通失败) */
    @ApiModelProperty(value = "是否开通基站电子定位管理(0:否,1:是,2:退订,3:开通失败)")
    private String electronicPositioning;
    /** 手环设备编号 */
    @ApiModelProperty(value = "手环设备编号")
    private String equipmentNumber;
    /** 是否采用腕带定位管理(0:否,1:是) */
    @ApiModelProperty(value = "是否采用腕带定位管理(0:否,1:是)")
    private Integer isEwPositioning;
    /** 是否重点人员(0:否,1:是) */
    @ApiModelProperty(value = "是否重点人员(0:否,1:是)")
    private Integer isKeyPersonnel;
    /** 处理等级(字典值：0:普管,1:严管,2:普管重点,3:严管重点) */
    @ApiModelProperty(value = "处理等级(字典值：0:普管,1:严管,2:普管重点,3:严管重点)")
    private String jzjb;
    /** 处理等级(字典值：0:普管,1:严管,2:普管重点,3:严管重点) */
    @ApiModelProperty(value = "处理等级(字典值：1:严管,2:普管)")
    private String jzjbName;
    /** 请假标识（是_1,否_0） */
    @ApiModelProperty(value = "请假标识（是_1,否_0）")
    private Integer isLeave;
    /** 版本 */
    @ApiModelProperty(value = "版本")
    private Integer version;
    /** createBy */
    @ApiModelProperty(value = "createBy")
    private String createBy;
    /** 创建日期 */
    @ApiModelProperty(value = "创建日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /** updateBy */
    @ApiModelProperty(value = "updateBy")
    private String updateBy;
    /** 更新日期 */
    @ApiModelProperty(value = "更新日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /** 是否删除（0：未删除，1删除） */
    @ApiModelProperty(value = "是否删除（0：未删除，1删除）")
    private Integer delFlag;

    /**
     * 免除学习时间
     */
    @ApiModelProperty(value = "免除学习开始时间 ")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date exemptionOfStudyStartTime;
    /**
     * 免除学习时间
     */
    @ApiModelProperty(value = "免除学习结束时间 ")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date exemptionOfStudyEndTime;
    /**
     * 免除学习原因
     */
    @ApiModelProperty(value = "免除学习原因 ")
    private String reason;

    /**
     * 免除学习原因
     */
    @ApiModelProperty(value = "免除事项 ")
    private String exemptions;

    /**
     * 免除公益活动开始时间
     */
    @ApiModelProperty(value = "免除公益活动开始时间 ")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date exemptionActivityStartTime;
    /**
     * 免除公益活动结束时间
     */
    @ApiModelProperty(value = "免除公益活动结束时间 ")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date exemptionActivityEndTime;

    /**
     * 免除公益活动原因
     */
    private String activityReason;




    @ApiModelProperty(value = "矫正阶段")
    @TableField(exist = false)
    private String correctionStage;


    @ApiModelProperty(value = "标签ID 例 [1,2,3]")
    private String labelItemIdList;
    @ApiModelProperty(value = "标签名称 例 ['初中','高中','离异']")
    private String labelItemNameList;
    /**文化程度(字典值：文盲、小学、初中、高中、大专、本科、硕士、博士及以上（包含博士）、中专和中技、其他)*/
    @ApiModelProperty(value = "文化程度(字典值：文盲、小学、初中、高中、大专、本科、硕士、博士及以上（包含博士）、中专和中技、其他)")
    private String whcd;
    /**文化程度(字典值：文盲、小学、初中、高中、大专、本科、硕士、博士及以上（包含博士）、中专和中技、其他)*/
    @ApiModelProperty(value = "文化程度(字典值：文盲、小学、初中、高中、大专、本科、硕士、博士及以上（包含博士）、中专和中技、其他)")
    private String whcdName;

    @ApiModelProperty(value = "是否重点人员(0否 1是)")
    private Integer isZdry;

    @ApiModelProperty(value = "当前就业情况")
    private String  dqjyqk;
    @ApiModelProperty(value = "所属行业")
    private String  sshy;


}
