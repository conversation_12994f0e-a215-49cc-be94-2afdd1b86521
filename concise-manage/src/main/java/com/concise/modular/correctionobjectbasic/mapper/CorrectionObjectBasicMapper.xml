<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.modular.correctionobjectbasic.mapper.CorrectionObjectBasicMapper">

    <select id="count" resultType="java.lang.Integer">
        select count(1)
        from ryxx
        where ZHUANGTAI in (200)
          and IS_REMOVED = 0
    </select>
    <select id="getAllByZhuangtaiAndDelFlag" resultType="com.concise.modular.correctionobjectbasic.entity.CorrectionObjectBasic">
        SELECT *
        FROM correction_object_basic
        WHERE zhuangtai = #{status}
          AND del_flag = #{delFlag}
    </select>
    <select id="selectIdListBySfzh" resultType="java.lang.String">
        SELECT id
        FROM correction_object_basic
        WHERE sfzh = #{sfzh}
    </select>
    <select id="bindPage" resultType="com.concise.modular.correctionobjectbasic.entity.CorrectionObjectBasic">
        SELECT lt.id,lt.jzjg_name,lt.xm,lt.dqjyqk,rt.jzry_id,rt.bind_jzry_id,rt.uid FROM correction_object_basic lt LEFT JOIN (
        SELECT a.id,jzry_id,bind_jzry_id,b.id uid
        FROM correction_object_basic a left join js_assistance_teamwork b on a.id = b.bind_jzry_id WHERE
        b.jzry_id=#{id}
        ) rt on lt.id = rt.id  where lt.dqjyqk='就业' or lt.dqjyqk='创业'
    </select>
</mapper>
