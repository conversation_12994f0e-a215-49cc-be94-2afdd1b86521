package com.concise.modular.utils;

import javax.crypto.Cipher;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

public class AESUtil {

    private static final String IV_STRING="citydolinping@jz";

    private static final String KEY="citydolinping@12";

    /**
     * AES加密
     */
    public static String encryptAES(String content) throws Exception{
        try{
            byte[] byteContent=content.getBytes("UTF-8");
            byte[] enCodeFormat=KEY.getBytes();
            SecretKeySpec secretKeySpec=new SecretKeySpec(enCodeFormat,"AES");
            byte[] initParam=IV_STRING.getBytes();
            IvParameterSpec ivParameterSpec=new IvParameterSpec(initParam);
            Cipher cipher= Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE,secretKeySpec,ivParameterSpec);
            byte[] encryptedBytes=cipher.doFinal(byteContent);
            Base64.Encoder encoder=Base64.getEncoder();
            return encoder.encodeToString(encryptedBytes);
        }catch(Exception e){
            throw new Exception("加密出错");
        }
    }

    /**
     * AES解密
     */
    public static String decryptAES(String content) throws Exception {
        try {
            Base64.Decoder decoder = Base64.getDecoder();
            byte[] encryptedBytes = decoder.decode(content);
            byte[] enCodeFormat = KEY.getBytes();
            SecretKeySpec secretKey = new SecretKeySpec(enCodeFormat, "AES");
            byte[] initParam = IV_STRING.getBytes();
            IvParameterSpec ivParameterSpec = new IvParameterSpec(initParam);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParameterSpec);
            byte[] result = cipher.doFinal(encryptedBytes);
            return new String(result, "UTF-8");
        } catch (Exception e) {
            throw new Exception("解密出错");
        }
    }

}
