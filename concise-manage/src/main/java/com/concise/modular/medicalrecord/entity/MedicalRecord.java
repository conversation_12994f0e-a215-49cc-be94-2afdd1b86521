package com.concise.modular.medicalrecord.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.concise.common.pojo.base.entity.BaseEntity;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 就诊记录表
 *
 * <AUTHOR>
 * @date 2023-05-16 11:40:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("medical_record")
public class MedicalRecord extends BaseEntity {

    /**
     * 序号
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 就诊日期
     */
    @Excel(name = "就诊日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
        private Date jzrq;
    
    /**
     * 科室名称
     */
        private String ksmc;
    
    /**
     * 身份证号
     */
        private String sfzh;
    
    /**
     * 姓名
     */
        private String xm;
    
    /**
     * 医疗机构名称
     */
        private String yljgmc;
    
    /**
     * 矫正对象id
     */
        private String jzdxId;
    
    /**
     * 矫正机构id
     */
        private String jzjg;
    
    /**
     * 矫正机构
     */
        private String jzjgName;
    
}
