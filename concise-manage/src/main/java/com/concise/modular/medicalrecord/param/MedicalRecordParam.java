package com.concise.modular.medicalrecord.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 就诊记录表参数类
 *
 * <AUTHOR>
 * @date 2023-05-16 11:40:13
*/
@Data
public class MedicalRecordParam extends BaseParam {

    /**
     * 序号
     */
    @NotNull(message = "序号不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 就诊日期
     */
    @NotNull(message = "就诊日期不能为空，请检查jzrq参数", groups = {add.class, edit.class})
    private String jzrq;

    /**
     * 科室名称
     */
    @NotBlank(message = "科室名称不能为空，请检查ksmc参数", groups = {add.class, edit.class})
        private String ksmc;
    
    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空，请检查sfzh参数", groups = {add.class, edit.class})
        private String sfzh;
    
    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
        private String xm;
    
    /**
     * 医疗机构名称
     */
    @NotBlank(message = "医疗机构名称不能为空，请检查yljgmc参数", groups = {add.class, edit.class})
        private String yljgmc;
    
    /**
     * 矫正对象id
     */
    @NotBlank(message = "矫正对象id不能为空，请检查jzdxId参数", groups = {add.class, edit.class})
        private String jzdxId;
    
    /**
     * 矫正机构id
     */
    @NotBlank(message = "矫正机构id不能为空，请检查jzjg参数", groups = {add.class, edit.class})
        private String jzjg;
    
    /**
     * 矫正机构
     */
    @NotBlank(message = "矫正机构不能为空，请检查jzjgName参数", groups = {add.class, edit.class})
        private String jzjgName;
    
}
