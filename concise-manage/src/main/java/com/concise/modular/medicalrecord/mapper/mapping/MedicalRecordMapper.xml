<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.modular.medicalrecord.mapper.MedicalRecordMapper">

    <select id="getRecordList" resultType="com.concise.modular.medicalrecord.entity.MedicalRecord">
        SELECT
        medical_record.*
        FROM
        medical_record
        INNER JOIN (
            SELECT jzdx_id, MAX(jzrq) as max_jzrq
            FROM medical_record
            GROUP BY jzdx_id
        ) t1 ON medical_record.jzdx_id = t1.jzdx_id AND medical_record.jzrq = t1.max_jzrq
        WHERE jzjg in
        <foreach collection="list" index="index" item="i" open="(" separator="," close=")">
            #{i}
        </foreach>
        ORDER BY jzrq desc
    </select>
</mapper>
