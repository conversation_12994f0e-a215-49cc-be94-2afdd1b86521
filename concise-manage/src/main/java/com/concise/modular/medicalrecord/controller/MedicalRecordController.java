package com.concise.modular.medicalrecord.controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.modular.medicalrecord.param.MedicalRecordParam;
import com.concise.modular.medicalrecord.service.MedicalRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 就诊记录表控制器
 *
 * <AUTHOR>
 * @date 2023-05-16 11:40:13
 */
@Api(tags = "就诊记录表")
@RestController
public class MedicalRecordController {

    @Resource
    private MedicalRecordService medicalRecordService;

    /**
     * 查询就诊记录表
     *
     * <AUTHOR>
     * @date 2023-05-16 11:40:13
     */
    @Permission
    @GetMapping("/medicalRecord/page")
    @ApiOperation("就诊记录表_分页查询")
    @BusinessLog(title = "就诊记录表_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(MedicalRecordParam medicalRecordParam) {
        return new SuccessResponseData(medicalRecordService.page(medicalRecordParam));
    }

    @GetMapping("/medicalRecord/updateInfo")
    @ApiOperation("就诊记录表_更新数据")
    @BusinessLog(title = "就诊记录表_更新数据", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData updateInfo() throws Exception {
        medicalRecordService.synchronizeMedicalData();
        return new SuccessResponseData();
    }

    /**
     * 添加就诊记录表
     *
     * <AUTHOR>
     * @date 2023-05-16 11:40:13
     */
    @Permission
    @PostMapping("/medicalRecord/add")
    @ApiOperation("就诊记录表_增加")
    @BusinessLog(title = "就诊记录表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(MedicalRecordParam.add.class) MedicalRecordParam medicalRecordParam) {
        medicalRecordService.add(medicalRecordParam);
        return new SuccessResponseData();
    }

    /**
     * 删除就诊记录表
     *
     * <AUTHOR>
     * @date 2023-05-16 11:40:13
     */
    @Permission
    @PostMapping("/medicalRecord/delete")
    @ApiOperation("就诊记录表_删除")
    @BusinessLog(title = "就诊记录表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(MedicalRecordParam.delete.class) MedicalRecordParam medicalRecordParam) {
        medicalRecordService.delete(medicalRecordParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑就诊记录表
     *
     * <AUTHOR>
     * @date 2023-05-16 11:40:13
     */
    @Permission
    @PostMapping("/medicalRecord/edit")
    @ApiOperation("就诊记录表_编辑")
    @BusinessLog(title = "就诊记录表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(MedicalRecordParam.edit.class) MedicalRecordParam medicalRecordParam) {
        medicalRecordService.edit(medicalRecordParam);
        return new SuccessResponseData();
    }

    /**
     * 查看就诊记录表
     *
     * <AUTHOR>
     * @date 2023-05-16 11:40:13
     */
    @Permission
    @GetMapping("/medicalRecord/detail")
    @ApiOperation("就诊记录表_查看")
    @BusinessLog(title = "就诊记录表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(MedicalRecordParam.detail.class) MedicalRecordParam medicalRecordParam) {
        return new SuccessResponseData(medicalRecordService.detail(medicalRecordParam));
    }

    /**
     * 就诊记录表列表
     *
     * <AUTHOR>
     * @date 2023-05-16 11:40:13
     */
    @Permission
    @GetMapping("/medicalRecord/list")
    @ApiOperation("就诊记录表_列表")
    @BusinessLog(title = "就诊记录表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(MedicalRecordParam medicalRecordParam) {
        return new SuccessResponseData(medicalRecordService.list(medicalRecordParam));
    }

}
