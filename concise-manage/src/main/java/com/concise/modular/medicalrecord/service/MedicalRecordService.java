package com.concise.modular.medicalrecord.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.medicalrecord.entity.MedicalRecord;
import com.concise.modular.medicalrecord.param.MedicalRecordParam;
import java.util.List;

/**
 * 就诊记录表service接口
 *
 * <AUTHOR>
 * @date 2023-05-16 11:40:13
 */
public interface MedicalRecordService extends IService<MedicalRecord> {

    /**
     * 查询就诊记录表
     *
     * <AUTHOR>
     * @date 2023-05-16 11:40:13
     */
    PageResult<MedicalRecord> page(MedicalRecordParam medicalRecordParam);

    /**
     * 就诊记录表列表
     *
     * <AUTHOR>
     * @date 2023-05-16 11:40:13
     */
    List<MedicalRecord> list(MedicalRecordParam medicalRecordParam);

    /**
     * 添加就诊记录表
     *
     * <AUTHOR>
     * @date 2023-05-16 11:40:13
     */
    void add(MedicalRecordParam medicalRecordParam);

    /**
     * 删除就诊记录表
     *
     * <AUTHOR>
     * @date 2023-05-16 11:40:13
     */
    void delete(MedicalRecordParam medicalRecordParam);

    /**
     * 编辑就诊记录表
     *
     * <AUTHOR>
     * @date 2023-05-16 11:40:13
     */
    void edit(MedicalRecordParam medicalRecordParam);

    /**
     * 查看就诊记录表
     *
     * <AUTHOR>
     * @date 2023-05-16 11:40:13
     */
     MedicalRecord detail(MedicalRecordParam medicalRecordParam);

    /**
     * 同步就诊信息
     */
    void synchronizeMedicalData() throws Exception;

    /**
     * 统计最新一次的就诊数据
     * @param collect 机构id
     * @return
     */
    List<MedicalRecord> getRecordList(List<String> collect);
}
