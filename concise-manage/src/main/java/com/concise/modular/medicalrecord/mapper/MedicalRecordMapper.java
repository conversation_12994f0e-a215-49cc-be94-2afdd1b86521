package com.concise.modular.medicalrecord.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.modular.medicalrecord.entity.MedicalRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 就诊记录表
 *
 * <AUTHOR>
 * @date 2023-05-16 11:40:13
 */
public interface MedicalRecordMapper extends BaseMapper<MedicalRecord> {
    List<MedicalRecord> getRecordList(@Param("list") List<String> collect);
}
