package com.concise.modular.medicalrecord.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.modular.correctionobjectinformation.service.CorrectionObjectInformationService;
import com.concise.modular.medicalrecord.entity.MedicalRecord;
import com.concise.modular.medicalrecord.enums.MedicalRecordExceptionEnum;
import com.concise.modular.medicalrecord.mapper.MedicalRecordMapper;
import com.concise.modular.medicalrecord.param.MedicalRecordParam;
import com.concise.modular.medicalrecord.service.MedicalRecordService;
import com.concise.modular.utils.AESUtil;
import com.concise.sys.modular.org.entity.SysOrg;
import com.concise.sys.modular.org.service.SysOrgService;
import com.mysql.cj.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 就诊记录表service接口实现类
 *
 * <AUTHOR>
 * @date 2023-05-16 11:40:13
 */
@Slf4j
@Service
public class MedicalRecordServiceImpl extends ServiceImpl<MedicalRecordMapper, MedicalRecord> implements MedicalRecordService {
    @Resource
    private CorrectionObjectInformationService correctionObjectInformationService;
    @Resource
    private SysOrgService sysOrgService;

    @Override
    public PageResult<MedicalRecord> page(MedicalRecordParam medicalRecordParam) {
        QueryWrapper<MedicalRecord> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(medicalRecordParam)) {

            // 根据就诊日期 查询
            if (ObjectUtil.isNotEmpty(medicalRecordParam.getJzrq())) {
                queryWrapper.lambda().eq(MedicalRecord::getJzrq, medicalRecordParam.getJzrq());
            }
            // 根据科室名称 查询
            if (ObjectUtil.isNotEmpty(medicalRecordParam.getKsmc())) {
                queryWrapper.lambda().eq(MedicalRecord::getKsmc, medicalRecordParam.getKsmc());
            }
            // 根据身份证号 查询
            if (ObjectUtil.isNotEmpty(medicalRecordParam.getSfzh())) {
                queryWrapper.lambda().eq(MedicalRecord::getSfzh, medicalRecordParam.getSfzh());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(medicalRecordParam.getXm())) {
                queryWrapper.lambda().eq(MedicalRecord::getXm, medicalRecordParam.getXm());
            }
            // 根据医疗机构名称 查询
            if (ObjectUtil.isNotEmpty(medicalRecordParam.getYljgmc())) {
                queryWrapper.lambda().eq(MedicalRecord::getYljgmc, medicalRecordParam.getYljgmc());
            }
            // 根据矫正对象id 查询
            if (ObjectUtil.isNotEmpty(medicalRecordParam.getJzdxId())) {
                queryWrapper.lambda().eq(MedicalRecord::getJzdxId, medicalRecordParam.getJzdxId());
            }
            // 根据矫正机构id 查询
            if (ObjectUtil.isNotEmpty(medicalRecordParam.getJzjg())) {
                queryWrapper.lambda().eq(MedicalRecord::getJzjg, medicalRecordParam.getJzjg());
            }
            // 根据矫正机构 查询
            if (ObjectUtil.isNotEmpty(medicalRecordParam.getJzjgName())) {
                queryWrapper.lambda().eq(MedicalRecord::getJzjgName, medicalRecordParam.getJzjgName());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<MedicalRecord> list(MedicalRecordParam medicalRecordParam) {
        return this.list();
    }

    @Override
    public void add(MedicalRecordParam medicalRecordParam) {
        MedicalRecord medicalRecord = new MedicalRecord();
        BeanUtil.copyProperties(medicalRecordParam, medicalRecord);
        this.save(medicalRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(MedicalRecordParam medicalRecordParam) {
        this.removeById(medicalRecordParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(MedicalRecordParam medicalRecordParam) {
        MedicalRecord medicalRecord = this.queryMedicalRecord(medicalRecordParam);
        BeanUtil.copyProperties(medicalRecordParam, medicalRecord);
        this.updateById(medicalRecord);
    }

    @Override
    public MedicalRecord detail(MedicalRecordParam medicalRecordParam) {
        return this.queryMedicalRecord(medicalRecordParam);
    }

    /**
     * 获取就诊记录表
     *
     * <AUTHOR>
     * @date 2023-05-16 11:40:13
     */
    private MedicalRecord queryMedicalRecord(MedicalRecordParam medicalRecordParam) {
        MedicalRecord medicalRecord = this.getById(medicalRecordParam.getId());
        if (ObjectUtil.isNull(medicalRecord)) {
            throw new ServiceException(MedicalRecordExceptionEnum.NOT_EXIST);
        }
        return medicalRecord;
    }

    @Override
    public void synchronizeMedicalData() throws Exception {
        List<String> collect = sysOrgService.list().stream().map(SysOrg::getId).collect(Collectors.toList());
        List<CorrectionObjectInformation> correctionObjectInformationList = correctionObjectInformationService.list(new LambdaQueryWrapper<CorrectionObjectInformation>().eq(CorrectionObjectInformation::getZhuangtai, "200").in(CorrectionObjectInformation::getJzjg, collect));
        for (CorrectionObjectInformation correctionObjectInformation : correctionObjectInformationList) {
            String encode = URLEncoder.encode(AESUtil.encryptAES(correctionObjectInformation.getSfzh()), "UTF-8");
            HttpRequest httpRequest = new HttpRequest("https://ggsjpt.lpxxfw.cn:17777/api/DataService/product/getApiData?pageNum=1&pageSize=1000&secretkey=e9494baa89b03a4e&sfzh=" + encode);
            httpRequest.method(Method.POST);
            String body = httpRequest.execute().body();
            log.info(correctionObjectInformation.getSfzh() + "," + correctionObjectInformation.getXm() + "," + body);
            JSONObject object = JSONObject.parseObject(body);
            if (object.getInteger("code") == 200) {
                String data = object.getString("data");
                JSONObject detailObject = JSONObject.parseObject(data);
                if (detailObject.getInteger("code") == 200) {
                    String objectString = detailObject.getString("data");
                    List<MedicalRecord> medicalRecords = JSONObject.parseArray(objectString, MedicalRecord.class);
                    if (CollectionUtil.isNotEmpty(medicalRecords)) {
                        for (MedicalRecord medicalRecord : medicalRecords) {
                            medicalRecord.setJzdxId(correctionObjectInformation.getId());
                            medicalRecord.setJzjg(correctionObjectInformation.getJzjg());
                            medicalRecord.setJzjgName(correctionObjectInformation.getJzjgName());
                        }
                        this.saveOrUpdateBatch(medicalRecords);
                        log.info("保存成功，" + correctionObjectInformation.getXm());
                    }
                }
            }

        }
    }

    @Scheduled(cron = "0 48 17 * * ? ")
    public void syncData() throws Exception {
        log.info("开始同步就医数据");
        this.synchronizeMedicalData();
        log.info("就医数据同步结束");
    }

    @Override
    public List<MedicalRecord> getRecordList(List<String> collect) {
        return this.baseMapper.getRecordList(collect);
    }

    public static void main(String[] args) throws Exception {
        String encode = URLEncoder.encode(AESUtil.encryptAES("362322199202133928"), "UTF-8");
        System.out.println("https://ggsjpt.lpxxfw.cn:17777/api/DataService/product/getApiData?pageNum=1&pageSize=1000&secretkey=e9494baa89b03a4e&sfzh=" + encode);
    }

}
