package com.concise.modular.rcauth.exception;

/**
 * 认证中心异常类
 * 
 * <AUTHOR>
 * @date 2024-06-20
 */
public class AuthCenterException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    private String errorCode;
    
    public AuthCenterException(String message) {
        super(message);
    }
    
    public AuthCenterException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public AuthCenterException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public AuthCenterException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
}
