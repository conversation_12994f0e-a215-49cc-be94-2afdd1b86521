package com.concise.modular.rcauth;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

@Data
@Component
@ConfigurationProperties(prefix = "auth.center")
public class AuthCenterConfig {
    private String clientId;
    private String clientSecret;
    private String authorizeUrl;
    private String codeUrl;
    private String tokenUrl;
    private String revokeUrl;
    private String logoutUrl;
    private String redirectUri;
    private String scope = "AUTH";
}
