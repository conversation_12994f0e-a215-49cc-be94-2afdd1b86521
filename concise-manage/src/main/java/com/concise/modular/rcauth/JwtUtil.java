package com.concise.modular.rcauth;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.JWTParser;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class JwtUtil {

    /**
     * JWTtoken解密
     */
    public JWTClaimsSet getJwtClaimsSet(String token) {
        if (token == null || token.trim().isEmpty()) {
            log.warn("JWT token为空");
            return null;
        }

        try {
            return JWTParser.parse(token).getJWTClaimsSet();
        } catch (java.text.ParseException e) {
            log.error("JWT token解析失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * JWTtoken验证超时
     */
    public boolean isNotExpiration(JWTClaimsSet jwtClaimsSet) {
        if (jwtClaimsSet == null) {
            log.warn("JWT ClaimsSet为空，无法验证过期时间");
            return false;
        }

        Date expirationTime = jwtClaimsSet.getExpirationTime();
        if (expirationTime == null) {
            log.warn("JWT token中没有过期时间信息");
            return false;
        }

        boolean notExpired = !expirationTime.before(new Date());
        if (!notExpired) {
            log.debug("JWT token已过期，过期时间: {}", expirationTime);
        }
        return notExpired;
    }

    /**
     * 从token中获取用户信息
     */
    public Map<String, Object> getUserInfo(String token) {
        JWTClaimsSet claimsSet = getJwtClaimsSet(token);
        if (claimsSet == null) {
            return null;
        }

        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("userId", claimsSet.getSubject());
        userInfo.put("userName", claimsSet.getClaim("operatorName"));
        userInfo.put("userCode", claimsSet.getClaim("operatorCode"));
        userInfo.put("orgName", claimsSet.getClaim("orgName"));
        userInfo.put("orgUuid", claimsSet.getClaim("orgUuid"));
        userInfo.put("sessionId", claimsSet.getClaim("sessionId"));
        log.debug(">>> userInfo: {}", userInfo);
        return userInfo;
    }
}
