package com.concise.modular.rcauth;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.modular.rcauth.enums.SsoResponseCodeEnum;
import com.concise.sys.modular.auth.service.AuthService;
import com.concise.sys.modular.user.entity.SysUser;
import com.concise.sys.modular.user.service.SysUserService;
import com.nimbusds.jwt.JWTClaimsSet;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;


@Service
@Slf4j
public class AuthCenterService {

    @Autowired
    private AuthCenterConfig config;



    @Resource
    private SysUserService sysUserService;

    @Resource
    private AuthService authService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 获取认证中心授权页面URL
     */
    public String getAuthorizeUrl(String state) {
        return UriComponentsBuilder.fromHttpUrl(config.getAuthorizeUrl())
                .queryParam("client_id", config.getClientId())
                .queryParam("response_type", "code")
                .queryParam("scope", config.getScope())
                .queryParam("redirect_uri", config.getRedirectUri())
                .queryParam("state", state)
                .build()
                .toString();
    }

    /**
     * 使用临时令牌获取正式令牌
     */
    public Map<String, Object> getTokenByCode(String code) {
        // 构建Basic认证头
        String auth = config.getClientId() + ":" + config.getClientSecret();
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));

        Map<String, Object> params = new HashMap<>();
        params.put("code", code);
        params.put("grant_type", "authorization_code");
        params.put("redirect_uri", config.getRedirectUri());
        log.info(">>> rc获取token请求参数: {}", params);

        try {
            HttpResponse response = HttpRequest.post(config.getCodeUrl())
                    .header("Authorization", "Basic " + encodedAuth)
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .form(params)
                    .execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info(">>> rc获取token成功: {}", responseBody);
                @SuppressWarnings("unchecked")
                Map<String, Object> result = JSONUtil.toBean(responseBody, Map.class);
                return result;
            } else {
                log.error("获取token失败: HTTP状态码={}, 响应内容={}", response.getStatus(), response.body());
                return null;
            }
        } catch (Exception e) {
            log.error("获取token异常, code: {}", code, e);
            return null;
        }
    }

    /**
     * 刷新token
     */
    public Map<String, Object> refreshToken(String refreshToken) {
        // 构建Basic认证头
        String auth = config.getClientId() + ":" + config.getClientSecret();
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));

        Map<String, Object> params = new HashMap<>();
        params.put("grant_type", "refresh_token");
        params.put("refresh_token", refreshToken);
        log.info(">>> rc刷新token请求参数: {}", params);

        try {
            HttpResponse response = HttpRequest.post(config.getTokenUrl())
                    .header("Authorization", "Basic " + encodedAuth)
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .form(params)
                    .execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info(">>> rc刷新token成功: {}", responseBody);
                @SuppressWarnings("unchecked")
                Map<String, Object> result = JSONUtil.toBean(responseBody, Map.class);
                return result;
            } else {
                log.error("刷新token失败: HTTP状态码={}, 响应内容={}", response.getStatus(), response.body());
                return null;
            }
        } catch (Exception e) {
            log.error("刷新token异常, refreshToken: {}", refreshToken, e);
            return null;
        }
    }

    /**
     * 撤销token
     */
    public boolean revokeToken(String accessToken) {
        if (!StringUtils.hasText(accessToken)) {
            log.warn("撤销token失败：accessToken为空");
            return false;
        }

        Map<String, Object> params = new HashMap<>();
        params.put("token", accessToken);
        log.info(">>> rc撤销token请求参数: {}", params);

        try {
            HttpResponse response = HttpRequest.post(config.getRevokeUrl())
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .form(params)
                    .execute();

            boolean success = response.isOk();
            if (success) {
                log.debug("撤销token成功");
            } else {
                log.warn("撤销token失败，HTTP状态码: {}, 响应内容: {}", response.getStatus(), response.body());
            }
            return success;
        } catch (Exception e) {
            log.error("撤销token异常, accessToken: {}", accessToken, e);
            return false;
        }
    }

    /**
     * 验证token是否有效
     */
    public boolean validateToken(String accessToken) {
        JWTClaimsSet claimsSet = jwtUtil.getJwtClaimsSet(accessToken);
        if (claimsSet == null) {
            return false;
        }

        return jwtUtil.isNotExpiration(claimsSet);
    }

    /**
     * 从token中获取用户信息
     */
    public Map<String, Object> getUserInfoFromToken(String accessToken) {
        if (!StringUtils.hasText(accessToken)) {
            log.warn("获取用户信息失败：accessToken为空");
            return null;
        }

        log.info(">>> rc获取用户信息，access_token放在header中");

        try {
            HttpResponse response = HttpRequest.post(config.getTokenUrl())
                    .header("access_token", accessToken)
                    .header("Content-Type", "application/json")
                    .execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info(">>> rc获取用户信息成功: {}", responseBody);

                @SuppressWarnings("unchecked")
                Map<String, Object> result = JSONUtil.toBean(responseBody, Map.class);

                // 检查响应是否成功
                Boolean success = (Boolean) result.get("success");
                if (success != null && success) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> data = (Map<String, Object>) result.get("data");
                    if (data != null) {
                        // 转换字段名以保持兼容性
                        Map<String, Object> userInfo = new HashMap<>();
                        userInfo.put("cractOuterUuid", data.get("cractOuterUuid"));
                        userInfo.put("cractName", data.get("cractName"));
                        userInfo.put("cractCode", data.get("cractCode"));
                        userInfo.put("cractUuid", data.get("cractUuid"));
                        userInfo.put("cractOrgOwners", data.get("cractOrgOwners"));
                        userInfo.put("cractStatus", data.get("cractStatus"));
                        userInfo.put("cractNature", data.get("cractNature"));
                        userInfo.put("cractOrder", data.get("cractOrder"));
                        userInfo.put("cractCreateTime", data.get("cractCreateTime"));
                        userInfo.put("cractUpdateTime", data.get("cractUpdateTime"));

                        // 处理扩展信息
                        @SuppressWarnings("unchecked")
                        Map<String, Object> ext = (Map<String, Object>) data.get("ext");
                        if (ext != null) {
                            userInfo.put("cractGovDingCode", ext.get("cractGovDingCode"));
                            userInfo.put("cractGovDingAccountId", ext.get("cractGovDingAccountId"));
                        }

                        log.debug(">>> 解析后的用户信息: {}", userInfo);
                        return userInfo;
                    }
                } else {
                    log.error("获取用户信息失败: 接口返回success=false, 响应内容={}", responseBody);
                }
                return null;
            } else {
                log.error("获取用户信息失败: HTTP状态码={}, 响应内容={}", response.getStatus(), response.body());
                return null;
            }
        } catch (Exception e) {
            log.error("获取用户信息异常, accessToken: {}", accessToken, e);
            return null;
        }
    }

    public String getLogoutUrl() {
        return UriComponentsBuilder.fromHttpUrl(config.getLogoutUrl())
                .queryParam("client_id", config.getClientId())
                .build()
                .toString();
    }

    public ResponseData getToken(String code) {
        Map<String, Object> map = getTokenByCode(code);
        if (map == null) {
            return ResponseData.error(SsoResponseCodeEnum.TOKEN_GET_FAILED.getCode(), SsoResponseCodeEnum.TOKEN_GET_FAILED.getMessage());
        }
        return ResponseData.success(map);
    }

    public ResponseData getUserInfoFromCode(String code) {
        //进入调试状态，直接搞成成功
        // String userId = "GE_b544f00ebcea451b9f7da8ffa4631c82";
        // SysUser user = sysUserService.getUserById(userId);
        // String token = authService.doLogin(user);
        // return ResponseData.error(SsoResponseCodeEnum.USER_INFO_PARSE_FAILED.getCode(), SsoResponseCodeEnum.USER_INFO_PARSE_FAILED.getMessage());
        Map<String, Object> map = getTokenByCode(code);
        if (map == null) {
            return ResponseData.error(SsoResponseCodeEnum.TOKEN_GET_FAILED.getCode(), SsoResponseCodeEnum.TOKEN_GET_FAILED.getMessage());
        }
        String accessToken = (String) map.get("access_token");
        log.info(">>> accessToken: {}", accessToken);
        Map<String, Object> userInfo = getUserInfoFromToken(accessToken);
        log.info(">>> userInfo: {}", userInfo);
        if (userInfo == null) {
            return ResponseData.error(SsoResponseCodeEnum.USER_INFO_PARSE_FAILED.getCode(), SsoResponseCodeEnum.USER_INFO_PARSE_FAILED.getMessage());
        }
        //获取用户id，如果有就登录，没有就返回相关信息
        String userId = (String) userInfo.get("cractOuterUuid");
        if (StringUtils.hasText(userId)) {
            SysUser user = sysUserService.getUserById(userId);
            log.info(">>> user: {}", user);
            if (user == null) {
                return ResponseData.error(SsoResponseCodeEnum.USER_NOT_EXIST.getCode(), SsoResponseCodeEnum.USER_NOT_EXIST.getMessage());
            }
            String token = authService.doLogin(user);
            log.info(">>> 登录成功，token: {}", token);
            SuccessResponseData successResponseData = ResponseData.success(SsoResponseCodeEnum.LOGIN_SUCCESS.getCode(), SsoResponseCodeEnum.LOGIN_SUCCESS.getMessage(), token);
            log.info(">>> successResponseData: {}", successResponseData);
            return successResponseData;
        }
        return ResponseData.error(SsoResponseCodeEnum.USER_INFO_PARSE_FAILED.getCode(), SsoResponseCodeEnum.USER_INFO_PARSE_FAILED.getMessage());
    }
}
