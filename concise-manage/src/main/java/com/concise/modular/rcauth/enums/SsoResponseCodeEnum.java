package com.concise.modular.rcauth.enums;

import lombok.Getter;

/**
 * SSO认证响应码枚举
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Getter
public enum SsoResponseCodeEnum {

    /**
     * 登录成功
     */
    LOGIN_SUCCESS(200, "登录成功"),

    /**
     * 获取token失败
     */
    TOKEN_GET_FAILED(4001, "获取token失败"),

    /**
     * 用户不存在
     */
    USER_NOT_EXIST(4002, "用户不存在"),

    /**
     * 用户信息解析失败
     */
    USER_INFO_PARSE_FAILED(4003, "解析失败，用户信息未获取到"),

    /**
     * 授权码为空
     */
    CODE_EMPTY(4004, "授权码不能为空"),

    /**
     * token验证失败
     */
    TOKEN_VALIDATE_FAILED(4005, "token验证失败"),

    /**
     * 用户未登录
     */
    USER_NOT_LOGIN(4006, "用户未登录"),

    /**
     * 登出成功
     */
    LOGOUT_SUCCESS(200, "登出成功"),

    /**
     * 登出失败
     */
    LOGOUT_FAILED(4007, "登出失败");

    private final Integer code;

    private final String message;

    SsoResponseCodeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
