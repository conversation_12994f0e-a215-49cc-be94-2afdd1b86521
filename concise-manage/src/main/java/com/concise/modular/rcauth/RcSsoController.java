package com.concise.modular.rcauth;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.concise.common.pojo.response.ResponseData;
import com.concise.sys.modular.user.service.SysUserService;

import lombok.extern.slf4j.Slf4j;

@Controller
@RequestMapping("/rcsso")
@Slf4j
public class RcSsoController {


    @Autowired
    private AuthCenterConfig config;

    @Resource
    private AuthCenterService authCenterService;

    @Resource
    private SysUserService sysUserService;


    /**
     * 重定向参数获取接口
     */
    @ResponseBody
    @GetMapping("/redirectInfo")
    public ResponseData redirect() {
        Map<String, String> map = new HashMap<>();
        map.put("client_id", config.getClientId());
        map.put("scope", "AUTH");
        map.put("response_type", "code");
        map.put("redirect_uri", config.getRedirectUri());
        //跳转地址
        map.put("jumpUrl", config.getAuthorizeUrl()+"?client_id="+config.getClientId()+"&scope=AUTH&response_type=code&redirect_uri="+config.getRedirectUri());
        return ResponseData.success(map);
    }

    /**
     * 根据临时token获取正式token
     */
    @ResponseBody
    @GetMapping("/getToken")
    public ResponseData getToken(@RequestParam("code") String code) {
        if (!StringUtils.hasText(code)) {
            return ResponseData.error(4004, "授权码不能为空");
        }
        log.debug(">>> tokenCode: {}", code);
        return authCenterService.getToken(code);
    }

    /**
     * 根据code直接拿用户信息
     */
    @ResponseBody
    @GetMapping("/getUserInfo")
    public ResponseData getUserInfo(@RequestParam("code") String code) {
        if (!StringUtils.hasText(code)) {
            return ResponseData.error(4004, "授权码不能为空");
        }
        log.info(">>> code: {}", code);
        return authCenterService.getUserInfoFromCode(code);
    }

    /**
     * 登出
     */
    @GetMapping("/logout")
    public String logout(HttpSession session) {
        String accessToken = (String) session.getAttribute("access_token");

        // 撤销token
        if (StringUtils.hasText(accessToken)) {
            authCenterService.revokeToken(accessToken);
        }

        // 清除session
        session.invalidate();

        // 重定向到认证中心登出
        return "redirect:" + authCenterService.getLogoutUrl();
    }

    /**
     * 检查登录状态
     */
    @GetMapping("/check")
    @ResponseBody
    public Map<String, Object> checkLoginStatus(HttpSession session) {
        Map<String, Object> result = new HashMap<>();

        String accessToken = (String) session.getAttribute("access_token");
        if (StringUtils.hasText(accessToken) && authCenterService.validateToken(accessToken)) {
            result.put("loggedIn", true);
            result.put("user", session.getAttribute("user"));
        } else {
            result.put("loggedIn", false);
        }

        return result;
    }
}
