package com.concise.modular.unify.pushrecord.service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.concise.modular.unify.pushrecord.entity.PushRecord;
import com.concise.modular.unify.pushtargetsystem.entity.PushTargetSystem;
import com.concise.modular.unify.pushtargetsystem.service.PushTargetSystemService;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据推送服务
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Slf4j
@Service
public class DataPushService {

    @Resource
    private PushRecordService pushRecordService;

    @Resource
    private PushTargetSystemService pushTargetSystemService;

    /**
     * 异步推送数据到目标系统
     *
     * @param sourceTable 源数据表名
     * @param sourceId 源数据ID
     * @param targetSystem 目标系统名称
     * @param targetUrl 目标系统接口地址
     * @param pushType 推送类型
     * @param data 推送数据
     */
    @Async
    public void pushDataAsync(String sourceTable, String sourceId, String targetSystem, 
                             String targetUrl, String pushType, Object data) {
        String pushData = JSON.toJSONString(data);
        String recordId = pushRecordService.createPushRecord(sourceTable, sourceId, targetSystem, 
                                                            targetUrl, pushType, pushData);
        
        // 更新状态为推送中
        pushRecordService.updatePushStatus(recordId, 3, null, "开始推送");
        
        try {
            // 执行推送
            HttpResponse response = executePush(targetUrl, pushData, pushType);
            
            if (response.isOk()) {
                // 推送成功
                pushRecordService.updatePushStatus(recordId, 1, 
                    String.valueOf(response.getStatus()), "推送成功");

            } else {
                // 推送失败
                pushRecordService.updatePushStatus(recordId, 2, 
                    String.valueOf(response.getStatus()), "推送失败：" + response.body());
            }
        } catch (Exception e) {
            // 推送异常，禁止输出堆栈信息
            pushRecordService.updatePushStatus(recordId, 2, "500",
                "推送异常：" + e.getClass().getSimpleName());
        }
    }

    /**
     * 执行HTTP推送
     *
     * @param targetUrl 目标URL
     * @param pushData 推送数据
     * @param pushType 推送类型
     * @return HTTP响应
     */
    private HttpResponse executePush(String targetUrl, String pushData, String pushType) {
        return executePushWithConfig(targetUrl, pushData, pushType, null);
    }

    /**
     * 执行HTTP推送（带配置）
     *
     * @param targetUrl 目标URL
     * @param pushData 推送数据
     * @param pushType 推送类型
     * @param targetSystem 目标系统配置
     * @return HTTP响应
     */
    private HttpResponse executePushWithConfig(String targetUrl, String pushData, String pushType, PushTargetSystem targetSystem) {
        HttpRequest request = new HttpRequest(targetUrl);
        request.contentType("application/json");

        // 设置超时时间
        int timeout = 30000; // 默认30秒
        if (targetSystem != null && targetSystem.getTimeout() != null) {
            timeout = targetSystem.getTimeout();
        }
        request.timeout(timeout);

        // 设置请求头
        if (targetSystem != null && targetSystem.getHeaders() != null) {
            try {
                JSONObject headers = JSON.parseObject(targetSystem.getHeaders());
                for (Map.Entry<String, Object> entry : headers.entrySet()) {
                    request.header(entry.getKey(), String.valueOf(entry.getValue()));
                }
            } catch (Exception e) {

            }
        }

        // 设置认证
        if (targetSystem != null && targetSystem.getAuthConfig() != null) {
            try {
                JSONObject authConfig = JSON.parseObject(targetSystem.getAuthConfig());
                String authType = targetSystem.getAuthType();
                if ("TOKEN".equals(authType) && authConfig.containsKey("token")) {
                    request.header("Authorization", "Bearer " + authConfig.getString("token"));
                } else if ("BASIC".equals(authType) && authConfig.containsKey("username") && authConfig.containsKey("password")) {
                    request.basicAuth(authConfig.getString("username"), authConfig.getString("password"));
                }
            } catch (Exception e) {

            }
        }

        // 统一使用POST方法，通过pushType参数区分操作类型
        request.method(Method.POST);

        request.body(pushData);
        return request.execute();
    }

    /**
     * 处理待推送的记录
     */
    public void processPendingPushRecords() {
        List<PushRecord> pendingRecords = pushRecordService.getPendingPushRecords();
        
        for (PushRecord record : pendingRecords) {
            try {
                // 更新状态为推送中
                pushRecordService.updatePushStatus(record.getId(), 3, null, "重新推送中");

                // 获取目标系统配置
                PushTargetSystem targetSystem = pushTargetSystemService.getBySystemCode(record.getTargetSystem());

                // 执行推送
                HttpResponse response = executePushWithConfig(record.getTargetUrl(),
                    record.getPushData(), record.getPushType(), targetSystem);

                if (response.isOk()) {
                    // 推送成功
                    pushRecordService.updatePushStatus(record.getId(), 1,
                        String.valueOf(response.getStatus()), "推送成功");
                } else {
                    // 推送失败，设置下次重试时间
                    handlePushFailure(record, String.valueOf(response.getStatus()),
                        "推送失败：" + response.body(), targetSystem);
                }
            } catch (Exception e) {
                // 推送异常，设置下次重试时间
                PushTargetSystem targetSystem = pushTargetSystemService.getBySystemCode(record.getTargetSystem());
                handlePushFailure(record, "500", "推送异常：" + e.getMessage(), targetSystem);
            }
        }
    }

    /**
     * 处理推送失败
     *
     * @param record 推送记录
     * @param responseCode 响应码
     * @param responseMessage 响应消息
     */
    private void handlePushFailure(PushRecord record, String responseCode, String responseMessage) {
        handlePushFailure(record, responseCode, responseMessage, null);
    }

    /**
     * 处理推送失败（带目标系统配置）
     *
     * @param record 推送记录
     * @param responseCode 响应码
     * @param responseMessage 响应消息
     * @param targetSystem 目标系统配置
     */
    private void handlePushFailure(PushRecord record, String responseCode, String responseMessage, PushTargetSystem targetSystem) {
        int retryCount = record.getRetryCount() + 1;
        int maxRetryCount = record.getMaxRetryCount();

        // 如果有目标系统配置，使用配置中的最大重试次数
        if (targetSystem != null && targetSystem.getMaxRetryCount() != null) {
            maxRetryCount = targetSystem.getMaxRetryCount();
        }

        if (retryCount >= maxRetryCount) {
            // 达到最大重试次数，标记为失败
            pushRecordService.updatePushStatus(record.getId(), 2, responseCode,
                responseMessage + "（已达最大重试次数）");
        } else {
            // 计算下次重试时间
            long delayMinutes = calculateRetryDelay(retryCount, targetSystem);
            Date nextRetryTime = new Date(System.currentTimeMillis() +
                TimeUnit.MINUTES.toMillis(delayMinutes));

            PushRecord updateRecord = new PushRecord();
            updateRecord.setId(record.getId());
            updateRecord.setPushStatus(0); // 重置为待推送
            updateRecord.setRetryCount(retryCount);
            updateRecord.setNextRetryTime(nextRetryTime);
            updateRecord.setResponseCode(responseCode);
            updateRecord.setResponseMessage(responseMessage + "（将在" + delayMinutes + "分钟后重试）");

            pushRecordService.updateById(updateRecord);
        }
    }

    /**
     * 计算重试延迟时间
     *
     * @param retryCount 重试次数
     * @param targetSystem 目标系统配置
     * @return 延迟分钟数
     */
    private long calculateRetryDelay(int retryCount, PushTargetSystem targetSystem) {
        if (targetSystem != null && targetSystem.getRetryDelayMinutes() != null) {
            try {
                String[] delays = targetSystem.getRetryDelayMinutes().split(",");
                if (retryCount <= delays.length) {
                    return Long.parseLong(delays[retryCount - 1].trim());
                }
            } catch (Exception e) {
                log.warn("解析重试延迟配置失败：{}", e.getMessage());
            }
        }

        // 默认指数退避：2^retryCount 分钟
        return (long) Math.pow(2, retryCount);
    }

    /**
     * 推送浙政钉用户信息到其他系统（推送原始接收参数）
     *
     * @param receiveBody 原始接收参数
     * @param targetSystem 目标系统名称
     * @param targetUrl 目标系统接口地址
     */
    public void pushLxUserReceiveData(com.concise.modular.unify.unifyuser.entity.ReceiveBody receiveBody,
                                     String targetSystem, String targetUrl) {
        // 直接推送原始接收的参数，不进行解析，统一使用POST方法
        pushDataAsync("lx_user_receive", "original_data", targetSystem,
                     targetUrl, "POST", receiveBody);
    }

    /**
     * 推送浙政钉用户信息到其他系统
     *
     * @param lxUserInfo 浙政钉用户信息
     * @param targetSystem 目标系统名称
     * @param targetUrl 目标系统接口地址
     */
    public void pushLxUserInfo(Object lxUserInfo, String targetSystem, String targetUrl) {
        // 根据操作类型确定推送类型，统一使用POST方法
        String pushType = determinePushType(lxUserInfo);

        pushDataAsync("lx_user_info", String.valueOf(lxUserInfo), targetSystem,
                     targetUrl, pushType, lxUserInfo);
    }

    /**
     * 根据用户信息中的operateType确定推送类型
     *
     * @param lxUserInfo 浙政钉用户信息
     * @return 推送类型
     */
    private String determinePushType(Object lxUserInfo) {
        try {
            if (lxUserInfo instanceof com.concise.modular.unify.unifyuser.entity.LxUserVo) {
                com.concise.modular.unify.unifyuser.entity.LxUserVo userVo =
                    (com.concise.modular.unify.unifyuser.entity.LxUserVo) lxUserInfo;
                int operateType = userVo.getOperatorType();

                switch (operateType) {
                    case 0:
                        return "ADD";    // 新增
                    case 1:
                        return "UPDATE"; // 修改
                    case 2:
                        return "DELETE"; // 删除
                    default:
                        return "ADD";    // 默认为新增
                }
            } else {
                // 如果是其他类型的对象，尝试通过JSON解析获取operateType
                String jsonStr = JSON.toJSONString(lxUserInfo);
                JSONObject jsonObject = JSON.parseObject(jsonStr);
                if (jsonObject.containsKey("operatorType")) {
                    int operateType = jsonObject.getIntValue("operatorType");
                    switch (operateType) {
                        case 0:
                            return "ADD";    // 新增
                        case 1:
                            return "UPDATE"; // 修改
                        case 2:
                            return "DELETE"; // 删除
                        default:
                            return "ADD";    // 默认为新增
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析operateType失败，使用默认推送类型ADD", e);
        }

        return "ADD"; // 默认为新增
    }
}
