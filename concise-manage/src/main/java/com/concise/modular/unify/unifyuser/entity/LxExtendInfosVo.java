package com.concise.modular.unify.unifyuser.entity;

import lombok.Data;

import java.util.Date;

/**
 * 龙晰综管用户扩展信息
 */
@Data
public class LxExtendInfosVo {

    /**
     *  用户ID（浙政钉employeeCode）
     */
    private String userId;
    /**
     *  职务
     */
    private String position;
    /**
     *  职级
     */
    private String rank;
    /**
     *  用户关联的部门ID
     */
    private String userDeptId;
    /**
     *  用户关联的部门名称
     */
    private String userDeptName;
    /**
     *  办公地址
     */
    private String workAddress;
    /**
     *  办公电话
     */
    private String workPhone;

    /**
     * 矫正机构id
     */
    private String jzjg;

    /**
     * 矫正机构名称
     */
    private String jzjgName;

    /**
     * 更新时间
     */
    private Date updateTime;

}
