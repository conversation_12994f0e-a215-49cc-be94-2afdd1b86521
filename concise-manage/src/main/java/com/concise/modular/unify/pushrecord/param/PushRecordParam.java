package com.concise.modular.unify.pushrecord.param;

import java.util.Date;

import javax.validation.constraints.NotBlank;

import lombok.Data;

/**
 * 推送记录表参数类
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Data
public class PushRecordParam {

    /**
     * 主键ID
     */
    @NotBlank(message = "主键ID不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 源数据表名
     */
    @NotBlank(message = "源数据表名不能为空，请检查sourceTable参数", groups = {add.class, edit.class})
    private String sourceTable;

    /**
     * 源数据ID
     */
    @NotBlank(message = "源数据ID不能为空，请检查sourceId参数", groups = {add.class, edit.class})
    private String sourceId;

    /**
     * 目标系统名称
     */
    @NotBlank(message = "目标系统名称不能为空，请检查targetSystem参数", groups = {add.class, edit.class})
    private String targetSystem;

    /**
     * 目标系统接口地址
     */
    @NotBlank(message = "目标系统接口地址不能为空，请检查targetUrl参数", groups = {add.class, edit.class})
    private String targetUrl;

    /**
     * 推送类型（ADD:新增 UPDATE:更新 DELETE:删除）
     */
    @NotBlank(message = "推送类型不能为空，请检查pushType参数", groups = {add.class, edit.class})
    private String pushType;

    /**
     * 推送的数据内容（JSON格式）
     */
    private String pushData;

    /**
     * 推送状态（0:待推送 1:推送成功 2:推送失败 3:推送中）
     */
    private Integer pushStatus;

    /**
     * 推送时间
     */
    private Date pushTime;

    /**
     * 响应状态码
     */
    private String responseCode;

    /**
     * 响应消息
     */
    private String responseMessage;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 下次重试时间
     */
    private Date nextRetryTime;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 参数校验分组：增加
     */
    public @interface add {
    }

    /**
     * 参数校验分组：编辑
     */
    public @interface edit {
    }

    /**
     * 参数校验分组：删除
     */
    public @interface delete {
    }

    /**
     * 参数校验分组：详情
     */
    public @interface detail {
    }

}
