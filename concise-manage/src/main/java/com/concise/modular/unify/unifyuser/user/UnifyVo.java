package com.concise.modular.unify.unifyuser.user;


import com.concise.modular.unify.unifyuser.depart.ExtendField;
import lombok.Data;

import java.util.List;

@Data
public class UnifyVo {
    private List<Belongs> belongs;
    private String displayName;
    private List<Emails> emails;
    private ExtendField extendField;
    private String externalId;
    private String id;
    private String password;
    private List<PhoneNumbers> phoneNumbers;
    private String userName;
    private String locked;
    private String moziDeptCode;
    /**
     * 矫正机构id
     */
    private String correctOrgId;
    /**
     * 矫正机构名称
     */
    private String correctOrgName;

}

