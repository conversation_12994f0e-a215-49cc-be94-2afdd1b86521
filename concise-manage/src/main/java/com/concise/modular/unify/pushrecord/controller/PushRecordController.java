package com.concise.modular.unify.pushrecord.controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.modular.unify.pushrecord.param.PushRecordParam;
import com.concise.modular.unify.pushrecord.service.PushRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;

/**
 * 推送记录表控制器
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Api(tags = "推送记录表")
@RestController
public class PushRecordController {

    @Resource
    private PushRecordService pushRecordService;

    /**
     * 查询推送记录表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @GetMapping("/pushRecord/page")
    @ApiOperation("推送记录表_分页查询")
    @BusinessLog(title = "推送记录表_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(PushRecordParam pushRecordParam) {
        return new SuccessResponseData(pushRecordService.page(pushRecordParam));
    }

    /**
     * 添加推送记录表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @PostMapping("/pushRecord/add")
    @ApiOperation("推送记录表_增加")
    @BusinessLog(title = "推送记录表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(PushRecordParam.add.class) PushRecordParam pushRecordParam) {
        pushRecordService.add(pushRecordParam);
        return new SuccessResponseData();
    }

    /**
     * 删除推送记录表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @PostMapping("/pushRecord/delete")
    @ApiOperation("推送记录表_删除")
    @BusinessLog(title = "推送记录表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(PushRecordParam.delete.class) PushRecordParam pushRecordParam) {
        pushRecordService.delete(pushRecordParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑推送记录表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @PostMapping("/pushRecord/edit")
    @ApiOperation("推送记录表_编辑")
    @BusinessLog(title = "推送记录表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(PushRecordParam.edit.class) PushRecordParam pushRecordParam) {
        pushRecordService.edit(pushRecordParam);
        return new SuccessResponseData();
    }

    /**
     * 查看推送记录表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @GetMapping("/pushRecord/detail")
    @ApiOperation("推送记录表_查看")
    @BusinessLog(title = "推送记录表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(PushRecordParam.detail.class) PushRecordParam pushRecordParam) {
        return new SuccessResponseData(pushRecordService.detail(pushRecordParam));
    }

    /**
     * 推送记录表列表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @GetMapping("/pushRecord/list")
    @ApiOperation("推送记录表_列表")
    @BusinessLog(title = "推送记录表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(PushRecordParam pushRecordParam) {
        return new SuccessResponseData(pushRecordService.list(pushRecordParam));
    }

    /**
     * 重试推送
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @PostMapping("/pushRecord/retry")
    @ApiOperation("推送记录表_重试推送")
    @BusinessLog(title = "推送记录表_重试推送", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData retry(@RequestParam String id) {
        pushRecordService.retryPush(id);
        return new SuccessResponseData();
    }

    /**
     * 获取待推送记录
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @GetMapping("/pushRecord/pending")
    @ApiOperation("推送记录表_获取待推送记录")
    @BusinessLog(title = "推送记录表_获取待推送记录", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getPendingRecords() {
        return new SuccessResponseData(pushRecordService.getPendingPushRecords());
    }

}
