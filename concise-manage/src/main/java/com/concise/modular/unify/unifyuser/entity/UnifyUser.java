
package com.concise.modular.unify.unifyuser.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 浙政钉用户表
 *
 * <AUTHOR>
 * @date 2022-04-14 14:13:15
 */
@Data
@TableName("unify_user")
public class UnifyUser {

    /**
     * 用户id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 登录账号
     */
    @Excel(name = "登录账号")
    private String userName;


    /**
     * 密码
     */
    @Excel(name = "密码")
    private String password;


    /**
     * 用户的显示名称
     */
    @Excel(name = "用户的显示名称")
    private String displayName;


    /**
     * 电子邮件
     */
    @Excel(name = "电子邮件")
    private String email;


    /**
     * 电话
     */
    @Excel(name = "电话")
    private String phoneNumbers;


    /**
     * 外部 ID,唯一,
     */
    private String externalId;
    /**
     * 为账户指定组织单位
     */
    @Excel(name = "为账户指定组织单位")
    private String belongs;


    /**
     * 是否禁用账户，ture 禁用账户,false 启用账
     */
    @Excel(name = "是否禁用账户，ture 禁用账户,false 启用账")
    private String locked;


    /**
     * 扩展字段,attributes 为系统定义扩展字段
     */
    @Excel(name = "扩展字段,attributes 为系统定义扩展字段")
    private String extendfield;


    /**
     * employee码
     */
    @Excel(name = "employee码")
    private String moziDeptCode;


    /**
     * json字符串，完整保存
     */
    @Excel(name = "json字符串，完整保存")
    private String jsonString;


    /**
     * 删除时间
     */
    @Excel(name = "删除时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date deleteTime;


    /**
     * 删除标记
     */
    @Excel(name = "删除标记")
    private String deleteFlag;


    /**
     * 矫正机构id
     */
    @Excel(name = "矫正机构id")
    private String correctOrgId;


    /**
     * 矫正机构名称
     */
    @Excel(name = "矫正机构名称")
    private String correctOrgName;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

}
