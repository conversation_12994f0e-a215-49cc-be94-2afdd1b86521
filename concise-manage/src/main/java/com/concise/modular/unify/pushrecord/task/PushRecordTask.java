package com.concise.modular.unify.pushrecord.task;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.concise.modular.unify.pushrecord.service.DataPushService;

import lombok.extern.slf4j.Slf4j;

/**
 * 推送记录定时任务
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Slf4j
@Component
public class PushRecordTask {

    @Resource
    private DataPushService dataPushService;

    /**
     * 处理待推送的记录
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 5 * 60 * 1000)
    public void processPendingPushRecords() {
        try {
            log.debug("开始处理待推送记录...");
            dataPushService.processPendingPushRecords();
            log.debug("处理待推送记录完成");
        } catch (Exception e) {
            log.error("处理待推送记录异常: {}", e.getMessage());
        }
    }
}
