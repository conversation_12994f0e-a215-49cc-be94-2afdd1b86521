package com.concise.modular.unify.unifyuser.entity;

import lombok.Data;

import java.util.List;

/**
 * 龙晰综管用户信息vo
 */
@Data
public class LxUserVo {

    /**
     *  用户id（浙政钉employeeCode）
     */
    private String id;
    /**
     *  姓名
     */
    private String name;
    /**
     *  用户的显示名称,唯一,支持中文
     */
    private String userName;
    /**
     *  性别（0:未知 1:男  2:女 9：未说明的性别）
     */
    private int sex;
    /**
     *  浙政钉accountId
     */
    private String accountId;
    /**
     *  机构id(一个或多个)多个用英文逗号隔开
     */
    private String deptId;
    /**
     *  动作类型 0:新增或修改  1:新增或修改  2:删除
     */
    private int operatorType;
    /**
     *  扩展信息
     */
    private List<LxExtendInfosVo> extendInfos;
}
