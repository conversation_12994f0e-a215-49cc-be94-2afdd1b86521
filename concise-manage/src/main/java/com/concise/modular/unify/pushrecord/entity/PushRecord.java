package com.concise.modular.unify.pushrecord.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.pojo.base.entity.BaseEntity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 推送记录表
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("push_record")
public class PushRecord extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 源数据表名
     */
    @Excel(name = "源数据表名")
    private String sourceTable;

    /**
     * 源数据ID
     */
    @Excel(name = "源数据ID")
    private String sourceId;

    /**
     * 目标系统名称
     */
    @Excel(name = "目标系统名称")
    private String targetSystem;

    /**
     * 目标系统接口地址
     */
    @Excel(name = "目标接口地址")
    private String targetUrl;

    /**
     * 推送类型（ADD:新增 UPDATE:更新 DELETE:删除）
     */
    @Excel(name = "推送类型")
    private String pushType;

    /**
     * 推送的数据内容（JSON格式）
     */
    private String pushData;

    /**
     * 推送状态（0:待推送 1:推送成功 2:推送失败 3:推送中）
     */
    @Excel(name = "推送状态")
    private Integer pushStatus;

    /**
     * 推送时间
     */
    @Excel(name = "推送时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss", width = 20)
    private Date pushTime;

    /**
     * 响应状态码
     */
    @Excel(name = "响应状态码")
    private String responseCode;

    /**
     * 响应消息
     */
    @Excel(name = "响应消息")
    private String responseMessage;

    /**
     * 重试次数
     */
    @Excel(name = "重试次数")
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    @Excel(name = "最大重试次数")
    private Integer maxRetryCount;

    /**
     * 下次重试时间
     */
    @Excel(name = "下次重试时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss", width = 20)
    private Date nextRetryTime;

    /**
     * 创建人ID
     */
    @Excel(name = "创建人ID")
    private String createUserId;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

}
