package com.concise.modular.unify.lxuserinfo.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.unify.lxuserinfo.entity.LxUserInfo;
import com.concise.modular.unify.lxuserinfo.param.LxUserInfoParam;

/**
 * 浙政钉用户信息表service接口
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface LxUserInfoService extends IService<LxUserInfo> {

    /**
     * 查询浙政钉用户信息表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    PageResult<LxUserInfo> page(LxUserInfoParam lxUserInfoParam);

    /**
     * 浙政钉用户信息表列表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    List<LxUserInfo> list(LxUserInfoParam lxUserInfoParam);

    /**
     * 添加浙政钉用户信息表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    void add(LxUserInfoParam lxUserInfoParam);

    /**
     * 删除浙政钉用户信息表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    void delete(LxUserInfoParam lxUserInfoParam);

    /**
     * 编辑浙政钉用户信息表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    void edit(LxUserInfoParam lxUserInfoParam);

    /**
     * 查看浙政钉用户信息表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    LxUserInfo detail(LxUserInfoParam lxUserInfoParam);

    /**
     * 保存浙政钉推送的用户信息
     *
     * @param lxUserVo 浙政钉用户信息
     * <AUTHOR>
     * @date 2024-12-20
     */
    void saveLxUserInfo(com.concise.modular.unify.unifyuser.entity.LxUserVo lxUserVo);

    /**
     * 处理浙政钉用户信息
     *
     * @param id 用户ID
     * <AUTHOR>
     * @date 2024-12-20
     */
    void processLxUserInfo(String id);

    /**
     * 保存用户机构关系
     *
     * @param userId 用户ID
     * @param extendInfos 扩展信息列表
     * <AUTHOR>
     * @date 2024-12-20
     */
    void saveUserDeptRelations(String userId, java.util.List<com.concise.modular.unify.unifyuser.entity.LxExtendInfosVo> extendInfos);
}
