package com.concise.modular.unify.pushrecord.service;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.concise.modular.unify.pushtargetsystem.entity.PushTargetSystem;
import com.concise.modular.unify.pushtargetsystem.service.PushTargetSystemService;
import com.concise.modular.unify.unifyuser.entity.ReceiveBody;

import lombok.extern.slf4j.Slf4j;

/**
 * 推送管理服务
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Slf4j
@Service
public class PushManagerService {

    @Resource
    private DataPushService dataPushService;

    @Resource
    private PushTargetSystemService pushTargetSystemService;

    /**
     * 推送浙政钉用户数据到所有启用的目标系统
     *
     * @param receiveBody 原始接收参数
     */
    @Async
    public void pushToAllTargets(ReceiveBody receiveBody) {
        List<PushTargetSystem> enabledTargets = pushTargetSystemService.getEnabledSystems();

        if (enabledTargets.isEmpty()) {
            log.info("没有启用的推送目标系统");
            return;
        }

        for (PushTargetSystem target : enabledTargets) {
            try {
                log.info("开始推送数据到目标系统：{}", target.getSystemName());
                dataPushService.pushLxUserReceiveData(receiveBody, target.getSystemName(), target.getTargetUrl());
            } catch (Exception e) {
                log.error("推送数据到目标系统失败，系统名称：{}", target.getSystemName(), e);
            }
        }
    }

    /**
     * 推送浙政钉用户数据到指定目标系统
     *
     * @param receiveBody 原始接收参数
     * @param systemCode 目标系统编码
     */
    public void pushToTarget(ReceiveBody receiveBody, String systemCode) {
        PushTargetSystem target = pushTargetSystemService.getBySystemCode(systemCode);

        if (target == null) {
            log.warn("未找到目标系统配置：{}", systemCode);
            return;
        }

        if (target.getEnabled() != 1) {
            log.warn("目标系统已禁用：{}", systemCode);
            return;
        }

        if (target.getStatus() != 1) {
            log.warn("目标系统状态无效：{}", systemCode);
            return;
        }

        try {
            log.info("开始推送数据到目标系统：{}", target.getSystemName());
            dataPushService.pushLxUserReceiveData(receiveBody, target.getSystemName(), target.getTargetUrl());
        } catch (Exception e) {
            log.error("推送数据到目标系统失败，系统名称：{}", target.getSystemName(), e);
        }
    }

    /**
     * 获取所有启用的目标系统
     */
    public List<PushTargetSystem> getEnabledTargets() {
        return pushTargetSystemService.getEnabledSystems();
    }

    /**
     * 检查推送配置是否有效
     */
    public boolean isConfigValid() {
        List<PushTargetSystem> systems = pushTargetSystemService.getEnabledSystems();
        return systems != null && !systems.isEmpty();
    }
}
