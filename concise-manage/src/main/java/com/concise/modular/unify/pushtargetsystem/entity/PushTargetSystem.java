package com.concise.modular.unify.pushtargetsystem.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 推送目标系统表
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("push_target_system")
public class PushTargetSystem extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 系统名称
     */
    @Excel(name = "系统名称")
    private String systemName;

    /**
     * 系统编码（唯一）
     */
    @Excel(name = "系统编码")
    private String systemCode;

    /**
     * 目标接口地址
     */
    @Excel(name = "目标接口地址")
    private String targetUrl;

    /**
     * 系统描述
     */
    @Excel(name = "系统描述")
    private String description;

    /**
     * 是否启用（0:禁用 1:启用）
     */
    @Excel(name = "是否启用")
    private Integer enabled;

    /**
     * 超时时间（毫秒）
     */
    @Excel(name = "超时时间")
    private Integer timeout;

    /**
     * 最大重试次数
     */
    @Excel(name = "最大重试次数")
    private Integer maxRetryCount;

    /**
     * 重试延迟时间（分钟），逗号分隔
     */
    @Excel(name = "重试延迟时间")
    private String retryDelayMinutes;

    /**
     * 认证类型（NONE:无认证 BASIC:基础认证 TOKEN:令牌认证）
     */
    @Excel(name = "认证类型")
    private String authType;

    /**
     * 认证配置（JSON格式）
     */
    private String authConfig;

    /**
     * 请求头配置（JSON格式）
     */
    private String headers;

    /**
     * 排序顺序
     */
    @Excel(name = "排序顺序")
    private Integer sortOrder;

    /**
     * 状态（0:无效 1:有效）
     */
    @Excel(name = "状态")
    private Integer status;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

}
