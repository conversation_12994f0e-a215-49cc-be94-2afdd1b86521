package com.concise.modular.unify.pushrecord.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.unify.pushrecord.entity.PushRecord;
import com.concise.modular.unify.pushrecord.param.PushRecordParam;
import java.util.List;

/**
 * 推送记录表service接口
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface PushRecordService extends IService<PushRecord> {

    /**
     * 查询推送记录表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    PageResult<PushRecord> page(PushRecordParam pushRecordParam);

    /**
     * 推送记录表列表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    List<PushRecord> list(PushRecordParam pushRecordParam);

    /**
     * 添加推送记录表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    void add(PushRecordParam pushRecordParam);

    /**
     * 删除推送记录表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    void delete(PushRecordParam pushRecordParam);

    /**
     * 编辑推送记录表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    void edit(PushRecordParam pushRecordParam);

    /**
     * 查看推送记录表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    PushRecord detail(PushRecordParam pushRecordParam);

    /**
     * 创建推送记录
     *
     * @param sourceTable 源数据表名
     * @param sourceId 源数据ID
     * @param targetSystem 目标系统名称
     * @param targetUrl 目标系统接口地址
     * @param pushType 推送类型
     * @param pushData 推送数据
     * @return 推送记录ID
     * <AUTHOR>
     * @date 2024-12-20
     */
    String createPushRecord(String sourceTable, String sourceId, String targetSystem, 
                           String targetUrl, String pushType, String pushData);

    /**
     * 更新推送状态
     *
     * @param id 推送记录ID
     * @param pushStatus 推送状态
     * @param responseCode 响应状态码
     * @param responseMessage 响应消息
     * <AUTHOR>
     * @date 2024-12-20
     */
    void updatePushStatus(String id, Integer pushStatus, String responseCode, String responseMessage);

    /**
     * 重试推送
     *
     * @param id 推送记录ID
     * <AUTHOR>
     * @date 2024-12-20
     */
    void retryPush(String id);

    /**
     * 获取待推送的记录
     *
     * @return 待推送记录列表
     * <AUTHOR>
     * @date 2024-12-20
     */
    List<PushRecord> getPendingPushRecords();
}
