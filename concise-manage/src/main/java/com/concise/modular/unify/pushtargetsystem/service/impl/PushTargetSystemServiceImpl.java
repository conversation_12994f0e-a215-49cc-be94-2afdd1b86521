package com.concise.modular.unify.pushtargetsystem.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.unify.pushtargetsystem.entity.PushTargetSystem;
import com.concise.modular.unify.pushtargetsystem.enums.PushTargetSystemExceptionEnum;
import com.concise.modular.unify.pushtargetsystem.mapper.PushTargetSystemMapper;
import com.concise.modular.unify.pushtargetsystem.param.PushTargetSystemParam;
import com.concise.modular.unify.pushtargetsystem.service.PushTargetSystemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 推送目标系统表service接口实现类
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Service
public class PushTargetSystemServiceImpl extends ServiceImpl<PushTargetSystemMapper, PushTargetSystem> implements PushTargetSystemService {

    @Override
    public PageResult<PushTargetSystem> page(PushTargetSystemParam pushTargetSystemParam) {
        QueryWrapper<PushTargetSystem> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(pushTargetSystemParam)) {
            // 根据系统名称 查询
            if (ObjectUtil.isNotEmpty(pushTargetSystemParam.getSystemName())) {
                queryWrapper.lambda().like(PushTargetSystem::getSystemName, pushTargetSystemParam.getSystemName());
            }
            // 根据系统编码 查询
            if (ObjectUtil.isNotEmpty(pushTargetSystemParam.getSystemCode())) {
                queryWrapper.lambda().like(PushTargetSystem::getSystemCode, pushTargetSystemParam.getSystemCode());
            }
            // 根据是否启用 查询
            if (ObjectUtil.isNotEmpty(pushTargetSystemParam.getEnabled())) {
                queryWrapper.lambda().eq(PushTargetSystem::getEnabled, pushTargetSystemParam.getEnabled());
            }
            // 根据状态 查询
            if (ObjectUtil.isNotEmpty(pushTargetSystemParam.getStatus())) {
                queryWrapper.lambda().eq(PushTargetSystem::getStatus, pushTargetSystemParam.getStatus());
            }
        }
        queryWrapper.lambda().orderByAsc(PushTargetSystem::getSortOrder).orderByDesc(PushTargetSystem::getCreateTime);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<PushTargetSystem> list(PushTargetSystemParam pushTargetSystemParam) {
        return this.list();
    }

    @Override
    public void add(PushTargetSystemParam pushTargetSystemParam) {
        // 检查系统编码是否重复
        QueryWrapper<PushTargetSystem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PushTargetSystem::getSystemCode, pushTargetSystemParam.getSystemCode());
        if (this.count(queryWrapper) > 0) {
            throw new ServiceException(PushTargetSystemExceptionEnum.SYSTEM_CODE_REPEAT);
        }
        
        PushTargetSystem pushTargetSystem = new PushTargetSystem();
        BeanUtil.copyProperties(pushTargetSystemParam, pushTargetSystem);
        
        // 设置默认值
        if (ObjectUtil.isNull(pushTargetSystem.getEnabled())) {
            pushTargetSystem.setEnabled(1);
        }
        if (ObjectUtil.isNull(pushTargetSystem.getTimeout())) {
            pushTargetSystem.setTimeout(30000);
        }
        if (ObjectUtil.isNull(pushTargetSystem.getMaxRetryCount())) {
            pushTargetSystem.setMaxRetryCount(3);
        }
        if (ObjectUtil.isEmpty(pushTargetSystem.getRetryDelayMinutes())) {
            pushTargetSystem.setRetryDelayMinutes("2,4,8");
        }
        if (ObjectUtil.isEmpty(pushTargetSystem.getAuthType())) {
            pushTargetSystem.setAuthType("NONE");
        }
        if (ObjectUtil.isNull(pushTargetSystem.getSortOrder())) {
            pushTargetSystem.setSortOrder(0);
        }
        if (ObjectUtil.isNull(pushTargetSystem.getStatus())) {
            pushTargetSystem.setStatus(1);
        }
        
        this.save(pushTargetSystem);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(PushTargetSystemParam pushTargetSystemParam) {
        this.removeById(pushTargetSystemParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(PushTargetSystemParam pushTargetSystemParam) {
        PushTargetSystem pushTargetSystem = this.queryPushTargetSystem(pushTargetSystemParam);
        
        // 检查系统编码是否重复（排除自己）
        if (ObjectUtil.isNotEmpty(pushTargetSystemParam.getSystemCode()) && 
            !pushTargetSystemParam.getSystemCode().equals(pushTargetSystem.getSystemCode())) {
            QueryWrapper<PushTargetSystem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(PushTargetSystem::getSystemCode, pushTargetSystemParam.getSystemCode())
                    .ne(PushTargetSystem::getId, pushTargetSystemParam.getId());
            if (this.count(queryWrapper) > 0) {
                throw new ServiceException(PushTargetSystemExceptionEnum.SYSTEM_CODE_REPEAT);
            }
        }
        
        BeanUtil.copyProperties(pushTargetSystemParam, pushTargetSystem);
        this.updateById(pushTargetSystem);
    }

    @Override
    public PushTargetSystem detail(PushTargetSystemParam pushTargetSystemParam) {
        return this.queryPushTargetSystem(pushTargetSystemParam);
    }

    @Override
    public List<PushTargetSystem> getEnabledSystems() {
        QueryWrapper<PushTargetSystem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(PushTargetSystem::getEnabled, 1)
                .eq(PushTargetSystem::getStatus, 1)
                .orderByAsc(PushTargetSystem::getSortOrder);
        return this.list(queryWrapper);
    }

    @Override
    public PushTargetSystem getBySystemCode(String systemCode) {
        QueryWrapper<PushTargetSystem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PushTargetSystem::getSystemCode, systemCode);
        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEnabled(String id, Integer enabled) {
        PushTargetSystem pushTargetSystem = this.getById(id);
        if (ObjectUtil.isNull(pushTargetSystem)) {
            throw new ServiceException(PushTargetSystemExceptionEnum.NOT_EXIST);
        }
        pushTargetSystem.setEnabled(enabled);
        this.updateById(pushTargetSystem);
    }

    /**
     * 获取推送目标系统表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    private PushTargetSystem queryPushTargetSystem(PushTargetSystemParam pushTargetSystemParam) {
        PushTargetSystem pushTargetSystem = this.getById(pushTargetSystemParam.getId());
        if (ObjectUtil.isNull(pushTargetSystem)) {
            throw new ServiceException(PushTargetSystemExceptionEnum.NOT_EXIST);
        }
        return pushTargetSystem;
    }
}
