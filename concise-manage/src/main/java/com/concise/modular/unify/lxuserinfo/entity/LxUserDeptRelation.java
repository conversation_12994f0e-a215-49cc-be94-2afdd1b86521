package com.concise.modular.unify.lxuserinfo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 浙政钉用户机构关系表
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("lx_user_dept_relation")
public class LxUserDeptRelation extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID（浙政钉employeeCode）
     */
    @Excel(name = "用户ID")
    private String userId;

    /**
     * 用户关联的部门ID
     */
    @Excel(name = "部门ID")
    private String userDeptId;

    /**
     * 用户关联的部门名称
     */
    @Excel(name = "部门名称")
    private String userDeptName;

    /**
     * 职务
     */
    @Excel(name = "职务")
    private String position;

    /**
     * 职级
     */
    @Excel(name = "职级")
    private String rank;

    /**
     * 办公地址
     */
    @Excel(name = "办公地址")
    private String workAddress;

    /**
     * 办公电话
     */
    @Excel(name = "办公电话")
    private String workPhone;

    /**
     * 矫正机构id
     */
    @Excel(name = "矫正机构ID")
    private String jzjg;

    /**
     * 矫正机构名称
     */
    @Excel(name = "矫正机构名称")
    private String jzjgName;

    /**
     * 是否主要部门（0:否 1:是）
     */
    @Excel(name = "是否主要部门")
    private Integer isMain;

    /**
     * 排序顺序
     */
    @Excel(name = "排序顺序")
    private Integer sortOrder;

    /**
     * 状态（0:无效 1:有效）
     */
    @Excel(name = "状态")
    private Integer status;

}
