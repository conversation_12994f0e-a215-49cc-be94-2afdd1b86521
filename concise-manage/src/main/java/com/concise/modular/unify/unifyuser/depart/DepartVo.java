package com.concise.modular.unify.unifyuser.depart;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class DepartVo {

    private List<String> childrenOuUuid;

    private String description;
    /**
     * 扩展字典,attributes 为系统定义扩展字段
     *
     */
    private ExtendField extendField;
    /**
     * 组织机构的管理者,value 是管理者账户的外部 ID,displayName 是用户名,管理者可为空
     */
    private List<Manager> manager;
    /**
     * 组织机构的名称
     */
    private String organization;
    /**
     * 本组织机构的 uuid 或外部 ID
     */
    @NotBlank(message = "organizationUuid不可为空")
    private String organizationUuid;
    /**
     * 所属父级组织机构的 uuid 或外部ID
     *
     */
    private String parentUuid;
    /**
     * 组织机构所属的区域 id,type 为SELF_OU(自建组织机构)时有可能会有值,可为空,type为DEPARTMENT("自建部门")不会出现值
     *
     */
    private String regionId;
    /**
     * 是否是根节点
     */
    private String rootNode;
    /**
     * SELF_OU(自建组织机构)或
     * DEPARTMENT("自建部门")
     */
    private String type;
    /**
     * 部门排序号
     */
    private String levelNumber;
    /**
     * 矫正机构id
     */
    private String correctOrgId;
    /**
     * 矫正机构名称
     */
    private String correctOrgName;
}

