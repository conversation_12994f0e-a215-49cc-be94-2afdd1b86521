package com.concise.modular.unify.lxuserinfo.controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.modular.unify.lxuserinfo.param.LxUserInfoParam;
import com.concise.modular.unify.lxuserinfo.service.LxUserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;

/**
 * 浙政钉用户信息表控制器
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Api(tags = "浙政钉用户信息表")
@RestController
public class LxUserInfoController {

    @Resource
    private LxUserInfoService lxUserInfoService;

    /**
     * 查询浙政钉用户信息表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @GetMapping("/lxUserInfo/page")
    @ApiOperation("浙政钉用户信息表_分页查询")
    @BusinessLog(title = "浙政钉用户信息表_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(LxUserInfoParam lxUserInfoParam) {
        return new SuccessResponseData(lxUserInfoService.page(lxUserInfoParam));
    }

    /**
     * 添加浙政钉用户信息表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @PostMapping("/lxUserInfo/add")
    @ApiOperation("浙政钉用户信息表_增加")
    @BusinessLog(title = "浙政钉用户信息表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(LxUserInfoParam.add.class) LxUserInfoParam lxUserInfoParam) {
        lxUserInfoService.add(lxUserInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 删除浙政钉用户信息表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @PostMapping("/lxUserInfo/delete")
    @ApiOperation("浙政钉用户信息表_删除")
    @BusinessLog(title = "浙政钉用户信息表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(LxUserInfoParam.delete.class) LxUserInfoParam lxUserInfoParam) {
        lxUserInfoService.delete(lxUserInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑浙政钉用户信息表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @PostMapping("/lxUserInfo/edit")
    @ApiOperation("浙政钉用户信息表_编辑")
    @BusinessLog(title = "浙政钉用户信息表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(LxUserInfoParam.edit.class) LxUserInfoParam lxUserInfoParam) {
        lxUserInfoService.edit(lxUserInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 查看浙政钉用户信息表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @GetMapping("/lxUserInfo/detail")
    @ApiOperation("浙政钉用户信息表_查看")
    @BusinessLog(title = "浙政钉用户信息表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(LxUserInfoParam.detail.class) LxUserInfoParam lxUserInfoParam) {
        return new SuccessResponseData(lxUserInfoService.detail(lxUserInfoParam));
    }

    /**
     * 浙政钉用户信息表列表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @GetMapping("/lxUserInfo/list")
    @ApiOperation("浙政钉用户信息表_列表")
    @BusinessLog(title = "浙政钉用户信息表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(LxUserInfoParam lxUserInfoParam) {
        return new SuccessResponseData(lxUserInfoService.list(lxUserInfoParam));
    }

    /**
     * 处理浙政钉用户信息
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @PostMapping("/lxUserInfo/process")
    @ApiOperation("浙政钉用户信息表_处理")
    @BusinessLog(title = "浙政钉用户信息表_处理", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData process(@RequestParam String id) {
        lxUserInfoService.processLxUserInfo(id);
        return new SuccessResponseData();
    }

}
