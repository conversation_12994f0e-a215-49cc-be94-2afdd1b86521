package com.concise.modular.unify.pushtargetsystem.controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.modular.unify.pushtargetsystem.param.PushTargetSystemParam;
import com.concise.modular.unify.pushtargetsystem.service.PushTargetSystemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;

/**
 * 推送目标系统表控制器
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Api(tags = "推送目标系统管理")
@RestController
public class PushTargetSystemController {

    @Resource
    private PushTargetSystemService pushTargetSystemService;

    /**
     * 查询推送目标系统表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @GetMapping("/pushTargetSystem/page")
    @ApiOperation("推送目标系统表_分页查询")
    @BusinessLog(title = "推送目标系统表_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(PushTargetSystemParam pushTargetSystemParam) {
        return new SuccessResponseData(pushTargetSystemService.page(pushTargetSystemParam));
    }

    /**
     * 添加推送目标系统表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @PostMapping("/pushTargetSystem/add")
    @ApiOperation("推送目标系统表_增加")
    @BusinessLog(title = "推送目标系统表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(PushTargetSystemParam.add.class) PushTargetSystemParam pushTargetSystemParam) {
        pushTargetSystemService.add(pushTargetSystemParam);
        return new SuccessResponseData();
    }

    /**
     * 删除推送目标系统表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @PostMapping("/pushTargetSystem/delete")
    @ApiOperation("推送目标系统表_删除")
    @BusinessLog(title = "推送目标系统表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(PushTargetSystemParam.delete.class) PushTargetSystemParam pushTargetSystemParam) {
        pushTargetSystemService.delete(pushTargetSystemParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑推送目标系统表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @PostMapping("/pushTargetSystem/edit")
    @ApiOperation("推送目标系统表_编辑")
    @BusinessLog(title = "推送目标系统表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(PushTargetSystemParam.edit.class) PushTargetSystemParam pushTargetSystemParam) {
        pushTargetSystemService.edit(pushTargetSystemParam);
        return new SuccessResponseData();
    }

    /**
     * 查看推送目标系统表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @GetMapping("/pushTargetSystem/detail")
    @ApiOperation("推送目标系统表_查看")
    @BusinessLog(title = "推送目标系统表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(PushTargetSystemParam.detail.class) PushTargetSystemParam pushTargetSystemParam) {
        return new SuccessResponseData(pushTargetSystemService.detail(pushTargetSystemParam));
    }

    /**
     * 推送目标系统表列表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @GetMapping("/pushTargetSystem/list")
    @ApiOperation("推送目标系统表_列表")
    @BusinessLog(title = "推送目标系统表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(PushTargetSystemParam pushTargetSystemParam) {
        return new SuccessResponseData(pushTargetSystemService.list(pushTargetSystemParam));
    }

    /**
     * 获取所有启用的目标系统
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @GetMapping("/pushTargetSystem/enabled")
    @ApiOperation("推送目标系统表_获取启用系统")
    @BusinessLog(title = "推送目标系统表_获取启用系统", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getEnabledSystems() {
        return new SuccessResponseData(pushTargetSystemService.getEnabledSystems());
    }

    /**
     * 启用/禁用目标系统
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    @Permission
    @PostMapping("/pushTargetSystem/updateEnabled")
    @ApiOperation("推送目标系统表_启用禁用")
    @BusinessLog(title = "推送目标系统表_启用禁用", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData updateEnabled(@RequestParam String id, @RequestParam Integer enabled) {
        pushTargetSystemService.updateEnabled(id, enabled);
        return new SuccessResponseData();
    }

}
