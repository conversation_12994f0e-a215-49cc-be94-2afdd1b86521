package com.concise.modular.unify.lxuserinfo.service.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.unify.lxuserinfo.entity.LxUserDeptRelation;
import com.concise.modular.unify.lxuserinfo.entity.LxUserInfo;
import com.concise.modular.unify.lxuserinfo.enums.LxUserInfoExceptionEnum;
import com.concise.modular.unify.lxuserinfo.mapper.LxUserDeptRelationMapper;
import com.concise.modular.unify.lxuserinfo.mapper.LxUserInfoMapper;
import com.concise.modular.unify.lxuserinfo.param.LxUserInfoParam;
import com.concise.modular.unify.lxuserinfo.service.LxUserInfoService;
import com.concise.modular.unify.unifyuser.entity.LxExtendInfosVo;
import com.concise.modular.unify.unifyuser.entity.LxUserVo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * 浙政钉用户信息表service接口实现类
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Service
public class LxUserInfoServiceImpl extends ServiceImpl<LxUserInfoMapper, LxUserInfo> implements LxUserInfoService {

    @Resource
    private LxUserDeptRelationMapper lxUserDeptRelationMapper;

    @Override
    public PageResult<LxUserInfo> page(LxUserInfoParam lxUserInfoParam) {
        QueryWrapper<LxUserInfo> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(lxUserInfoParam)) {
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(lxUserInfoParam.getName())) {
                queryWrapper.lambda().like(LxUserInfo::getName, lxUserInfoParam.getName());
            }
            // 根据用户名 查询
            if (ObjectUtil.isNotEmpty(lxUserInfoParam.getUserName())) {
                queryWrapper.lambda().like(LxUserInfo::getUserName, lxUserInfoParam.getUserName());
            }
            // 根据处理状态 查询
            if (ObjectUtil.isNotEmpty(lxUserInfoParam.getProcessStatus())) {
                queryWrapper.lambda().eq(LxUserInfo::getProcessStatus, lxUserInfoParam.getProcessStatus());
            }
            // 根据操作类型 查询
            if (ObjectUtil.isNotEmpty(lxUserInfoParam.getOperatorType())) {
                queryWrapper.lambda().eq(LxUserInfo::getOperatorType, lxUserInfoParam.getOperatorType());
            }
        }
        queryWrapper.lambda().orderByDesc(LxUserInfo::getReceiveTime);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<LxUserInfo> list(LxUserInfoParam lxUserInfoParam) {
        return this.list();
    }

    @Override
    public void add(LxUserInfoParam lxUserInfoParam) {
        LxUserInfo lxUserInfo = new LxUserInfo();
        BeanUtil.copyProperties(lxUserInfoParam, lxUserInfo);
        this.save(lxUserInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(LxUserInfoParam lxUserInfoParam) {
        this.removeById(lxUserInfoParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(LxUserInfoParam lxUserInfoParam) {
        LxUserInfo lxUserInfo = this.queryLxUserInfo(lxUserInfoParam);
        BeanUtil.copyProperties(lxUserInfoParam, lxUserInfo);
        this.updateById(lxUserInfo);
    }

    @Override
    public LxUserInfo detail(LxUserInfoParam lxUserInfoParam) {
        return this.queryLxUserInfo(lxUserInfoParam);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLxUserInfo(LxUserVo lxUserVo) {
        LxUserInfo lxUserInfo = new LxUserInfo();
        lxUserInfo.setId(lxUserVo.getId());
        lxUserInfo.setName(lxUserVo.getName());
        lxUserInfo.setUserName(lxUserVo.getUserName());
        lxUserInfo.setSex(lxUserVo.getSex());
        lxUserInfo.setAccountId(lxUserVo.getAccountId());
        lxUserInfo.setDeptId(lxUserVo.getDeptId());
        lxUserInfo.setOperatorType(lxUserVo.getOperatorType());
        
        // 处理扩展信息
        List<LxExtendInfosVo> extendInfos = lxUserVo.getExtendInfos();
        if (ObjectUtil.isNotEmpty(extendInfos) && !extendInfos.isEmpty()) {
            LxExtendInfosVo firstExtendInfo = extendInfos.get(0);
            lxUserInfo.setMainPosition(firstExtendInfo.getPosition());
            lxUserInfo.setMainRank(firstExtendInfo.getRank());
            lxUserInfo.setMainDeptId(firstExtendInfo.getUserDeptId());
            lxUserInfo.setMainDeptName(firstExtendInfo.getUserDeptName());
            lxUserInfo.setMainWorkAddress(firstExtendInfo.getWorkAddress());
            lxUserInfo.setMainWorkPhone(firstExtendInfo.getWorkPhone());
            lxUserInfo.setMainJzjg(firstExtendInfo.getJzjg());
            lxUserInfo.setMainJzjgName(firstExtendInfo.getJzjgName());

            // 保存扩展信息JSON
            lxUserInfo.setExtendInfosJson(JSON.toJSONString(extendInfos));
        }
        
        // 保存原始数据JSON
        lxUserInfo.setRawDataJson(JSON.toJSONString(lxUserVo));
        lxUserInfo.setReceiveTime(new Date());
        lxUserInfo.setProcessStatus(0); // 未处理

        this.saveOrUpdate(lxUserInfo);

        // 保存用户机构关系
        if (ObjectUtil.isNotEmpty(extendInfos)) {
            saveUserDeptRelations(lxUserVo.getId(), extendInfos);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processLxUserInfo(String id) {
        LxUserInfo lxUserInfo = this.getById(id);
        if (ObjectUtil.isNull(lxUserInfo)) {
            throw new ServiceException(LxUserInfoExceptionEnum.NOT_EXIST);
        }
        
        try {
            // 这里可以添加具体的处理逻辑
            // 比如同步到系统用户表等
            
            lxUserInfo.setProcessStatus(1); // 已处理
            lxUserInfo.setProcessTime(new Date());
            lxUserInfo.setProcessMessage("处理成功");
            this.updateById(lxUserInfo);
        } catch (Exception e) {
            lxUserInfo.setProcessStatus(2); // 处理失败
            lxUserInfo.setProcessTime(new Date());
            lxUserInfo.setProcessMessage("处理失败：" + e.getMessage());
            this.updateById(lxUserInfo);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUserDeptRelations(String userId, List<LxExtendInfosVo> extendInfos) {
        // 先删除该用户的所有机构关系
        QueryWrapper<LxUserDeptRelation> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.lambda().eq(LxUserDeptRelation::getUserId, userId);
        lxUserDeptRelationMapper.delete(deleteWrapper);

        // 保存新的机构关系
        for (int i = 0; i < extendInfos.size(); i++) {
            LxExtendInfosVo extendInfo = extendInfos.get(i);
            LxUserDeptRelation relation = new LxUserDeptRelation();
            relation.setId(IdUtil.simpleUUID());
            relation.setUserId(userId);
            relation.setUserDeptId(extendInfo.getUserDeptId());
            relation.setUserDeptName(extendInfo.getUserDeptName());
            relation.setPosition(extendInfo.getPosition());
            relation.setRank(extendInfo.getRank());
            relation.setWorkAddress(extendInfo.getWorkAddress());
            relation.setWorkPhone(extendInfo.getWorkPhone());
            relation.setJzjg(extendInfo.getJzjg());
            relation.setJzjgName(extendInfo.getJzjgName());
            relation.setIsMain(i == 0 ? 1 : 0); // 第一个为主要部门
            relation.setSortOrder(i);
            relation.setStatus(1); // 有效

            lxUserDeptRelationMapper.insert(relation);
        }
    }

    /**
     * 获取浙政钉用户信息表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    private LxUserInfo queryLxUserInfo(LxUserInfoParam lxUserInfoParam) {
        LxUserInfo lxUserInfo = this.getById(lxUserInfoParam.getId());
        if (ObjectUtil.isNull(lxUserInfo)) {
            throw new ServiceException(LxUserInfoExceptionEnum.NOT_EXIST);
        }
        return lxUserInfo;
    }
}
