package com.concise.modular.unify.pushtargetsystem.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.unify.pushtargetsystem.entity.PushTargetSystem;
import com.concise.modular.unify.pushtargetsystem.param.PushTargetSystemParam;
import java.util.List;

/**
 * 推送目标系统表service接口
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface PushTargetSystemService extends IService<PushTargetSystem> {

    /**
     * 查询推送目标系统表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    PageResult<PushTargetSystem> page(PushTargetSystemParam pushTargetSystemParam);

    /**
     * 推送目标系统表列表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    List<PushTargetSystem> list(PushTargetSystemParam pushTargetSystemParam);

    /**
     * 添加推送目标系统表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    void add(PushTargetSystemParam pushTargetSystemParam);

    /**
     * 删除推送目标系统表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    void delete(PushTargetSystemParam pushTargetSystemParam);

    /**
     * 编辑推送目标系统表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    void edit(PushTargetSystemParam pushTargetSystemParam);

    /**
     * 查看推送目标系统表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    PushTargetSystem detail(PushTargetSystemParam pushTargetSystemParam);

    /**
     * 获取所有启用的目标系统
     *
     * @return 启用的目标系统列表
     * <AUTHOR>
     * @date 2024-12-20
     */
    List<PushTargetSystem> getEnabledSystems();

    /**
     * 根据系统编码获取目标系统
     *
     * @param systemCode 系统编码
     * @return 目标系统
     * <AUTHOR>
     * @date 2024-12-20
     */
    PushTargetSystem getBySystemCode(String systemCode);

    /**
     * 启用/禁用目标系统
     *
     * @param id 系统ID
     * @param enabled 是否启用
     * <AUTHOR>
     * @date 2024-12-20
     */
    void updateEnabled(String id, Integer enabled);
}
