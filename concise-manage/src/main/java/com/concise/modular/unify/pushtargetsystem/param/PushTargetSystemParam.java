package com.concise.modular.unify.pushtargetsystem.param;

import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 推送目标系统表参数类
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Data
public class PushTargetSystemParam {

    /**
     * 主键ID
     */
    @NotBlank(message = "主键ID不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 系统名称
     */
    @NotBlank(message = "系统名称不能为空，请检查systemName参数", groups = {add.class, edit.class})
    private String systemName;

    /**
     * 系统编码（唯一）
     */
    @NotBlank(message = "系统编码不能为空，请检查systemCode参数", groups = {add.class, edit.class})
    private String systemCode;

    /**
     * 目标接口地址
     */
    @NotBlank(message = "目标接口地址不能为空，请检查targetUrl参数", groups = {add.class, edit.class})
    private String targetUrl;

    /**
     * 系统描述
     */
    private String description;

    /**
     * 是否启用（0:禁用 1:启用）
     */
    private Integer enabled;

    /**
     * 超时时间（毫秒）
     */
    private Integer timeout;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 重试延迟时间（分钟），逗号分隔
     */
    private String retryDelayMinutes;

    /**
     * 认证类型（NONE:无认证 BASIC:基础认证 TOKEN:令牌认证）
     */
    private String authType;

    /**
     * 认证配置（JSON格式）
     */
    private String authConfig;

    /**
     * 请求头配置（JSON格式）
     */
    private String headers;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 状态（0:无效 1:有效）
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 参数校验分组：增加
     */
    public @interface add {
    }

    /**
     * 参数校验分组：编辑
     */
    public @interface edit {
    }

    /**
     * 参数校验分组：删除
     */
    public @interface delete {
    }

    /**
     * 参数校验分组：详情
     */
    public @interface detail {
    }

}
