package com.concise.modular.unify.pushrecord.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.modular.unify.pushrecord.entity.PushRecord;
import com.concise.modular.unify.pushrecord.enums.PushRecordExceptionEnum;
import com.concise.modular.unify.pushrecord.mapper.PushRecordMapper;
import com.concise.modular.unify.pushrecord.param.PushRecordParam;
import com.concise.modular.unify.pushrecord.service.PushRecordService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * 推送记录表service接口实现类
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Service
public class PushRecordServiceImpl extends ServiceImpl<PushRecordMapper, PushRecord> implements PushRecordService {

    @Override
    public PageResult<PushRecord> page(PushRecordParam pushRecordParam) {
        QueryWrapper<PushRecord> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(pushRecordParam)) {
            // 根据源数据表名 查询
            if (ObjectUtil.isNotEmpty(pushRecordParam.getSourceTable())) {
                queryWrapper.lambda().eq(PushRecord::getSourceTable, pushRecordParam.getSourceTable());
            }
            // 根据源数据ID 查询
            if (ObjectUtil.isNotEmpty(pushRecordParam.getSourceId())) {
                queryWrapper.lambda().eq(PushRecord::getSourceId, pushRecordParam.getSourceId());
            }
            // 根据目标系统名称 查询
            if (ObjectUtil.isNotEmpty(pushRecordParam.getTargetSystem())) {
                queryWrapper.lambda().eq(PushRecord::getTargetSystem, pushRecordParam.getTargetSystem());
            }
            // 根据推送状态 查询
            if (ObjectUtil.isNotEmpty(pushRecordParam.getPushStatus())) {
                queryWrapper.lambda().eq(PushRecord::getPushStatus, pushRecordParam.getPushStatus());
            }
            // 根据推送类型 查询
            if (ObjectUtil.isNotEmpty(pushRecordParam.getPushType())) {
                queryWrapper.lambda().eq(PushRecord::getPushType, pushRecordParam.getPushType());
            }
        }
        queryWrapper.lambda().orderByDesc(PushRecord::getCreateTime);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<PushRecord> list(PushRecordParam pushRecordParam) {
        return this.list();
    }

    @Override
    public void add(PushRecordParam pushRecordParam) {
        PushRecord pushRecord = new PushRecord();
        BeanUtil.copyProperties(pushRecordParam, pushRecord);
        pushRecord.setId(IdUtil.simpleUUID());
        pushRecord.setPushStatus(0); // 待推送
        pushRecord.setRetryCount(0);
        pushRecord.setMaxRetryCount(3);
        this.save(pushRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(PushRecordParam pushRecordParam) {
        this.removeById(pushRecordParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(PushRecordParam pushRecordParam) {
        PushRecord pushRecord = this.queryPushRecord(pushRecordParam);
        BeanUtil.copyProperties(pushRecordParam, pushRecord);
        this.updateById(pushRecord);
    }

    @Override
    public PushRecord detail(PushRecordParam pushRecordParam) {
        return this.queryPushRecord(pushRecordParam);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPushRecord(String sourceTable, String sourceId, String targetSystem, 
                                  String targetUrl, String pushType, String pushData) {
        PushRecord pushRecord = new PushRecord();
        pushRecord.setId(IdUtil.simpleUUID());
        pushRecord.setSourceTable(sourceTable);
        pushRecord.setSourceId(sourceId);
        pushRecord.setTargetSystem(targetSystem);
        pushRecord.setTargetUrl(targetUrl);
        pushRecord.setPushType(pushType);
        pushRecord.setPushData(pushData);
        pushRecord.setPushStatus(0); // 待推送
        pushRecord.setRetryCount(0);
        pushRecord.setMaxRetryCount(3);
        
        try {
            pushRecord.setCreateUserId(LoginContextHolder.me().getSysLoginUser().getId());
        } catch (Exception e) {
            pushRecord.setCreateUserId("system");
        }
        
        this.save(pushRecord);
        return pushRecord.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePushStatus(String id, Integer pushStatus, String responseCode, String responseMessage) {
        PushRecord pushRecord = this.getById(id);
        if (ObjectUtil.isNull(pushRecord)) {
            throw new ServiceException(PushRecordExceptionEnum.NOT_EXIST);
        }
        
        pushRecord.setPushStatus(pushStatus);
        pushRecord.setResponseCode(responseCode);
        pushRecord.setResponseMessage(responseMessage);
        pushRecord.setPushTime(new Date());
        
        this.updateById(pushRecord);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void retryPush(String id) {
        PushRecord pushRecord = this.getById(id);
        if (ObjectUtil.isNull(pushRecord)) {
            throw new ServiceException(PushRecordExceptionEnum.NOT_EXIST);
        }
        
        if (pushRecord.getRetryCount() >= pushRecord.getMaxRetryCount()) {
            throw new ServiceException(PushRecordExceptionEnum.RETRY_LIMIT_EXCEEDED);
        }
        
        pushRecord.setRetryCount(pushRecord.getRetryCount() + 1);
        pushRecord.setPushStatus(0); // 重置为待推送
        pushRecord.setNextRetryTime(null);
        
        this.updateById(pushRecord);
    }

    @Override
    public List<PushRecord> getPendingPushRecords() {
        QueryWrapper<PushRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(PushRecord::getPushStatus, 0) // 待推送
                .and(wrapper -> wrapper
                        .isNull(PushRecord::getNextRetryTime)
                        .or()
                        .le(PushRecord::getNextRetryTime, new Date())
                )
                .orderByAsc(PushRecord::getCreateTime);
        
        return this.list(queryWrapper);
    }

    /**
     * 获取推送记录表
     *
     * <AUTHOR>
     * @date 2024-12-20
     */
    private PushRecord queryPushRecord(PushRecordParam pushRecordParam) {
        PushRecord pushRecord = this.getById(pushRecordParam.getId());
        if (ObjectUtil.isNull(pushRecord)) {
            throw new ServiceException(PushRecordExceptionEnum.NOT_EXIST);
        }
        return pushRecord;
    }
}
