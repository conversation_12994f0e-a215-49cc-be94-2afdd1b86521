package com.concise.modular.unify.lxuserinfo.param;

import java.util.Date;

import javax.validation.constraints.NotBlank;

import lombok.Data;

/**
 * 浙政钉用户信息表参数类
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Data
public class LxUserInfoParam {

    /**
     * 用户id（浙政钉employeeCode）
     */
    @NotBlank(message = "用户ID不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查name参数", groups = {add.class, edit.class})
    private String name;

    /**
     * 用户的显示名称,唯一,支持中文
     */
    @NotBlank(message = "用户名不能为空，请检查userName参数", groups = {add.class, edit.class})
    private String userName;

    /**
     * 性别（0:未知 1:男 2:女 9：未说明的性别）
     */
    private Integer sex;

    /**
     * 浙政钉accountId
     */
    private String accountId;

    /**
     * 机构id(一个或多个)多个用英文逗号隔开
     */
    private String deptId;

    /**
     * 动作类型 0:新增或修改 1:新增或修改 2:删除
     */
    private Integer operatorType;

    /**
     * 职务
     */
    private String position;

    /**
     * 职级
     */
    private String rank;

    /**
     * 用户关联的部门ID
     */
    private String userDeptId;

    /**
     * 用户关联的部门名称
     */
    private String userDeptName;

    /**
     * 办公地址
     */
    private String workAddress;

    /**
     * 办公电话
     */
    private String workPhone;

    /**
     * 矫正机构id
     */
    private String jzjg;

    /**
     * 矫正机构名称
     */
    private String jzjgName;

    /**
     * 扩展信息JSON字符串
     */
    private String extendInfosJson;

    /**
     * 原始推送数据JSON字符串
     */
    private String rawDataJson;

    /**
     * 接收时间
     */
    private Date receiveTime;

    /**
     * 处理状态（0:未处理 1:已处理 2:处理失败）
     */
    private Integer processStatus;

    /**
     * 处理时间
     */
    private Date processTime;

    /**
     * 处理消息
     */
    private String processMessage;

    /**
     * 参数校验分组：增加
     */
    public @interface add {
    }

    /**
     * 参数校验分组：编辑
     */
    public @interface edit {
    }

    /**
     * 参数校验分组：删除
     */
    public @interface delete {
    }

    /**
     * 参数校验分组：详情
     */
    public @interface detail {
    }

}
