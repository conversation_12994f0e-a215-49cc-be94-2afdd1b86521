
package com.concise.modular.unify.unifyuser.param;


import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 浙政钉用户表参数类
 *
 * <AUTHOR>
 * @date 2022-04-14 14:13:15
 */
@Data
public class UnifyUserParam extends BaseParam {

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 登录账号
     */
    @NotBlank(message = "登录账号不能为空，请检查userName参数", groups = {add.class, edit.class})
    private String userName;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空，请检查password参数", groups = {add.class, edit.class})
    private String password;

    /**
     * 用户的显示名称
     */
    @NotBlank(message = "用户的显示名称不能为空，请检查displayName参数", groups = {add.class, edit.class})
    private String displayName;

    /**
     * 电子邮件
     */
    @NotBlank(message = "电子邮件不能为空，请检查email参数", groups = {add.class, edit.class})
    private String email;

    /**
     * 电话
     */
    @NotBlank(message = "电话不能为空，请检查phoneNumbers参数", groups = {add.class, edit.class})
    private String phoneNumbers;

    /**
     * 外部 ID,唯一,
     */
    @NotNull(message = "外部 ID,唯一,不能为空，请检查externalId参数", groups = {edit.class, delete.class, detail.class})
    private String externalId;

    /**
     * 为账户指定组织单位
     */
    @NotBlank(message = "为账户指定组织单位不能为空，请检查belongs参数", groups = {add.class, edit.class})
    private String belongs;

    /**
     * 是否禁用账户，ture 禁用账户,false 启用账
     */
    @NotBlank(message = "是否禁用账户，ture 禁用账户,false 启用账不能为空，请检查locked参数", groups = {add.class, edit.class})
    private String locked;

    /**
     * 扩展字段,attributes 为系统定义扩展字段
     */
    @NotBlank(message = "扩展字段,attributes 为系统定义扩展字段不能为空，请检查extendfield参数", groups = {add.class, edit.class})
    private String extendfield;

    /**
     * employee码
     */
    @NotBlank(message = "employee码不能为空，请检查moziDeptCode参数", groups = {add.class, edit.class})
    private String moziDeptCode;

    /**
     * json字符串，完整保存
     */
    @NotNull(message = "json字符串，完整保存不能为空，请检查jsonString参数", groups = {add.class, edit.class})
    private String jsonString;

    /**
     * 删除时间
     */
    @NotNull(message = "删除时间不能为空，请检查deleteTime参数", groups = {add.class, edit.class})
    private String deleteTime;

    /**
     * 删除标记
     */
    @NotBlank(message = "删除标记不能为空，请检查deleteFlag参数", groups = {add.class, edit.class})
    private String deleteFlag;

    /**
     * 矫正机构id
     */
    @NotBlank(message = "矫正机构id不能为空，请检查correctOrgId参数", groups = {add.class, edit.class})
    private String correctOrgId;

    /**
     * 矫正机构名称
     */
    @NotBlank(message = "矫正机构名称不能为空，请检查correctOrgName参数", groups = {add.class, edit.class})
    private String correctOrgName;

}
