package com.concise.modular.unify.unifyuser.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.unify.unifyuser.entity.UnifyUser;
import com.concise.modular.unify.unifyuser.param.UnifyUserParam;


import java.util.List;

/**
 * 浙政钉用户表service接口
 *
 * <AUTHOR>
 * @date 2022-04-14 14:13:15
 */
public interface UnifyUserService extends IService<UnifyUser> {

    /**
     * 查询浙政钉用户表
     *
     * <AUTHOR>
     * @date 2022-04-14 14:13:15
     */
    PageResult<UnifyUser> page(UnifyUserParam unifyUserParam);

    /**
     * 浙政钉用户表列表
     *
     * <AUTHOR>
     * @date 2022-04-14 14:13:15
     */
    List<UnifyUser> list(UnifyUserParam unifyUserParam);

    /**
     * 添加浙政钉用户表
     *
     * <AUTHOR>
     * @date 2022-04-14 14:13:15
     */
    void add(UnifyUserParam unifyUserParam);

    /**
     * 删除浙政钉用户表
     *
     * <AUTHOR>
     * @date 2022-04-14 14:13:15
     */
    void delete(List<UnifyUserParam> unifyUserParamList);

    /**
     * 编辑浙政钉用户表
     *
     * <AUTHOR>
     * @date 2022-04-14 14:13:15
     */
    void edit(UnifyUserParam unifyUserParam);

    /**
     * 查看浙政钉用户表
     *
     * <AUTHOR>
     * @date 2022-04-14 14:13:15
     */
    UnifyUser detail(UnifyUserParam unifyUserParam);

    /**
     * 导出浙政钉用户表
     *
     * <AUTHOR>
     * @date 2022-04-14 14:13:15
     */
    void export(UnifyUserParam unifyUserParam);

    /**
     * 同步浙政钉用户到系统用户表
     */
    void updateUserToSystemUser();

    /**
     * 更新用户部门到sys_emp
     */
    void updateUserDepartToSysEmp();
}
