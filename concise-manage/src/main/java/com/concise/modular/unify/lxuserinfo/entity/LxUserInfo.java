package com.concise.modular.unify.lxuserinfo.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.pojo.base.entity.BaseEntity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 浙政钉用户信息表
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("lx_user_info")
public class LxUserInfo extends BaseEntity {

    /**
     * 用户id（浙政钉employeeCode）
     */
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 用户的显示名称,唯一,支持中文
     */
    @Excel(name = "用户名")
    private String userName;

    /**
     * 性别（0:未知 1:男 2:女 9：未说明的性别）
     */
    @Excel(name = "性别")
    private Integer sex;

    /**
     * 浙政钉accountId
     */
    @Excel(name = "账户ID")
    private String accountId;

    /**
     * 机构id(一个或多个)多个用英文逗号隔开
     */
    @Excel(name = "部门ID")
    private String deptId;

    /**
     * 动作类型 0:新增或修改 1:新增或修改 2:删除
     */
    @Excel(name = "操作类型")
    private Integer operatorType;

    /**
     * 主要部门ID（第一个部门）
     */
    @Excel(name = "主要部门ID")
    private String mainDeptId;

    /**
     * 主要部门名称（第一个部门）
     */
    @Excel(name = "主要部门名称")
    private String mainDeptName;

    /**
     * 主要职务
     */
    @Excel(name = "主要职务")
    private String mainPosition;

    /**
     * 主要职级
     */
    @Excel(name = "主要职级")
    private String mainRank;

    /**
     * 主要办公地址
     */
    @Excel(name = "主要办公地址")
    private String mainWorkAddress;

    /**
     * 主要办公电话
     */
    @Excel(name = "主要办公电话")
    private String mainWorkPhone;

    /**
     * 主要矫正机构id
     */
    @Excel(name = "主要矫正机构ID")
    private String mainJzjg;

    /**
     * 主要矫正机构名称
     */
    @Excel(name = "主要矫正机构名称")
    private String mainJzjgName;

    /**
     * 扩展信息JSON字符串
     */
    private String extendInfosJson;

    /**
     * 原始推送数据JSON字符串
     */
    private String rawDataJson;

    /**
     * 接收时间
     */
    @Excel(name = "接收时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss", width = 20)
    private Date receiveTime;

    /**
     * 处理状态（0:未处理 1:已处理 2:处理失败）
     */
    @Excel(name = "处理状态")
    private Integer processStatus;

    /**
     * 处理时间
     */
    @Excel(name = "处理时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss", width = 20)
    private Date processTime;

    /**
     * 处理消息
     */
    @Excel(name = "处理消息")
    private String processMessage;

}
