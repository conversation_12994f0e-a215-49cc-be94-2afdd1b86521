package com.concise.modular.zwdd;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient;
import com.alibaba.xxpt.gateway.shared.client.http.GetClient;
import com.alibaba.xxpt.gateway.shared.client.http.PostClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/7/3
 */
@Slf4j
@Component
public class ZwddQrCodeUtil {
    private static final ExecutableClient executableClient = ExecutableClient.getInstance();
    private static final String protocal = "https";


    public static void init() {
        executableClient.setDomainName(ZwddProperties.getDomain_name());
        executableClient.setProtocal(protocal);
        //应用App Key
        executableClient.setAccessKey(ZwddProperties.getKey());
        //应用App Secret
        executableClient.setSecretKey(ZwddProperties.getSecret());
        executableClient.init();
    }

    public static String getAccessToken() {
        init();
        //executableClient要单例，并且使用前要初始化，只需要初始化一次
        String api = "/gettoken.json";
        GetClient getClient = executableClient.newGetClient(api);
        //设置参数 专有钉钉
        getClient.addParameter("appkey", ZwddProperties.getKey());
        getClient.addParameter("appsecret", ZwddProperties.getSecret());
        //调用API
        String apiResult = getClient.get();

        log.info("返回token信息+" + apiResult);
        if (StringUtils.isEmpty(apiResult)) {
            throw new RuntimeException("获取access_token失败");
        } else {
            JSONObject res = JSONObject.parseObject(apiResult);
            JSONObject content = res.getJSONObject("content");
            System.out.println("content:" + content);
            JSONObject data = content.getJSONObject("data");
            System.out.println("data" + data);
            String accessToken = data.getString("accessToken");
            System.out.println("accessToken=" + accessToken);
            return accessToken;
        }
    }

    public static JSONObject getUserInfoByQrCode(String accessToken, String code) {
        init();
        String api = "/rpc/oauth2/getuserinfo_bycode.json";
        PostClient postClient = executableClient.newPostClient(api);
        //设置参数
        postClient.addParameter("access_token", accessToken);
        postClient.addParameter("code", code);
        //调用API
        String apiResult = postClient.post();
        log.info("返回apiResult{}", apiResult);
        return JSONObject.parseObject(apiResult);
    }

    public static void getkey(){
        System.out.println(ZwddProperties.getMark());
    }



    public static void main(String[] args) {
        //余 77620767
        //余 GE_49f20bbf8125444cbb1e3fefedd5a093
//        System.out.println(getUserInfoByQrCode(getAccessToken(), "eb113553f21749c3b39297b04a99d404a0661f01"));
        getkey();
    }
}
