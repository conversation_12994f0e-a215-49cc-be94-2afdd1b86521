package com.concise.modular.zwdd;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
@Data
@Component
public class ZwddProperties {

    /**
     * 应用key
     */
    @Value("${zzd.qrcode.appkey}")
    private String appkey;

    /**
     * 应用秘钥
     */
    @Value("${zzd.qrcode.appsecret}")
    private String appsecret;

    /**
     * 应用标识
     */
    @Value("${zzd.qrcode.remark}")
    private String remark;

    @Value("${zzd.qrcode.domain}")
    private String domain;

    private static String key;

    private static String secret;

    private static String mark;

    private static String domain_name;

    @PostConstruct
    public void setProperties() {
        key = this.appkey;
        secret = this.appsecret;
        mark = this.remark;
        domain_name=this.domain;
    }

    public static String getKey() {
        return key;
    }

    public static String getSecret() {
        return secret;
    }

    public static String getMark() {
        return mark;
    }

    public static String getDomain_name() {
        return domain_name;
    }
}
