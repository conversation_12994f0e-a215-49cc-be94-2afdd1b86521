package com.concise.modular.zwdd;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;

import com.concise.common.pojo.response.ResponseData;
import com.concise.sys.modular.auth.service.AuthService;
import com.concise.sys.modular.org.service.SysOrgService;
import com.concise.sys.modular.user.entity.SysUser;
import com.concise.sys.modular.user.service.SysUserDataScopeService;
import com.concise.sys.modular.user.service.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 单点登录控制器
 *
 * <AUTHOR>
 * @date 2021-08-03 14:27
 */
@Slf4j
@Api(tags = "浙政钉-单点登录")
@RestController
public class SignInController {

    @Autowired
    SysUserService sysUserService;

    @Autowired
    AuthService authService;

    @Autowired
    SysUserDataScopeService sysUserDataScopeService;

    @Autowired
    SysOrgService sysOrgService;

    @ApiOperation("浙政钉扫码登录接口")
    @GetMapping(value = "/loginQrCode")
    public ResponseData loginQrCode(String code) {
        JSONObject userInfoByQrCode = ZwddQrCodeUtil.getUserInfoByQrCode(ZwddQrCodeUtil.getAccessToken(), code);
        if (userInfoByQrCode.getBoolean("success")) {
            try {
                String content = userInfoByQrCode.getString("content");
                JSONObject jsonObject = JSONObject.parseObject(content);
                Boolean success = jsonObject.getBoolean("success");
                if (success) {
                    String userInfoString = jsonObject.getString("data");
                    JSONObject userInfo = JSONObject.parseObject(userInfoString);
                    String employeeCode = userInfo.getString("employeeCode");
                    SysUser sysUser = sysUserService.getById(employeeCode);
                    if (ObjectUtil.isNotEmpty(sysUser)) {
                        String doLogin = authService.doLogin(sysUser);
                        Map<String, String> map = new HashMap<>();
                        map.put("token", doLogin);
                        map.put("username", sysUser.getAccount());
                        map.put("userId", sysUser.getId());
                        return ResponseData.success(map);
                    }
                    return ResponseData.error("用户不存在！");
                } else {
                    String responseMessage = jsonObject.getString("responseMessage");
                    return ResponseData.error("登录失败！" + responseMessage);
                }

            } catch (Exception e) {
                e.printStackTrace();
                return ResponseData.error("登录失败！" + e.getMessage());
            }
        }
        return ResponseData.error("登录失败！" + userInfoByQrCode.getString("content"));
    }

}
