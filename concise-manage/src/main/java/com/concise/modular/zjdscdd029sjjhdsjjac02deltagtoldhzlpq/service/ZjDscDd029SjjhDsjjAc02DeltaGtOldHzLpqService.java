package com.concise.modular.zjdscdd029sjjhdsjjac02deltagtoldhzlpq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.screen.vo.ScreenVo;
import com.concise.modular.zjdscdd029sjjhdsjjac02deltagtoldhzlpq.entity.ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq;
import com.concise.modular.zjdscdd029sjjhdsjjac02deltagtoldhzlpq.param.ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam;

import java.util.List;

/**
 * 省下发_社会保险个人参保信息service接口
 *
 * <AUTHOR>
 * @date 2023-03-15 14:07:21
 */
public interface ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqService extends IService<ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq> {

    /**
     * 查询省下发_社会保险个人参保信息
     *
     * <AUTHOR>
     * @date 2023-03-15 14:07:21
     */
    PageResult<ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq> page(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam);

    /**
     * 省下发_社会保险个人参保信息列表
     *
     * <AUTHOR>
     * @date 2023-03-15 14:07:21
     */
    List<ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq> list(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam);

    /**
     * 添加省下发_社会保险个人参保信息
     *
     * <AUTHOR>
     * @date 2023-03-15 14:07:21
     */
    void add(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam);

    /**
     * 删除省下发_社会保险个人参保信息
     *
     * <AUTHOR>
     * @date 2023-03-15 14:07:21
     */
    void delete(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam);

    /**
     * 编辑省下发_社会保险个人参保信息
     *
     * <AUTHOR>
     * @date 2023-03-15 14:07:21
     */
    void edit(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam);

    /**
     * 查看省下发_社会保险个人参保信息
     *
     * <AUTHOR>
     * @date 2023-03-15 14:07:21
     */
     ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq detail(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam);

     List<ScreenVo> socialSecurity(String orgId);
}
