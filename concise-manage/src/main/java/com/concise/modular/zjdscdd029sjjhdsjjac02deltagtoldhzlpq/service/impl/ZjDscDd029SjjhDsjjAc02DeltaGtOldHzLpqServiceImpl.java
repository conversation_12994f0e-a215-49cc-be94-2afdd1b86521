package com.concise.modular.zjdscdd029sjjhdsjjac02deltagtoldhzlpq.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.modular.correctionobjectinformation.service.CorrectionObjectInformationService;
import com.concise.modular.screen.vo.ScreenVo;
import com.concise.modular.zjdscdd029sjjhdsjjac02deltagtoldhzlpq.entity.ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq;
import com.concise.modular.zjdscdd029sjjhdsjjac02deltagtoldhzlpq.enums.ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqExceptionEnum;
import com.concise.modular.zjdscdd029sjjhdsjjac02deltagtoldhzlpq.mapper.ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqMapper;
import com.concise.modular.zjdscdd029sjjhdsjjac02deltagtoldhzlpq.param.ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam;
import com.concise.modular.zjdscdd029sjjhdsjjac02deltagtoldhzlpq.service.ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqService;
import com.concise.sys.modular.org.entity.SysOrg;
import com.concise.sys.modular.org.service.SysOrgService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 省下发_社会保险个人参保信息service接口实现类
 *
 * <AUTHOR>
 * @date 2023-03-15 14:07:21
 */
@DS(value = "huiliu")
@Service
public class ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqServiceImpl extends ServiceImpl<ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqMapper, ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq> implements ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqService {
    public static final String LP = "DEPT0000000000000000000000910500";
    @Resource
    private CorrectionObjectInformationService correctionObjectInformationService;
    @Resource
    private SysOrgService sysOrgService;

    @Override
    public PageResult<ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq> page(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam) {
        QueryWrapper<ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam)) {

            // 根据基准参保关系ID 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getBaz159())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getBaz159, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getBaz159());
            }
            // 根据个人编号 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAac001())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getAac001, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAac001());
            }
            // 根据社会保障号码 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAac002())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getAac002, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAac002());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAac003())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getAac003, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAac003());
            }
            // 根据证件类型 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAac058())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getAac058, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAac058());
            }
            // 根据单位编号 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAab001())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getAab001, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAab001());
            }
            // 根据统一社会信用代码 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getBab010())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getBab010, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getBab010());
            }
            // 根据单位名称 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAab004())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getAab004, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAab004());
            }
            // 根据险种类型 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAae140())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getAae140, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAae140());
            }
            // 根据人员参保状态 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAac008())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getAac008, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAac008());
            }
            // 根据个人缴费状态 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAac031())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getAac031, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAac031());
            }
            // 根据开始日期 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAae030())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getAae030, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAae030());
            }
            // 根据终止日期 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAae031())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getAae031, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAae031());
            }
            // 根据行政区划代码 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAab301())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getAab301, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAab301());
            }
            // 根据所属地市 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscCity())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getDscCity, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscCity());
            }
            // 根据所需区/县 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscAdmRegion())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getDscAdmRegion, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscAdmRegion());
            }
            // 根据数源单位代码 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscSydepCode())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getDscSydepCode, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscSydepCode());
            }
            // 根据数源单位 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscSydepName())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getDscSydepName, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscSydepName());
            }
            // 根据数据所属系统名称 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscSydepSys())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getDscSydepSys, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscSydepSys());
            }
            // 根据数源单位表名 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscSydepTblname())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getDscSydepTblname, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscSydepTblname());
            }
            // 根据唯一自增序列号 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscBizRecordId())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getDscBizRecordId, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscBizRecordId());
            }
            // 根据I 插入 U 更新 D 删除 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscBizOperation())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getDscBizOperation, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscBizOperation());
            }
            // 根据源表数据同步时间 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscBizTimestamp())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getDscBizTimestamp, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscBizTimestamp());
            }
            // 根据数据来源表名(清洗库或基础库 表名) 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscDatasrTblname())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getDscDatasrTblname, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscDatasrTblname());
            }
            // 根据业务主键MD5值（清洗增加） 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscHashUnique())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getDscHashUnique, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscHashUnique());
            }
            // 根据清洗时间（清洗增加） 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscCleanTimestamp())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getDscCleanTimestamp, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscCleanTimestamp());
            }
            // 根据地市仓数据入库时间 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscDwRksj())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getDscDwRksj, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getDscDwRksj());
            }
            // 根据区县仓数据入库时间 查询
            if (ObjectUtil.isNotEmpty(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getEtlDate())) {
                queryWrapper.lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getEtlDate, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getEtlDate());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq> list(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam) {
        return this.list();
    }

    @Override
    public void add(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam) {
        ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq = new ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq();
        BeanUtil.copyProperties(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq);
        this.save(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam) {
        this.removeById(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAac147());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam) {
        ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq = this.queryZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam);
        BeanUtil.copyProperties(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam, zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq);
        this.updateById(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq);
    }

    @Override
    public ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq detail(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam) {
        return this.queryZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam);
    }

    /**
     * 获取省下发_社会保险个人参保信息
     *
     * <AUTHOR>
     * @date 2023-03-15 14:07:21
     */
    private ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq queryZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam) {
        ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq = this.getById(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.getAac147());
        if (ObjectUtil.isNull(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq)) {
            throw new ServiceException(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqExceptionEnum.NOT_EXIST);
        }
        return zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq;
    }

    @Cacheable(value="socialSecurity",key="#orgId")
    @Override
    public List<ScreenVo> socialSecurity(String orgId) {
        List<ScreenVo> screenVoList = new ArrayList<>();
        List<CorrectionObjectInformation> correctionObjectInformationList;
        if (LP.equals(orgId)) {
            List<String> collect = sysOrgService.list().stream().map(SysOrg::getId).collect(Collectors.toList());
            correctionObjectInformationList = correctionObjectInformationService.list(new QueryWrapper<CorrectionObjectInformation>().lambda().in(CorrectionObjectInformation::getJzjg, collect).eq(CorrectionObjectInformation::getZhuangtai, "200"));
        } else {
            correctionObjectInformationList = correctionObjectInformationService.list(new QueryWrapper<CorrectionObjectInformation>().lambda().eq(CorrectionObjectInformation::getJzjg, orgId).eq(CorrectionObjectInformation::getZhuangtai, "200"));
        }
        if (CollectionUtil.isNotEmpty(correctionObjectInformationList)) {
            for (CorrectionObjectInformation correctionObjectInformation : correctionObjectInformationList) {
                List<ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq> list = this.list(new QueryWrapper<ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq>().lambda().eq(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq::getAac147, correctionObjectInformation.getSfzh()));
                if (CollectionUtil.isEmpty(list)){
                    ScreenVo screenVo = new ScreenVo();
                    screenVo.setName(correctionObjectInformation.getXm());
                    screenVo.setJzjg(correctionObjectInformation.getJzjgName());
                    if (ObjectUtil.isNotEmpty(correctionObjectInformation.getCsrq())){
                        screenVo.setCsrq(correctionObjectInformation.getCsrq());
                    }
                    screenVo.setWhcdName(correctionObjectInformation.getWhcdName());
                    screenVo.setXbName(correctionObjectInformation.getXbName());
                    if (!"浙江省".equals(correctionObjectInformation.getHjszsName())){
                        screenVo.setHj("外省");
                    }else if ("杭州市".equals(correctionObjectInformation.getHjszdsName())){
                        screenVo.setHj("本市");
                    }else {
                        screenVo.setHj("外市");
                    }
                    screenVoList.add(screenVo);
                }
            }
        }
        return screenVoList;
    }
}
