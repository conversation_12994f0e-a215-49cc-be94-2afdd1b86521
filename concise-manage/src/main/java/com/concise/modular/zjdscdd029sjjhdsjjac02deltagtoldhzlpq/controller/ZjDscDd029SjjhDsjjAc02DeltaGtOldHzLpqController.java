package com.concise.modular.zjdscdd029sjjhdsjjac02deltagtoldhzlpq.controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.modular.zjdscdd029sjjhdsjjac02deltagtoldhzlpq.param.ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam;
import com.concise.modular.zjdscdd029sjjhdsjjac02deltagtoldhzlpq.service.ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 省下发_社会保险个人参保信息控制器
 *
 * <AUTHOR>
 * @date 2023-03-15 14:07:21
 */
@Api(tags = "省下发_社会保险个人参保信息")
@RestController
public class ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqController {

    @Resource
    private ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqService zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqService;

    /**
     * 查询省下发_社会保险个人参保信息
     *
     * <AUTHOR>
     * @date 2023-03-15 14:07:21
     */
    @Permission
    @GetMapping("/zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq/page")
    @ApiOperation("省下发_社会保险个人参保信息_分页查询")
    @BusinessLog(title = "省下发_社会保险个人参保信息_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam) {
        return new SuccessResponseData(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqService.page(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam));
    }

    /**
     * 添加省下发_社会保险个人参保信息
     *
     * <AUTHOR>
     * @date 2023-03-15 14:07:21
     */
    @Permission
    @PostMapping("/zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq/add")
    @ApiOperation("省下发_社会保险个人参保信息_增加")
    @BusinessLog(title = "省下发_社会保险个人参保信息_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.add.class) ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam) {
        zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqService.add(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam);
        return new SuccessResponseData();
    }

    /**
     * 删除省下发_社会保险个人参保信息
     *
     * <AUTHOR>
     * @date 2023-03-15 14:07:21
     */
    @Permission
    @PostMapping("/zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq/delete")
    @ApiOperation("省下发_社会保险个人参保信息_删除")
    @BusinessLog(title = "省下发_社会保险个人参保信息_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.delete.class) ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam) {
        zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqService.delete(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑省下发_社会保险个人参保信息
     *
     * <AUTHOR>
     * @date 2023-03-15 14:07:21
     */
    @Permission
    @PostMapping("/zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq/edit")
    @ApiOperation("省下发_社会保险个人参保信息_编辑")
    @BusinessLog(title = "省下发_社会保险个人参保信息_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.edit.class) ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam) {
        zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqService.edit(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam);
        return new SuccessResponseData();
    }

    /**
     * 查看省下发_社会保险个人参保信息
     *
     * <AUTHOR>
     * @date 2023-03-15 14:07:21
     */
    @Permission
    @GetMapping("/zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq/detail")
    @ApiOperation("省下发_社会保险个人参保信息_查看")
    @BusinessLog(title = "省下发_社会保险个人参保信息_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam.detail.class) ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam) {
        return new SuccessResponseData(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqService.detail(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam));
    }

    /**
     * 省下发_社会保险个人参保信息列表
     *
     * <AUTHOR>
     * @date 2023-03-15 14:07:21
     */
    @Permission
    @GetMapping("/zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq/list")
    @ApiOperation("省下发_社会保险个人参保信息_列表")
    @BusinessLog(title = "省下发_社会保险个人参保信息_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam) {
        return new SuccessResponseData(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqService.list(zjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam));
    }

}
