package com.concise.modular.zjdscdd029sjjhdsjjac02deltagtoldhzlpq.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 省下发_社会保险个人参保信息
 *
 * <AUTHOR>
 * @date 2023-03-15 14:07:21
 */
@Data
@TableName("zj_dsc_dd029_sjjh_dsjj_ac02_delta_gt_old_hz_lpq")
public class ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpq {


    /**
     * 基准参保关系ID
     */
    private String baz159;

    /**
     * 个人编号
     */
    private String aac001;

    /**
     * 社会保障号码
     */
    private String aac002;

    /**
     * 姓名
     */
    private String aac003;

    /**
     * 证件类型
     */
    private String aac058;
    /**
     * 证件号码
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String aac147;

    /**
     * 单位编号
     */
    private String aab001;

    /**
     * 统一社会信用代码
     */
    private String bab010;

    /**
     * 单位名称
     */
    private String aab004;

    /**
     * 险种类型
     */
    private String aae140;

    /**
     * 人员参保状态
     */
    private String aac008;

    /**
     * 个人缴费状态
     */
    private String aac031;

    /**
     * 开始日期
     */
    private BigDecimal aae030;

    /**
     * 终止日期
     */
    private BigDecimal aae031;

    /**
     * 行政区划代码
     */
    private String aab301;

    /**
     * 所属地市
     */
    private String dscCity;

    /**
     * 所需区/县
     */
    private String dscAdmRegion;

    /**
     * 数源单位代码
     */
    private String dscSydepCode;

    /**
     * 数源单位
     */
    private String dscSydepName;

    /**
     * 数据所属系统名称
     */
    private String dscSydepSys;

    /**
     * 数源单位表名
     */
    private String dscSydepTblname;

    /**
     * 唯一自增序列号
     */
    private String dscBizRecordId;

    /**
     * I 插入 U 更新 D 删除
     */
    private String dscBizOperation;

    /**
     * 源表数据同步时间
     */
    @Excel(name = "源表数据同步时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date dscBizTimestamp;

    /**
     * 数据来源表名(清洗库或基础库 表名)
     */
    private String dscDatasrTblname;

    /**
     * 业务主键MD5值（清洗增加）
     */
    private String dscHashUnique;

    /**
     * 清洗时间（清洗增加）
     */
    private String dscCleanTimestamp;

    /**
     * 地市仓数据入库时间
     */
    @Excel(name = "地市仓数据入库时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date dscDwRksj;

    /**
     * 区县仓数据入库时间
     */
    @Excel(name = "区县仓数据入库时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date etlDate;

}
