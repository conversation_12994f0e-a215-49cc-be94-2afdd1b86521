package com.concise.modular.zjdscdd029sjjhdsjjac02deltagtoldhzlpq.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
* 省下发_社会保险个人参保信息参数类
 *
 * <AUTHOR>
 * @date 2023-03-15 14:07:21
*/
@Data
public class ZjDscDd029SjjhDsjjAc02DeltaGtOldHzLpqParam extends BaseParam {

    /**
     * 基准参保关系ID
     */
    @NotBlank(message = "基准参保关系ID不能为空，请检查baz159参数", groups = {add.class, edit.class})
    private String baz159;

    /**
     * 个人编号
     */
    @NotBlank(message = "个人编号不能为空，请检查aac001参数", groups = {add.class, edit.class})
    private String aac001;

    /**
     * 社会保障号码
     */
    @NotBlank(message = "社会保障号码不能为空，请检查aac002参数", groups = {add.class, edit.class})
    private String aac002;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查aac003参数", groups = {add.class, edit.class})
    private String aac003;

    /**
     * 证件类型
     */
    @NotBlank(message = "证件类型不能为空，请检查aac058参数", groups = {add.class, edit.class})
    private String aac058;

    /**
     * 证件号码
     */
    @NotNull(message = "证件号码不能为空，请检查aac147参数", groups = {edit.class, delete.class, detail.class})
    private String aac147;

    /**
     * 单位编号
     */
    @NotBlank(message = "单位编号不能为空，请检查aab001参数", groups = {add.class, edit.class})
    private String aab001;

    /**
     * 统一社会信用代码
     */
    @NotBlank(message = "统一社会信用代码不能为空，请检查bab010参数", groups = {add.class, edit.class})
    private String bab010;

    /**
     * 单位名称
     */
    @NotBlank(message = "单位名称不能为空，请检查aab004参数", groups = {add.class, edit.class})
    private String aab004;

    /**
     * 险种类型
     */
    @NotBlank(message = "险种类型不能为空，请检查aae140参数", groups = {add.class, edit.class})
    private String aae140;

    /**
     * 人员参保状态
     */
    @NotBlank(message = "人员参保状态不能为空，请检查aac008参数", groups = {add.class, edit.class})
    private String aac008;

    /**
     * 个人缴费状态
     */
    @NotBlank(message = "个人缴费状态不能为空，请检查aac031参数", groups = {add.class, edit.class})
    private String aac031;

    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空，请检查aae030参数", groups = {add.class, edit.class})
    private BigDecimal aae030;

    /**
     * 终止日期
     */
    @NotNull(message = "终止日期不能为空，请检查aae031参数", groups = {add.class, edit.class})
    private BigDecimal aae031;

    /**
     * 行政区划代码
     */
    @NotBlank(message = "行政区划代码不能为空，请检查aab301参数", groups = {add.class, edit.class})
    private String aab301;

    /**
     * 所属地市
     */
    @NotBlank(message = "所属地市不能为空，请检查dscCity参数", groups = {add.class, edit.class})
    private String dscCity;

    /**
     * 所需区/县
     */
    @NotBlank(message = "所需区/县不能为空，请检查dscAdmRegion参数", groups = {add.class, edit.class})
    private String dscAdmRegion;

    /**
     * 数源单位代码
     */
    @NotBlank(message = "数源单位代码不能为空，请检查dscSydepCode参数", groups = {add.class, edit.class})
    private String dscSydepCode;

    /**
     * 数源单位
     */
    @NotBlank(message = "数源单位不能为空，请检查dscSydepName参数", groups = {add.class, edit.class})
    private String dscSydepName;

    /**
     * 数据所属系统名称
     */
    @NotBlank(message = "数据所属系统名称不能为空，请检查dscSydepSys参数", groups = {add.class, edit.class})
    private String dscSydepSys;

    /**
     * 数源单位表名
     */
    @NotBlank(message = "数源单位表名不能为空，请检查dscSydepTblname参数", groups = {add.class, edit.class})
    private String dscSydepTblname;

    /**
     * 唯一自增序列号
     */
    @NotBlank(message = "唯一自增序列号不能为空，请检查dscBizRecordId参数", groups = {add.class, edit.class})
    private String dscBizRecordId;

    /**
     * I 插入 U 更新 D 删除
     */
    @NotBlank(message = "I 插入 U 更新 D 删除不能为空，请检查dscBizOperation参数", groups = {add.class, edit.class})
    private String dscBizOperation;

    /**
     * 源表数据同步时间
     */
    @NotNull(message = "源表数据同步时间不能为空，请检查dscBizTimestamp参数", groups = {add.class, edit.class})
    private String dscBizTimestamp;

    /**
     * 数据来源表名(清洗库或基础库 表名)
     */
    @NotBlank(message = "数据来源表名(清洗库或基础库 表名)不能为空，请检查dscDatasrTblname参数", groups = {add.class, edit.class})
    private String dscDatasrTblname;

    /**
     * 业务主键MD5值（清洗增加）
     */
    @NotBlank(message = "业务主键MD5值（清洗增加）不能为空，请检查dscHashUnique参数", groups = {add.class, edit.class})
    private String dscHashUnique;

    /**
     * 清洗时间（清洗增加）
     */
    @NotBlank(message = "清洗时间（清洗增加）不能为空，请检查dscCleanTimestamp参数", groups = {add.class, edit.class})
    private String dscCleanTimestamp;

    /**
     * 地市仓数据入库时间
     */
    @NotNull(message = "地市仓数据入库时间不能为空，请检查dscDwRksj参数", groups = {add.class, edit.class})
    private String dscDwRksj;

    /**
     * 区县仓数据入库时间
     */
    @NotNull(message = "区县仓数据入库时间不能为空，请检查etlDate参数", groups = {add.class, edit.class})
    private String etlDate;

}
