package com.concise.modular.config;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2020年06月23日 10:33
 * @Description 系统相关配置
 */
@Slf4j
@Configuration
public class InitializeConfig implements CommandLineRunner {


    @Value("${server.port}")
    private String port;

    @Value(value = "${server.servlet.context-path}")
    private String context;

    @Value(value = "${spring.application.name}")
    private String applicationName;



    @Override
    public void run(String... args) throws Exception {
        try {
            InetAddress localHost = InetAddress.getLocalHost();

            log.info("{} 启动成功: http://{}:{}{}", applicationName, localHost.getHostAddress(), port, context);
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
    }


}
