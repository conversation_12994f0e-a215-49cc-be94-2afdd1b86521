package com.concise.modular.screen.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * @Author: luo
 * @Date: 2022-11-17 11:14
 */
@Data
public class ScreenVo {
    /**
     * 名称
     */
    private String name;

    /**
     * 数量
     */
    private Integer value;

    private String jzjg;

    /**
     * 学历
     */
    @TableField(exist = false)
    private String whcdName;

    /**
     * 出生日期
     */
    @TableField(exist = false)
    private Date csrq;

    /**
     * 性别
     */
    @TableField(exist = false)
    private String xbName;

    /**
     * 户籍
     */
    @TableField(exist = false)
    private String hj;
}
