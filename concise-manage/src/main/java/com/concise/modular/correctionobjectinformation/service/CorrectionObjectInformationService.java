package com.concise.modular.correctionobjectinformation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.modular.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.modular.correctionobjectinformation.param.CorrectionObjectInformationParam;
import com.concise.modular.screen.vo.ScreenVo;


import java.util.List;

/**
 * 矫正对象信息表service接口
 *
 * <AUTHOR>
 * @date 2021-09-10 17:17:06
 */
public interface CorrectionObjectInformationService extends IService<CorrectionObjectInformation> {

    /**
     * 查询矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    PageResult<CorrectionObjectInformation> page(CorrectionObjectInformationParam correctionObjectInformationParam);

    /**
     * 矫正对象信息表列表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    List<CorrectionObjectInformation> list(CorrectionObjectInformationParam correctionObjectInformationParam);

    /**
     * 添加矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    void add(CorrectionObjectInformationParam correctionObjectInformationParam);

    /**
     * 删除矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    void delete(List<CorrectionObjectInformationParam> correctionObjectInformationParamList);

    /**
     * 编辑矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    void edit(CorrectionObjectInformationParam correctionObjectInformationParam);

    /**
     * 查看矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    CorrectionObjectInformation detail(CorrectionObjectInformationParam correctionObjectInformationParam);

    /**
     * 导出矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    void export(CorrectionObjectInformationParam correctionObjectInformationParam);

    /**
     * 获取全部的矫正对象id
     *
     * @return
     */
    List<String> getCorrectionIds();

    /**
     * 获取吴兴的矫正对象id
     *
     * @return
     */
    List<String> getCorrectionWuxinIds();

    /**
     * 获取
     *
     * @return
     */
    List<String> getCorrectionIdsByDepart();

    /**
     * 更新矫正对象状态
     *
     * @param id
     * @param zhuangtai
     */
    void updateCorrectionZt(String id, String zhuangtai);

    /**
     * 只获取id和sfzh
     * @return
     */
    List<CorrectionObjectInformation> getCorrectionIdAndSfzhs();

    /**
     * 根据机构分类人员
     * @param collect
     * @return
     */
    List<ScreenVo> personnelClassification(List<String> collect);
}
