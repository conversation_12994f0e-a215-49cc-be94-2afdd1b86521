<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.modular.correctionobjectinformation.mapper.CorrectionObjectInformationMapper">
    <update id="updateCorrectionZt">
        UPDATE correction_object_information
        SET zhuangtai = #{zhuangtai}
        WHERE
            id = #{id}
    </update>

    <select id="getCorrectionIds" resultType="java.lang.String">
        SELECT
            id
        FROM
            correction_object_information
    </select>
    <select id="getCorrectionWuxinIds" resultType="java.lang.String">
        SELECT id FROM correction_object_information WHERE jzjg IN (SELECT id FROM sys_depart WHERE id='DEPT0000000000000000000000902044' OR parent_id='DEPT0000000000000000000000902044')
    </select>
    <select id="getCorrectionWuxin"
            resultType="com.concise.modular.correctionobjectinformation.entity.CorrectionObjectInformation">
        SELECT * FROM correction_object_information WHERE jzjg IN (SELECT id FROM sys_depart WHERE id='DEPT0000000000000000000000902044' OR parent_id='DEPT0000000000000000000000902044')
    </select>
    <select id="getCorrectionIdsByDepart" resultType="java.lang.String">
        SELECT
            id
        FROM
            correction_object_information
        WHERE
                jzjg IN (
                SELECT
                    id
                FROM
                    sys_depart
                WHERE
                        parent_id IN ( SELECT id FROM `sys_depart` WHERE id = 'DEPT0000000000000000000000900336' OR parent_id = 'DEPT0000000000000000000000900336' )
                   OR id = 'DEPT0000000000000000000000900336')
    </select>
    <select id="getCorrectionIdAndSfzhs"
            resultType="com.concise.modular.correctionobjectinformation.entity.CorrectionObjectInformation">
        SELECT
            id,
            sfzh
        FROM
            correction_object_information
    </select>

    <select id="getCorrection" resultType="com.concise.modular.correctionobjectinformation.entity.CorrectionObjectInformation">
        SELECT
            id,
            xm,
            sfzh,
            sqjzksrq
        FROM
            correction_object_information where zhuangtai = 200 order by rujiaoriqi asc
    </select>
    <select id="personnelClassification" resultType="com.concise.modular.screen.vo.ScreenVo">
        SELECT
            jzlb_name AS NAME,
            COUNT( id ) AS VALUE

        FROM
            `correction_object_information`
        WHERE
        zhuangtai='200' and
        jzjg in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY
            jzlb, jzlb_name
    </select>
</mapper>
