
package com.concise.modular.correctionobjectinformation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.modular.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.modular.screen.vo.ScreenVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 矫正对象信息表
 *
 * <AUTHOR>
 * @date 2021-09-10 17:17:06
 */
public interface CorrectionObjectInformationMapper extends BaseMapper<CorrectionObjectInformation> {
    /**
     * 获取全部的矫正对象id
     * @return
     */
    List<String> getCorrectionIds();

    /**
     * 吴兴矫正对象id
     * @return
     */
    List<String> getCorrectionWuxinIds();

    /**
     * 吴兴矫正对象
     * @return
     */
    List<CorrectionObjectInformation> getCorrectionWuxin();

    /**
     * 宁波矫正对象列表
     * @return
     */
    List<String> getCorrectionIdsByDepart();

    /**
     * 更新矫正对象状态
     * @param id
     * @param zhuangtai
     */
    void updateCorrectionZt(@Param(value = "id") String id,@Param(value = "zhuangtai") String zhuangtai);

    /**
     * 只获取id和sfzh
     * @return
     */
    List<CorrectionObjectInformation> getCorrectionIdAndSfzhs();

    /**
     * 只获取id和sfzh(只取在矫的)
     * @return
     */
    List<CorrectionObjectInformation> getCorrection();

    /**
     * 根据机构分类
     * @param list
     * @return
     */
    List<ScreenVo> personnelClassification(List<String> list);
}
