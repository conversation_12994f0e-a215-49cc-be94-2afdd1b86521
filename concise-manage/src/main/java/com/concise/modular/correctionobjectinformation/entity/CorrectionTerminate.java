package com.concise.modular.correctionobjectinformation.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 矫正对象解矫列表
 *
 * <AUTHOR>
 * @date 2022-02-23 14:14:40
 */
@Data
@TableName("correction_terminate")
public class CorrectionTerminate {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 状态
     */
    private String zhuangtai;

    /**
     * 状态中文值
     */
    private String zhuangtaiName;

    /**
     * 终止日期
     */
    @Excel(name = "终止日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    @ApiModelProperty(value = "终止日期")
    private Date zhongzhiriqi;

    /**
     * 最后修改时间
     */
    @Excel(name = "最后修改时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    @ApiModelProperty(value = "最后修改时间")
    private Date lastModifiedTime;

    /**
     * 收监执行原因
     */
    private String sjzxyy;
    /**
     * 收监执行原因名称
     */
    private String sjzxyyName;

}
