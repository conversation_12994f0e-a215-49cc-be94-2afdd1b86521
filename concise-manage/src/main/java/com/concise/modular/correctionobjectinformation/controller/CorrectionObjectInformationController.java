package com.concise.modular.correctionobjectinformation.controller;


import java.util.List;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.base.param.BaseParam;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.modular.correctionobjectinformation.param.CorrectionObjectInformationParam;
import com.concise.modular.correctionobjectinformation.service.CorrectionObjectInformationService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 矫正对象信息表控制器
 *
 * <AUTHOR>
 * @date 2021-09-10 17:17:06
 */
@Api(tags = "矫正对象信息表")
@RestController
public class CorrectionObjectInformationController {

    @Resource
    private CorrectionObjectInformationService correctionObjectInformationService;

    /**
     * 查询矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    @Permission
    @ApiOperation(value = "矫正对象信息表_查询")
    @GetMapping("/correctionObjectInformation/page")
    @BusinessLog(title = "矫正对象信息表_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(CorrectionObjectInformationParam correctionObjectInformationParam) {
        return new SuccessResponseData(correctionObjectInformationService.page(correctionObjectInformationParam));
    }


    /**
     * 添加矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    @Permission
    @PostMapping("/correctionObjectInformation/add")
    @BusinessLog(title = "矫正对象信息表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(BaseParam.add.class) CorrectionObjectInformationParam correctionObjectInformationParam) {
        correctionObjectInformationService.add(correctionObjectInformationParam);
        return new SuccessResponseData();
    }

    /**
     * 删除矫正对象信息表，可批量删除
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    @Permission
    @PostMapping("/correctionObjectInformation/delete")
    @BusinessLog(title = "矫正对象信息表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(BaseParam.delete.class) List<CorrectionObjectInformationParam> correctionObjectInformationParamList) {
        correctionObjectInformationService.delete(correctionObjectInformationParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    @Permission
    @PostMapping("/correctionObjectInformation/edit")
    @BusinessLog(title = "矫正对象信息表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(BaseParam.edit.class) CorrectionObjectInformationParam correctionObjectInformationParam) {
        correctionObjectInformationService.edit(correctionObjectInformationParam);
        return new SuccessResponseData();
    }

    /**
     * 查看矫正对象信息表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    @Permission
    @GetMapping("/correctionObjectInformation/detail")
    @BusinessLog(title = "矫正对象信息表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(BaseParam.detail.class) CorrectionObjectInformationParam correctionObjectInformationParam) {
        return new SuccessResponseData(correctionObjectInformationService.detail(correctionObjectInformationParam));
    }

    /**
     * 矫正对象信息表列表
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    @Permission
    @GetMapping("/correctionObjectInformation/list")
    @BusinessLog(title = "矫正对象信息表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(CorrectionObjectInformationParam correctionObjectInformationParam) {
        return new SuccessResponseData(correctionObjectInformationService.list(correctionObjectInformationParam));
    }

    /**
     * 导出系统用户
     *
     * <AUTHOR>
     * @date 2021-09-10 17:17:06
     */
    @Permission
    @GetMapping("/correctionObjectInformation/export")
    @BusinessLog(title = "矫正对象信息表_导出", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void export(CorrectionObjectInformationParam correctionObjectInformationParam) {
        correctionObjectInformationService.export(correctionObjectInformationParam);
    }



}
