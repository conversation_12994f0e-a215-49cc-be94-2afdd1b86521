package com.concise;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * SpringBoot方式启动类
 *
 * <AUTHOR>
 * @date 2017/5/21 12:06
 */
@EnableCaching
@EnableScheduling
@EnableAsync
@SpringBootApplication(scanBasePackages = {"com.concise"})
public class ConciseManageApplication {

    public static void main(String[] args) {
        SpringApplication.run(ConciseManageApplication.class, args);
    }

}
