package com.jiashi.diary.correctionobjectinformationdelete.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 矫正对象删除列表
 *
 * <AUTHOR>
 * @date 2022-10-09 16:55:27
 */
@Data
@TableName("correction_object_information_delete")
public class CorrectionObjectInformationDelete {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 状态
     */
    private String zhuangtai;

    /**
     * 状态中文值
     */
    private String zhuangtaiName;

    /**
     * 收监执行原因
     */
    private String sjzxyy;

    /**
     * 收监执行原因中文值
     */
    private String sjzxyyName;

    /**
     * 最后更新时间
     */
    @Excel(name = "最后更新时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date lastModifiedTime;

    /**
     * 是否删除
     */
    private String isRemoved;

}
