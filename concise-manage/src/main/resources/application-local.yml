# Mysql数据库 - 使用环境变量避免敏感信息泄露
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${DB_URL:*******************************************************************************************************************************************************************************************************}
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}


# 认证中心配置 - 使用环境变量避免敏感信息泄露
auth:
  center:
    # 客户端ID（必填）- 从认证中心获取
    client-id: ${AUTH_CLIENT_ID:your_client_id_here}
    # 客户端密钥（必填）- 从认证中心获取
    client-secret: ${AUTH_CLIENT_SECRET:your_client_secret_here}
    # 认证中心授权地址（必填）
    authorize-url: ${AUTH_AUTHORIZE_URL:https://one.lpxxfw.cn:7200/auth}
    # 获取token的地址（必填）
    token-url: ${AUTH_TOKEN_URL:https://localhost:7100/typt/public/user/getUserInfoByToken}
    # 撤销token的地址（必填）
    revoke-url: ${AUTH_REVOKE_URL:https://one.lpxxfw.cn:7200/auth/oauth2/revoke}
    # 登出地址（必填）
    logout-url: ${AUTH_LOGOUT_URL:https://one.lpxxfw.cn:7200/auth/logout}
    # 回调地址（必填）- 必须与注册时的地址一致，使用实际域名
    redirect-uri: ${AUTH_REDIRECT_URI:http://localhost:8050/#/transfer}
    # 授权范围（可选）- 默认为AUTH
    scope: ${AUTH_SCOPE:AUTH}
