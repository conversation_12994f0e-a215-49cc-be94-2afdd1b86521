# Mysql数据库 - 使用环境变量避免敏感信息泄露
spring:
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候回抛出异常,不启动会使用默认数据源.
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${DB_MASTER_URL:*********************************************************************************************************************************************************************************}
          username: ${DB_MASTER_USERNAME:root}
          password: ${DB_MASTER_PASSWORD:password}
        dataCenter:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${DB_DATACENTER_URL:****************************************************************************************************************************************************************************}
          username: ${DB_DATACENTER_USERNAME:root}
          password: ${DB_DATACENTER_PASSWORD:password}
        jyfx:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${DB_JYFX_URL:*************************************************************************************************************************************************************************}
          username: ${DB_JYFX_USERNAME:root}
          password: ${DB_JYFX_PASSWORD:password}
        huiliu:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${DB_HUILIU_URL:**********************************************************************************************************************************************************************************************}
          username: ${DB_HUILIU_USERNAME:root}
          password: ${DB_HUILIU_PASSWORD:password}
  redis:
    host: localhost
    port: 6379
    database: 5
#验证码相关配置 去除日志打印
logging:
  level:
    com.anji: off
    com.concise: info
# 浙政钉配置 - 使用环境变量避免敏感信息泄露
zzd:
  qrcode:
    appkey: ${ZZD_APPKEY:your_appkey_here}
    appsecret: ${ZZD_APPSECRET:your_appsecret_here}
    remark: ${ZZD_REMARK:xinyoulinxi_dingoa}
    domain: ${ZZD_DOMAIN:openplatform-pro.ding.zj.gov.cn}
    tenantid: ${ZZD_TENANTID:196729}

# 认证中心配置 - 使用环境变量避免敏感信息泄露
auth:
  center:
    # 客户端ID（必填）- 从认证中心获取
    client-id: ${AUTH_CLIENT_ID:your_client_id_here}
    # 客户端密钥（必填）- 从认证中心获取
    client-secret: ${AUTH_CLIENT_SECRET:your_client_secret_here}
    # 认证中心授权地址（必填）
    authorize-url: ${AUTH_AUTHORIZE_URL:https://one.lpxxfw.cn:7200/auth/oauth2/authorize}
    # 获取access_token的地址（必填）
    code-url: ${AUTH_CODE_URL:https://one.lpxxfw.cn:7200/auth/oauth2/token}
    # 获取token的地址（必填）
    token-url: ${AUTH_TOKEN_URL:https://localhost:7100/typt/public/user/getUserInfoByToken}
    # 撤销token的地址（必填）
    revoke-url: ${AUTH_REVOKE_URL:https://one.lpxxfw.cn:7200/auth/oauth2/revoke}
    # 登出地址（必填）
    logout-url: ${AUTH_LOGOUT_URL:https://one.lpxxfw.cn:7200/auth/logout}
    # 回调地址（必填）- 必须与注册时的地址一致，使用实际域名
    redirect-uri: ${AUTH_REDIRECT_URI:http://localhost:8050/transfer}
    # 授权范围（可选）- 默认为AUTH
    scope: ${AUTH_SCOPE:AUTH}
