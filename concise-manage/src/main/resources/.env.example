# 环境变量配置示例文件
# 复制此文件为 .env 并填入实际的生产环境配置值
# 注意：.env 文件应该添加到 .gitignore 中，避免提交到版本控制系统

# ===========================================
# 数据库配置
# ===========================================

# 主数据库配置
DB_MASTER_URL=************************************************************************************************************************************************************************************
DB_MASTER_USERNAME=your_db_username
DB_MASTER_PASSWORD=your_db_password

# 数据中心数据库配置
DB_DATACENTER_URL=*******************************************************************************************************************************************************************************
DB_DATACENTER_USERNAME=your_db_username
DB_DATACENTER_PASSWORD=your_db_password

# 矫正分析数据库配置
DB_JYFX_URL=****************************************************************************************************************************************************************************
DB_JYFX_USERNAME=your_db_username
DB_JYFX_PASSWORD=your_db_password

# 回流数据库配置
DB_HUILIU_URL=******************************************************************************************************************************************************************************************************
DB_HUILIU_USERNAME=your_huiliu_username
DB_HUILIU_PASSWORD=your_huiliu_password

# ===========================================
# 浙政钉配置
# ===========================================
ZZD_APPKEY=your_zzd_appkey
ZZD_APPSECRET=your_zzd_appsecret
ZZD_REMARK=your_app_remark
ZZD_DOMAIN=openplatform-pro.ding.zj.gov.cn
ZZD_TENANTID=your_tenant_id

# ===========================================
# 认证中心配置
# ===========================================
AUTH_CLIENT_ID=your_auth_client_id
AUTH_CLIENT_SECRET=your_auth_client_secret
AUTH_AUTHORIZE_URL=https://your_auth_domain/auth/oauth2/authorize
AUTH_CODE_URL=https://your_auth_domain/auth/oauth2/token
AUTH_TOKEN_URL=https://your_token_server/typt/public/user/getUserInfoByToken
AUTH_REVOKE_URL=https://your_auth_domain/auth/oauth2/revoke
AUTH_LOGOUT_URL=https://your_auth_domain/auth/logout
AUTH_REDIRECT_URI=http://your_app_domain/transfer
AUTH_SCOPE=AUTH

# ===========================================
# Redis配置 (用于application-local.yml)
# ===========================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# ===========================================
# SSO单点登录配置
# ===========================================
SSO_ENABLED=true
SSO_DEFAULT_TOKEN_EXPIRE=300
SSO_MAX_TOKEN_EXPIRE=1800
SSO_ENCRYPTION_ALGORITHM=SM4
SSO_KEY_LENGTH=16
SSO_BASE_SECRET_KEY=your_base_secret_key_here
SSO_ENABLE_TIMESTAMP_VALIDATION=true
SSO_ENABLE_IP_WHITELIST=false
SSO_ENABLE_LOGGING=true

# SSO子系统1配置
SSO_SYSTEM1_NAME=子系统1
SSO_SYSTEM1_DESC=第一个子系统
SSO_SYSTEM1_URL=http://your_system1_host:8081
SSO_SYSTEM1_ENABLED=true
SSO_SYSTEM1_TOKEN_EXPIRE=600
SSO_SYSTEM1_CALLBACK_URL=http://your_system1_host:8081/sso/callback

# SSO子系统2配置
SSO_SYSTEM2_NAME=子系统2
SSO_SYSTEM2_DESC=第二个子系统
SSO_SYSTEM2_URL=http://your_system2_host:8082
SSO_SYSTEM2_ENABLED=true
SSO_SYSTEM2_SECRET_KEY=your_custom_secret_key_here
SSO_SYSTEM2_CALLBACK_URL=http://your_system2_host:8082/sso/callback

# ===========================================
# 部署说明
# ===========================================
# 1. 复制此文件为 .env
# 2. 将所有 your_* 占位符替换为实际的生产环境配置值
# 3. 确保 .env 文件权限设置为 600 (仅所有者可读写)
# 4. 在 .gitignore 中添加 .env 文件，避免提交敏感信息
# 5. 在生产环境中通过环境变量或配置管理工具加载这些配置
# 6. 生成强密码和密钥，避免使用示例中的默认值
