package com.concise.common.util;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 重定向安全工具类
 * 用于防止未验证重定向漏洞 (CWE-601)
 * 
 * <AUTHOR>
 * @date 2024-08-04
 */
@Slf4j
public class RedirectSecurityUtil {

    /**
     * 默认允许的域名列表（可以通过配置文件配置）
     */
    private static final List<String> DEFAULT_ALLOWED_DOMAINS = Arrays.asList(
        "localhost",
        "127.0.0.1",
        "::1"
    );

    /**
     * 危险协议列表
     */
    private static final List<String> DANGEROUS_PROTOCOLS = Arrays.asList(
        "javascript:",
        "data:",
        "vbscript:",
        "file:",
        "ftp:"
    );

    /**
     * URL格式验证正则表达式
     */
    private static final Pattern URL_PATTERN = Pattern.compile(
        "^(https?://)?" +                           // 协议（可选）
        "([a-zA-Z0-9.-]+)" +                        // 域名
        "(:[0-9]+)?" +                              // 端口（可选）
        "(/[^\\s]*)?" +                             // 路径（可选）
        "$"
    );

    /**
     * 验证重定向URL是否安全
     * 
     * @param redirectUrl 要验证的重定向URL
     * @param allowedDomains 允许的域名列表
     * @return 是否安全
     */
    public static boolean isValidRedirectUrl(String redirectUrl, List<String> allowedDomains) {
        if (StrUtil.isBlank(redirectUrl)) {
            log.warn("重定向URL为空");
            return false;
        }

        try {
            // 1. 检查是否包含危险协议
            String lowerUrl = redirectUrl.toLowerCase().trim();
            for (String protocol : DANGEROUS_PROTOCOLS) {
                if (lowerUrl.startsWith(protocol)) {
                    log.warn("重定向URL包含危险协议: {}", protocol);
                    return false;
                }
            }

            // 2. 检查URL格式
            if (!URL_PATTERN.matcher(redirectUrl).matches()) {
                log.warn("重定向URL格式不正确: {}", redirectUrl);
                return false;
            }

            // 3. 解析URL并验证域名
            URL url;
            if (!redirectUrl.startsWith("http://") && !redirectUrl.startsWith("https://")) {
                // 如果没有协议，默认添加https://
                url = new URL("https://" + redirectUrl);
            } else {
                url = new URL(redirectUrl);
            }

            String host = url.getHost();
            if (StrUtil.isBlank(host)) {
                log.warn("重定向URL域名为空: {}", redirectUrl);
                return false;
            }

            // 4. 检查域名是否在允许列表中
            List<String> domainsToCheck = allowedDomains != null ? allowedDomains : DEFAULT_ALLOWED_DOMAINS;
            boolean isAllowed = domainsToCheck.stream()
                .anyMatch(domain -> host.equals(domain) || host.endsWith("." + domain));

            if (!isAllowed) {
                log.warn("重定向URL域名不在允许列表中: {}", host);
                return false;
            }

            // 5. 检查查询参数中是否包含重定向到其他域名的参数
            String query = url.getQuery();
            if (query != null) {
                // 检查常见的重定向参数
                String[] redirectParams = {"redirect", "redirect_uri", "return_url", "callback", "next", "url"};
                for (String param : redirectParams) {
                    if (query.toLowerCase().contains(param + "=")) {
                        // 如果包含重定向参数，需要进一步验证
                        String[] queryPairs = query.split("&");
                        for (String pair : queryPairs) {
                            if (pair.toLowerCase().startsWith(param + "=")) {
                                String value = pair.substring(pair.indexOf("=") + 1);
                                try {
                                    // URL解码
                                    value = java.net.URLDecoder.decode(value, "UTF-8");
                                    // 检查重定向参数值是否包含外部域名
                                    if (value.contains("://") && !isValidRedirectUrl(value, domainsToCheck)) {
                                        log.warn("重定向URL查询参数包含不安全的重定向: {}", value);
                                        return false;
                                    }
                                } catch (Exception e) {
                                    log.warn("解析重定向URL查询参数时发生异常: {}", pair, e);
                                    return false;
                                }
                            }
                        }
                    }
                }
            }

            // 6. 检查端口是否合理（避免内网端口扫描）
            int port = url.getPort();
            if (port != -1 && (port < 80 || port > 65535)) {
                log.warn("重定向URL端口不合理: {}", port);
                return false;
            }

            log.debug("重定向URL验证通过: {}", redirectUrl);
            return true;

        } catch (MalformedURLException e) {
            log.warn("重定向URL格式错误: {}", redirectUrl, e);
            return false;
        } catch (Exception e) {
            log.error("验证重定向URL时发生异常: {}", redirectUrl, e);
            return false;
        }
    }

    /**
     * 验证重定向URL是否安全（使用默认允许域名列表）
     * 
     * @param redirectUrl 要验证的重定向URL
     * @return 是否安全
     */
    public static boolean isValidRedirectUrl(String redirectUrl) {
        return isValidRedirectUrl(redirectUrl, null);
    }

    /**
     * 获取安全的重定向URL
     * 如果URL不安全，返回默认的安全URL
     * 
     * @param redirectUrl 原始重定向URL
     * @param allowedDomains 允许的域名列表
     * @param defaultUrl 默认安全URL
     * @return 安全的重定向URL
     */
    public static String getSafeRedirectUrl(String redirectUrl, List<String> allowedDomains, String defaultUrl) {
        if (isValidRedirectUrl(redirectUrl, allowedDomains)) {
            return redirectUrl;
        } else {
            log.warn("使用默认重定向URL替代不安全的URL: {} -> {}", redirectUrl, defaultUrl);
            return defaultUrl;
        }
    }

    /**
     * 获取安全的重定向URL（使用默认允许域名列表）
     * 
     * @param redirectUrl 原始重定向URL
     * @param defaultUrl 默认安全URL
     * @return 安全的重定向URL
     */
    public static String getSafeRedirectUrl(String redirectUrl, String defaultUrl) {
        return getSafeRedirectUrl(redirectUrl, null, defaultUrl);
    }

    /**
     * 检查URL是否为相对路径（相对路径通常是安全的）
     * 
     * @param url URL字符串
     * @return 是否为相对路径
     */
    public static boolean isRelativeUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return false;
        }
        
        String trimmedUrl = url.trim();
        // 相对路径不应该以协议开头
        return !trimmedUrl.contains("://") && 
               !trimmedUrl.startsWith("//") &&
               (trimmedUrl.startsWith("/") || trimmedUrl.startsWith("./") || trimmedUrl.startsWith("../"));
    }

    /**
     * 规范化URL，移除潜在的恶意字符
     * 
     * @param url 原始URL
     * @return 规范化后的URL
     */
    public static String normalizeUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return url;
        }

        // 移除潜在的恶意字符和编码
        return url.trim()
                .replaceAll("[\r\n\t]", "")           // 移除换行符和制表符
                .replaceAll("%0[aAdD]", "")           // 移除URL编码的换行符
                .replaceAll("%09", "")                // 移除URL编码的制表符
                .replaceAll("\\\\", "/");             // 统一路径分隔符
    }

    /**
     * 验证并规范化重定向URL
     * 
     * @param redirectUrl 原始重定向URL
     * @param allowedDomains 允许的域名列表
     * @param defaultUrl 默认安全URL
     * @return 安全且规范化的重定向URL
     */
    public static String validateAndNormalizeRedirectUrl(String redirectUrl, List<String> allowedDomains, String defaultUrl) {
        if (StrUtil.isBlank(redirectUrl)) {
            return defaultUrl;
        }

        // 先规范化URL
        String normalizedUrl = normalizeUrl(redirectUrl);
        
        // 如果是相对路径，直接返回（相对路径通常是安全的）
        if (isRelativeUrl(normalizedUrl)) {
            log.debug("重定向URL为相对路径，直接返回: {}", normalizedUrl);
            return normalizedUrl;
        }

        // 验证绝对URL的安全性
        return getSafeRedirectUrl(normalizedUrl, allowedDomains, defaultUrl);
    }

    /**
     * 从配置中获取允许的域名列表
     *
     * @param configDomains 配置的域名字符串（逗号分隔）
     * @return 域名列表
     */
    public static List<String> parseAllowedDomains(String configDomains) {
        if (StrUtil.isBlank(configDomains)) {
            return new java.util.ArrayList<>(DEFAULT_ALLOWED_DOMAINS);
        }

        // 创建可修改的列表
        List<String> domains = new java.util.ArrayList<>(Arrays.asList(configDomains.split(",")));
        domains.replaceAll(String::trim);
        domains.removeIf(StrUtil::isBlank);

        // 合并默认域名和配置域名
        domains.addAll(DEFAULT_ALLOWED_DOMAINS);

        return domains;
    }
}
