package com.concise.common.consts;

import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.annotation.Value;

import cn.hutool.core.util.ArrayUtil;

/**
 * SpringSecurity相关常量
 *
 * <AUTHOR>
 * @date 2020/3/18 17:49
 */
@Configurable
public class SpringSecurityConstant {

    @Value("${spring.profiles.active}")
    private String active;


    /**
     * 放开权限校验的接口
     */
    public static String[] NONE_SECURITY_URL_PATTERNS = {

            //前端的
            "/favicon.ico",

            //后端的
            "/login",
            "/checkToken",
            "/medicalRecord/updateInfo",
            "/logout",
            "/oauth/**",

            //文件的
            "/sysFileInfo/upload",
            "/sysFileInfo/download",
            "/sysFileInfo/preview",

            //druid的
            "/druid/**",

            "/screen/**",

            //获取验证码
            "/captcha/**",
            "/getCaptchaOpen",
            //浙政钉扫码
            "/loginQrCode",
            //浙政钉用户推送
            "/unifyUser/userReceive",
            "/unifyUser/testPushReceive",
            //单点登录
            "/rcsso/**",

    };

    {
        if (!"prod".equals(active)) {
            //swagger相关的
            SpringSecurityConstant.NONE_SECURITY_URL_PATTERNS = ArrayUtil.addAll(SpringSecurityConstant.NONE_SECURITY_URL_PATTERNS,
                    new String[]{
                            "/doc.html",
                            "/webjars/**",
                            "/swagger-resources/**",
                            "/v2/api-docs",
                            "/v2/api-docs-ext",
                            "/configuration/ui",
                            "/configuration/security"});

        }
    }

}
