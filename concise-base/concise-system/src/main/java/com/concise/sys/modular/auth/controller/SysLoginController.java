package com.concise.sys.modular.auth.controller;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.concise.common.consts.CommonConstant;
import com.concise.common.context.constant.ConstantContextHolder;
import com.concise.common.exception.AuthException;
import com.concise.common.exception.enums.AuthExceptionEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.common.util.ResponseUtil;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.core.login.SysLoginUser;
import com.concise.sys.core.jwt.JwtTokenUtil;
import com.concise.sys.modular.auth.service.AuthService;
import com.concise.sys.modular.user.entity.SysUser;
import com.concise.sys.modular.user.service.SysUserService;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 登录控制器
 *
 * <AUTHOR>
 * @date 2020/3/11 12:20
 */
@RestController
public class SysLoginController {

    @Resource
    private AuthService authService;
    @Resource
    private SysUserService sysUserService;

    @Lazy
    @Resource
    private CaptchaService captchaService;

    /**
     * 获取是否开启租户的标识
     *
     * <AUTHOR>
     * @date 2020/9/4
     */
    @GetMapping("/getTenantOpen")
    public ResponseData getTenantOpen() {
        return new SuccessResponseData(ConstantContextHolder.getTenantOpenFlag());
    }

    @PostMapping("/checkToken")
    public ResponseData checkToken(HttpServletRequest request) {
        String token = request.getHeader("token");
        Boolean tokenExpired = JwtTokenUtil.isTokenExpired(token);
        if (tokenExpired) {
            return ResponseData.error("登录失效");
        }
        return ResponseData.success();

    }

    /**
     * 账号密码登录
     *
     * <AUTHOR>
     * @date 2020/3/11 15:52
     */
    @PostMapping("/login")
    public ResponseData login(@RequestBody Dict dict) {
        String type = dict.getStr("type");
        String account = dict.getStr("account");
        String password = dict.getStr("password");
//        String tenantCode = dict.getStr("tenantCode");
        //检测是否开启验证码
//        if (ConstantContextHolder.getCaptchaOpenFlag()) {
//            verificationCode(dict.getStr("code"));
//        }

        //如果系统开启了多租户开关，则添加租户的临时缓存
//        if (ConstantContextHolder.getTenantOpenFlag()) {
//            authService.cacheTenantInfo(tenantCode);
//        }

        String token = authService.login(account, password, type);
        return new SuccessResponseData(token);
    }

    // 如燕随行单点登录
    @PostMapping("/sso/login")
    public ResponseData getRYLogin(@RequestBody SysUser sysUser) {
        SysUser sysUsers = sysUserService.getUserByCount(sysUser.getAccount());
        if(ObjectUtil.isEmpty(sysUsers)) {
            return ResponseData.error("用户不存在");
        }
        Map map = new HashMap();
        map.put("password",sysUsers.getPassword());
        map.put("username",sysUser.getAccount());
        return new SuccessResponseData(map);
    }

    /**
     * 退出登录
     *
     * <AUTHOR>
     * @date 2020/3/16 15:02
     */
    @GetMapping("/logout")
    public void logout() {
        authService.logout();
    }

    /**
     * 获取当前登录用户信息
     *
     * <AUTHOR>
     * @date 2020/3/23 17:57
     */
    @GetMapping("/getLoginUser")
    public ResponseData getLoginUser() {
        return new SuccessResponseData(LoginContextHolder.me().getSysLoginUserUpToDate());
    }

    /**
     * 获取验证码开关
     *
     * <AUTHOR>
     * @date 2021/1/21 15:27
     */
    @GetMapping("/getCaptchaOpen")
    public ResponseData getCaptchaOpen() {
        return new SuccessResponseData(ConstantContextHolder.getCaptchaOpenFlag());
    }

    /**
     * 校验验证码
     *
     * <AUTHOR>
     * @date 2021/1/21 15:27
     */
    private boolean verificationCode(String code) {
        CaptchaVO vo = new CaptchaVO();
        vo.setCaptchaVerification(code);
        if (!captchaService.verification(vo).isSuccess()) {
            throw new AuthException(AuthExceptionEnum.CONSTANT_EMPTY_ERROR);
        }
        return true;
    }

}
