<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.sys.modular.org.mapper.SysOrgMapper">

    <!-- 根据userId查询所拥有的部门 -->
    <select id="queryDepartsByUserId" parameterType="String" resultType="java.util.Map">
        select
        *
        from sys_org where id
        in(
            select org_id from sys_emp where id = #{userId}
        )
    </select>

</mapper>
