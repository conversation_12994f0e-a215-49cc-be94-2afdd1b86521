package com.concise.sys.modular.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.sys.modular.user.entity.SysUserDataScope;
import com.concise.sys.modular.user.param.SysUserParam;

import java.util.List;

/**
 * 系统用户数据范围service接口
 *
 * <AUTHOR>
 * @date 2020/3/13 15:45
 */
public interface SysUserDataScopeService extends IService<SysUserDataScope> {

    /**
     * 授权数据
     *
     * @param sysUserParam 授权参数
     * <AUTHOR>
     * @date 2020/3/28 16:57
     */
    void grantData(SysUserParam sysUserParam);

    /**
     * 获取用户的数据范围id集合
     *
     * @param uerId 用户id
     * @return 数据范围id集合
     * <AUTHOR>
     * @date 2020/4/5 17:27
     */
    List<String> getUserDataScopeIdList(String uerId);

    /**
     * 根据机构id集合删除对应的用户-数据范围关联信息
     *
     * @param orgIdList 机构id集合
     * <AUTHOR>
     * @date 2020/6/28 14:15
     */
    void deleteUserDataScopeListByOrgIdList(List<String> orgIdList);

    /**
     * 根据用户id删除对应的用户-数据范围关联信息
     *
     * @param userId 用户id
     * <AUTHOR>
     * @date 2020/6/28 14:52
     */
    void deleteUserDataScopeListByUserId(String userId);
}
