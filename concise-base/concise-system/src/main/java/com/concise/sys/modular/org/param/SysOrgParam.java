package com.concise.sys.modular.org.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 系统组织机构参数
 *
 * <AUTHOR>
 * @date 2020/3/26 10:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysOrgParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 父id
     */
    @NotNull(message = "pid不能为空，请检查pid参数", groups = {add.class, edit.class})
    private String pid;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空，请检查name参数", groups = {add.class, edit.class})
    private String name;

    /**
     * 编码
     */
    @NotBlank(message = "编码不能为空，请检查code参数", groups = {add.class, edit.class})
    private String code;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空，请检查sort参数", groups = {add.class, edit.class})
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    public SysOrgParam(@NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class}) String id, @NotNull(message = "pid不能为空，请检查pid参数", groups = {add.class, edit.class}) String pid, @NotBlank(message = "名称不能为空，请检查name参数", groups = {add.class, edit.class}) String name, @NotBlank(message = "编码不能为空，请检查code参数", groups = {add.class, edit.class}) String code, @NotNull(message = "排序不能为空，请检查sort参数", groups = {add.class, edit.class}) Integer sort, String remark) {
        this.id = id;
        this.pid = pid;
        this.name = name;
        this.code = code;
        this.sort = sort;
        this.remark = remark;
    }

    public SysOrgParam() {

    }
}
