package com.concise.sys.modular.org.controller;

import cn.hutool.core.util.ObjectUtil;
import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.DataScope;
import com.concise.common.annotion.Permission;
import com.concise.common.pojo.base.param.BaseParam;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.core.context.login.LoginContext;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.sys.modular.org.entity.SysOrg;
import com.concise.sys.modular.org.param.SysOrgParam;
import com.concise.sys.modular.org.service.SysOrgService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

/**
 * 系统组织机构控制器
 *
 * <AUTHOR>
 * @date 2020/3/20 19:47
 */
@RestController
public class SysOrgController {

    @Resource
    private SysOrgService sysOrgService;

    /**
     * 查询系统机构
     *
     * <AUTHOR>
     * @date 2020/5/11 15:49
     */
    @Permission
    @DataScope
    @GetMapping("/sysOrg/page")
    @BusinessLog(title = "系统机构_查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(SysOrgParam sysOrgParam) {
        return new SuccessResponseData(sysOrgService.page(sysOrgParam));
    }


    /**
     * 系统组织机构列表
     *
     * <AUTHOR>
     * @date 2020/3/26 10:20
     */
    @Permission
    @DataScope
    @GetMapping("/sysOrg/list")
    @BusinessLog(title = "系统组织机构_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(SysOrgParam sysOrgParam) {
        return new SuccessResponseData(sysOrgService.list(sysOrgParam));
    }

    /**
     * 添加系统组织机构
     *
     * <AUTHOR>
     * @date 2020/3/25 14:44
     */
    @Permission
    @DataScope
    @PostMapping("/sysOrg/add")
    @BusinessLog(title = "系统组织机构_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(BaseParam.add.class) SysOrgParam sysOrgParam) {
        sysOrgService.add(sysOrgParam);
        return new SuccessResponseData();
    }

    /**
     * 删除系统组织机构
     *
     * <AUTHOR>
     * @date 2020/3/25 14:54
     */
    @Permission
    @DataScope
    @PostMapping("/sysOrg/delete")
    @BusinessLog(title = "系统组织机构_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(BaseParam.delete.class) List<SysOrgParam> sysOrgParamList) {
        sysOrgService.delete(sysOrgParamList);
        return new SuccessResponseData();
    }

    /**
     * 编辑系统组织机构
     *
     * <AUTHOR>
     * @date 2020/3/25 14:54
     */
    @Permission
    @DataScope
    @PostMapping("/sysOrg/edit")
    @BusinessLog(title = "系统组织机构_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(BaseParam.edit.class) SysOrgParam sysOrgParam) {
        sysOrgService.edit(sysOrgParam);
        return new SuccessResponseData();
    }

    /**
     * 查看系统组织机构
     *
     * <AUTHOR>
     * @date 2020/3/26 9:49
     */
    @Permission
    @GetMapping("/sysOrg/detail")
    @BusinessLog(title = "系统组织机构_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(BaseParam.detail.class) SysOrgParam sysOrgParam) {
        return new SuccessResponseData(sysOrgService.detail(sysOrgParam));
    }

    /**
     * 获取组织机构树
     *
     * <AUTHOR>
     * @date 2020/3/26 11:55
     */
    @Permission
    @DataScope
    @GetMapping("/sysOrg/tree")
    @BusinessLog(title = "系统组织机构_树", opType = LogAnnotionOpTypeEnum.TREE)
    public ResponseData tree(SysOrgParam sysOrgParam) {
        return new SuccessResponseData(sysOrgService.tree(sysOrgParam));
    }

    /**
     * 获取下级组织机构
     *
     * <AUTHOR>
     * @date 2020/3/26 11:55
     */
    @Permission
    @DataScope
    @GetMapping("/sysOrg/child")
    public ResponseData childSysOrg(SysOrgParam sysOrgParam) {
        return new SuccessResponseData(sysOrgService.childSysOrg(sysOrgParam));
    }



    /**
     * 导出系统组织机构
     *
     * <AUTHOR>
     * @date 2021/5/30 12:48
     */
    @Permission
    @GetMapping("/sysOrg/export")
    @BusinessLog(title = "系统组织机构_导出", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void export(SysOrgParam sysOrgParam) {
        sysOrgService.export(sysOrgParam);
    }

    //后台数据展示权限
    public static Set<String> getOrgs(SysOrgService sysOrgService,String searchOrgId) {
        LoginContext login = LoginContextHolder.me();
        String orgId = "";
        if(ObjectUtil.isNotEmpty(searchOrgId)) {
            if(searchOrgId.equals("DEPT0000000000000000000000900000")) {
                if(login.isSuperAdmin()) {
                    orgId = searchOrgId;
                } else {
                    orgId = login.getSysLoginUserOrgId();
                }
            } else {
                orgId = searchOrgId;
            }
        } else {
            orgId = login.getSysLoginUserOrgId();
        }
        SysOrg sysOrg = sysOrgService.getById(orgId);
        if (null != sysOrg) {
            //判断登录人机构是否是部门，如果是部门则需要再往上找一级(参照信息化平台 type = sp 表示该组织机构是部门)
            if ("sp".equals(sysOrg.getType())){
                orgId = sysOrg.getPid();
            }
        }
        Set<String> orgs = null;
        if (ObjectUtil.isNotEmpty(orgId)) {
            orgs = new HashSet<>();
            // 查询本级&下级
            sysOrgService.getSubOrgIdById(orgId, orgs, sysOrgService.list());
            orgs.add(orgId);
        }
        return orgs;
    }
}
