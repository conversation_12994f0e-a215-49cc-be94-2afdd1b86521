package com.concise.sys.modular.log.controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.sys.modular.log.param.SysOpLogParam;
import com.concise.sys.modular.log.param.SysVisLogParam;
import com.concise.sys.modular.log.service.SysOpLogService;
import com.concise.sys.modular.log.service.SysVisLogService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

/**
 * 系统日志控制器
 *
 * <AUTHOR>
 * @date 2020/3/19 21:14
 */
@RestController
public class SysLogController {

    @Resource
    private SysVisLogService sysVisLogService;

    @Resource
    private SysOpLogService sysOpLogService;

    /**
     * 查询访问日志
     *
     * <AUTHOR>
     * @date 2020/3/20 18:52
     */
    @Permission
    @GetMapping("/sysVisLog/page")
    public ResponseData visLogPage(SysVisLogParam visLogParam) {
        return new SuccessResponseData(sysVisLogService.page(visLogParam));
    }

    /**
     * 查询操作日志
     *
     * <AUTHOR>
     * @date 2020/3/20 18:52
     */
    @Permission
    @GetMapping("/sysOpLog/page")
    public ResponseData opLogPage(SysOpLogParam sysOpLogParam) {
        return new SuccessResponseData(sysOpLogService.page(sysOpLogParam));
    }

    /**
     * 清空访问日志
     *
     * <AUTHOR>
     * @date 2020/3/23 16:28
     */
    @Permission
    @PostMapping("/sysVisLog/delete")
    @BusinessLog(title = "访问日志_清空", opType = LogAnnotionOpTypeEnum.CLEAN)
    public ResponseData visLogDelete() {
        sysVisLogService.delete();
        return new SuccessResponseData();
    }

    /**
     * 清空操作日志
     *
     * <AUTHOR>
     * @date 2020/3/23 16:28
     */
    @Permission
    @PostMapping("/sysOpLog/delete")
    @BusinessLog(title = "操作日志_清空", opType = LogAnnotionOpTypeEnum.CLEAN)
    public ResponseData opLogDelete() {
        sysOpLogService.delete();
        return new SuccessResponseData();
    }

    /**
     * 导出系统操作日志
     *
     * <AUTHOR>
     * @date 2020/5/30 17:55
     */
    @Permission
    @GetMapping("/sysOpLog/export")
    @BusinessLog(title = "操作日志_导出", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void export(SysOpLogParam sysOpLogParam) {
        sysOpLogService.export(sysOpLogParam);
    }

    /**
     * 导出系统访问日志
     *
     * <AUTHOR>
     * @date 2020/5/30 17:55
     */
    @Permission
    @GetMapping("/sysVisLog/export")
    @BusinessLog(title = "访问日志_导出", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void export(SysVisLogParam sysVisLogParam) {
        sysVisLogService.export(sysVisLogParam);
    }

}
