package com.concise.sys.modular.menu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.node.LoginMenuTreeNode;
import com.concise.sys.modular.menu.entity.SysMenu;
import com.concise.sys.modular.menu.node.MenuBaseTreeNode;
import com.concise.sys.modular.menu.param.SysMenuParam;

import java.util.List;

/**
 * 系统菜单service接口
 *
 * <AUTHOR>
 * @date 2020/3/13 16:05
 */
public interface SysMenuService extends IService<SysMenu> {

    /**
     * 获取用户权限相关信息
     *
     * @param userId 用户id
     * @return 权限集合
     * <AUTHOR>
     * @date 2020/3/13 16:26
     */
    List<String> getLoginPermissions(String userId);

    /**
     * 获取用户AntDesign菜单相关信息，前端使用
     *
     * @param userId  用户id
     * @param appCode 应用编码
     * @return AntDesign菜单信息结果集
     * <AUTHOR>
     * @date 2020/4/17 17:48
     */
    List<LoginMenuTreeNode> getLoginMenusAntDesign(String userId, String appCode);

    /**
     * 获取用户菜单所属的应用编码集合
     *
     * @param userId 用户id
     * @return 用户菜单所属的应用编码集合
     * <AUTHOR>
     * @date 2020/3/21 11:01
     */
    List<String> getUserMenuAppCodeList(String userId);

    /**
     * 系统菜单列表（树表）
     *
     * @param sysMenuParam 查询参数
     * @return 菜单树表列表
     * <AUTHOR>
     * @date 2020/3/26 10:19
     */
    List<SysMenu> list(SysMenuParam sysMenuParam);

    /**
     * 添加系统菜单
     *
     * @param sysMenuParam 添加参数
     * <AUTHOR>
     * @date 2020/3/27 9:03
     */
    void add(SysMenuParam sysMenuParam);

    /**
     * 删除系统菜单
     *
     * @param sysMenuParam 删除参数
     * <AUTHOR>
     * @date 2020/3/27 9:03
     */
    void delete(SysMenuParam sysMenuParam);

    /**
     * 编辑系统菜单
     *
     * @param sysMenuParam 编辑参数
     * <AUTHOR>
     * @date 2020/3/27 9:03
     */
    void edit(SysMenuParam sysMenuParam);

    /**
     * 查看系统菜单
     *
     * @param sysMenuParam 查看参数
     * @return 系统菜单
     * <AUTHOR>
     * @date 2020/3/27 9:03
     */
    SysMenu detail(SysMenuParam sysMenuParam);

    /**
     * 获取系统菜单树，用于新增，编辑时选择上级节点
     *
     * @param sysMenuParam 查询参数
     * @return 菜单树列表
     * <AUTHOR>
     * @date 2020/3/27 15:56
     */
    List<MenuBaseTreeNode> tree(SysMenuParam sysMenuParam);

    /**
     * 获取系统菜单树，用于给角色授权时选择
     *
     * @param sysMenuParam 查询参数
     * @return 菜单树列表
     * <AUTHOR>
     * @date 2020/4/5 15:01
     */
    List<MenuBaseTreeNode> treeForGrant(SysMenuParam sysMenuParam);

    /**
     * 根据应用编码判断该机构下是否有状态为正常的菜单
     *
     * @param appCode 应用编码
     * @return 该应用下是否有正常菜单，true是，false否
     * <AUTHOR>
     * @date 2020/6/28 12:14
     */
    boolean hasMenu(String appCode);
}
