package com.concise.sys.modular.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.sys.modular.log.entity.SysVisLog;
import com.concise.sys.modular.log.param.SysVisLogParam;

/**
 * 系统访问日志service接口
 *
 * <AUTHOR>
 * @date 2020/3/12 14:20
 */
public interface SysVisLogService extends IService<SysVisLog> {

    /**
     * 查询系统访问日志
     *
     * @param sysVisLogParam 查询参数
     * @return 查询结果集合
     * <AUTHOR>
     * @date 2020/3/24 20:55
     */
    PageResult<SysVisLog> page(SysVisLogParam sysVisLogParam);

    /**
     * 清空系统访问日志
     *
     * <AUTHOR>
     * @date 2020/6/1 11:04
     */
    void delete();

    /**
     * 导出系统访问日志
     *
     * <AUTHOR>
     * @date 2021/5/30 17:58
     */
    void export(SysVisLogParam sysVisLogParam);
}
