package com.concise.sys.modular.org.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.sys.modular.org.entity.SysOrg;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统组织机构mapper接口
 *
 * <AUTHOR>
 * @date 2020/3/13 16:03
 */
public interface SysOrgMapper extends BaseMapper<SysOrg> {
    List<SysOrg> queryDepartsByUserId(@Param(value = "userId") String userId);
}
