package com.concise.sys.modular.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.sys.modular.log.entity.SysOpLog;
import com.concise.sys.modular.log.param.SysOpLogParam;

/**
 * 系统操作日志service接口
 *
 * <AUTHOR>
 * @date 2020/3/12 14:21
 */
public interface SysOpLogService extends IService<SysOpLog> {

    /**
     * 查询系统操作日志
     *
     * @param sysOpLogParam 查询参数
     * @return 查询分页结果
     * <AUTHOR>
     * @date 2020/3/30 10:32
     */
    PageResult<SysOpLog> page(SysOpLogParam sysOpLogParam);

    /**
     * 清空系统操作日志
     *
     * <AUTHOR>
     * @date 2020/6/1 11:05
     */
    void delete();

    /**
     * 导出系统操作日志
     *
     * <AUTHOR>
     * @date 2021/5/30 17:58
     */
    void export(SysOpLogParam sysOpLogParam);
}
