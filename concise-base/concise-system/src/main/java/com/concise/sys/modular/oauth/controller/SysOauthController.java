package com.concise.sys.modular.oauth.controller;

import java.io.IOException;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import com.concise.common.consts.CommonConstant;
import com.concise.common.consts.SymbolConstant;
import com.concise.common.context.constant.ConstantContextHolder;
import com.concise.common.util.RedirectSecurityUtil;
import com.concise.sys.modular.oauth.service.SysOauthService;

import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthCallback;

/**
 * Oauth登录控制器
 *
 * <AUTHOR>
 * @date 2020/7/28 16:38
 **/
@RestController
@Slf4j
public class SysOauthController {

    @Resource
    private SysOauthService sysOauthService;

    /**
     * oauth登录
     *
     * <AUTHOR>
     * @date 2020/7/29 12:18
     **/
    @GetMapping("/oauth/render/{source}")
    public void renderAuth(@PathVariable("source") String source, HttpServletResponse response) throws IOException {
        String authorizeUrl = sysOauthService.getAuthorizeUrl(source);

        // 安全验证OAuth授权URL，防止重定向到恶意站点
        String safeAuthorizeUrl = validateOAuthAuthorizeUrl(authorizeUrl, source);

        log.info("OAuth授权重定向: source={}, authorizeUrl={}", source, safeAuthorizeUrl);
        response.sendRedirect(safeAuthorizeUrl);
    }

    /**
     * oauth平台中配置的授权回调地址
     *
     * <AUTHOR>
     * @date 2020/7/29 12:19
     **/
    @GetMapping("/oauth/callback/{source}")
    public void callback(@PathVariable("source") String source, AuthCallback callback, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String token = sysOauthService.callback(source, callback, request);
        String webUrl = ConstantContextHolder.getWebUrl();

        // 安全验证重定向URL，防止未验证重定向漏洞
        String redirectUrl = webUrl + SymbolConstant.QUESTION_MARK + CommonConstant.TOKEN_NAME + SymbolConstant.EQUAL + token;
        String safeRedirectUrl = validateAndGetSafeRedirectUrl(redirectUrl, "/");

        log.info("OAuth回调重定向: source={}, redirectUrl={}", source, safeRedirectUrl);
        response.sendRedirect(safeRedirectUrl);
    }

    /**
     * 验证OAuth授权URL的安全性
     *
     * @param authorizeUrl OAuth授权URL
     * @param source OAuth平台来源
     * @return 安全的授权URL
     */
    private String validateOAuthAuthorizeUrl(String authorizeUrl, String source) {
        try {
            // 定义允许的OAuth平台域名
            java.util.List<String> allowedOAuthDomains = java.util.Arrays.asList(
                "github.com",
                "gitee.com",
                "oauth.qq.com",
                "api.weibo.com",
                "graph.qq.com",
                "openapi.baidu.com",
                "api.weixin.qq.com",
                "open.weixin.qq.com"
            );

            // 验证URL安全性
            if (RedirectSecurityUtil.isValidRedirectUrl(authorizeUrl, allowedOAuthDomains)) {
                return authorizeUrl;
            } else {
                log.error("OAuth授权URL不安全，拒绝重定向: source={}, url={}", source, authorizeUrl);
                throw new RuntimeException("OAuth授权URL不安全: " + source);
            }
        } catch (Exception e) {
            log.error("验证OAuth授权URL时发生异常: source={}, url={}", source, authorizeUrl, e);
            throw new RuntimeException("OAuth授权URL验证失败: " + source, e);
        }
    }

    /**
     * 验证并获取安全的重定向URL
     *
     * @param redirectUrl 原始重定向URL
     * @param defaultUrl 默认安全URL
     * @return 安全的重定向URL
     */
    private String validateAndGetSafeRedirectUrl(String redirectUrl, String defaultUrl) {
        try {
            // 获取允许的域名列表（从配置中获取）
            String allowedDomainsConfig = ConstantContextHolder.getSysConfigWithDefault("SNOWY_OAUTH_ALLOWED_DOMAINS", String.class, "");
            java.util.List<String> allowedDomains = RedirectSecurityUtil.parseAllowedDomains(allowedDomainsConfig);

            // 验证并规范化重定向URL
            return RedirectSecurityUtil.validateAndNormalizeRedirectUrl(redirectUrl, allowedDomains, defaultUrl);
        } catch (Exception e) {
            log.error("验证重定向URL时发生异常，使用默认URL: {}", defaultUrl, e);
            return defaultUrl;
        }
    }
}
